{"109": {"inputs": {"text": "", "clip": ["198", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "117": {"inputs": {"images": ["120", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "118": {"inputs": {"strength": ["164", 1], "start_percent": 0, "end_percent": ["165", 0], "positive": ["178", 0], "negative": ["146", 0], "control_net": ["202", 0], "image": ["201", 0], "vae": ["189", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "119": {"inputs": {"start": ["165", 0], "end": 1, "conditioning": ["171", 0]}, "class_type": "ConditioningSetTimestepRange", "_meta": {"title": "ConditioningSetTimestepRange"}}, "120": {"inputs": {"preprocessor": "AnyLineArtPreprocessor_aux", "resolution": 1472, "image": ["148", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "121": {"inputs": {"type": "canny/lineart/anime_lineart/mlsd", "control_net": ["203", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "122": {"inputs": {"strength": 1.0000000000000002, "conditioning": ["145", 0]}, "class_type": "ConditioningSetAreaStrength", "_meta": {"title": "ConditioningSetAreaStrength"}}, "123": {"inputs": {"add_noise": true, "noise_seed": 890713221243326, "steps": 30, "cfg": 1, "sampler_name": "euler", "scheduler": "sgm_uniform", "start_at_step": 0, "end_at_step": 10000, "noise_mode": "GPU(=A1111)", "return_with_leftover_noise": false, "batch_seed_mode": "incremental", "variation_seed": 0, "variation_strength": 0, "variation_method": "linear", "internal_seed": 0, "model": ["147", 0], "positive": ["122", 0], "negative": ["146", 0], "latent_image": ["149", 0]}, "class_type": "KSamplerAdvanced //Inspire", "_meta": {"title": "KSamplerAdvanced (inspire)"}}, "124": {"inputs": {"upscale_model": ["208", 0], "image": ["191", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "125": {"inputs": {"pixels": ["128", 0], "vae": ["189", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "126": {"inputs": {"sharpen_radius": 1, "sigma": 0.4000000000000001, "alpha": 0.4000000000000001, "image": ["124", 0]}, "class_type": "ImageSharpen", "_meta": {"title": "Image Sharpen"}}, "127": {"inputs": {"coeff": 1.2000000000000002, "denoise": 0.3400000000000001}, "class_type": "GITSSchedulerFuncProvider", "_meta": {"title": "GITSScheduler Func Provider"}}, "128": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 3.500000000000001, "image": ["126", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "129": {"inputs": {"samples": ["130", 0], "vae": ["189", 0]}, "class_type": "VAEDecode", "_meta": {"title": "final_image_output"}}, "130": {"inputs": {"add_noise": true, "noise_seed": 199254252996922, "steps": 25, "cfg": 1, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "sgm_uniform", "start_at_step": 0, "end_at_step": 10000, "noise_mode": "GPU(=A1111)", "return_with_leftover_noise": false, "batch_seed_mode": "incremental", "variation_seed": 0, "variation_strength": 0, "variation_method": "linear", "internal_seed": 0, "model": ["198", 0], "positive": ["178", 0], "negative": ["146", 0], "latent_image": ["135", 0], "scheduler_func_opt": ["127", 0]}, "class_type": "KSamplerAdvanced //Inspire", "_meta": {"title": "KSamplerAdvanced (inspire)"}}, "135": {"inputs": {"noise_std": 0.30000000000000004, "samples": ["125", 0]}, "class_type": "Latent Noise Injection", "_meta": {"title": "Latent Noise Injection"}}, "137": {"inputs": {"expression": "a+b", "a": ["197", 0], "b": ["197", 1]}, "class_type": "MathExpression|pysssss", "_meta": {"title": "Math Expression 🐍"}}, "138": {"inputs": {"model_name": "4x-UltraSharp.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "139": {"inputs": {"comparison": ">=", "a": ["137", 0], "b": ["159", 0]}, "class_type": "SimpleComparison+", "_meta": {"title": "🔧 Simple Comparison"}}, "140": {"inputs": {"upscale_model": ["138", 0], "image": ["216", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "144": {"inputs": {"strength": 0.5500000000000002, "start_percent": ["165", 0], "end_percent": ["169", 0], "positive": ["212", 0], "negative": ["212", 1], "control_net": ["121", 0], "image": ["120", 0], "vae": ["189", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "145": {"inputs": {"conditioning_1": ["162", 0], "conditioning_2": ["119", 0]}, "class_type": "Conditioning<PERSON><PERSON><PERSON>", "_meta": {"title": "Conditioning (Combine)"}}, "146": {"inputs": {"conditioning": ["109", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "147": {"inputs": {"max_shift": 1.0000000000000002, "base_shift": 1.0000000000000002, "width": ["185", 0], "height": ["185", 1], "model": ["151", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "148": {"inputs": {"boolean": ["139", 0], "on_true": ["174", 0], "on_false": ["200", 0]}, "class_type": "easy ifElse", "_meta": {"title": "If else"}}, "149": {"inputs": {"width": ["185", 0], "height": ["185", 1], "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "151": {"inputs": {"model": ["198", 0]}, "class_type": "CFGZeroStar", "_meta": {"title": "CFGZeroStar"}}, "152": {"inputs": {"samples": ["123", 0], "vae": ["189", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "159": {"inputs": {"value": 3000}, "class_type": "easy int", "_meta": {"title": "Int"}}, "162": {"inputs": {"start": 0, "end": ["169", 0], "conditioning": ["172", 0]}, "class_type": "ConditioningSetTimestepRange", "_meta": {"title": "ConditioningSetTimestepRange"}}, "163": {"inputs": {"value": 0.7800000000000001}, "class_type": "easy float", "_meta": {"title": "CN-控制部分结束时机"}}, "164": {"inputs": {"expression": "a*b", "a": ["176", 0], "b": ["204", 0]}, "class_type": "MathExpression|pysssss", "_meta": {"title": "Math Expression 🐍"}}, "165": {"inputs": {"value": 0.5000000000000001}, "class_type": "easy float", "_meta": {"title": "CN-深度/线稿切换时机"}}, "166": {"inputs": {"expression": "a*b", "a": ["176", 0], "b": ["163", 0]}, "class_type": "MathExpression|pysssss", "_meta": {"title": "Math Expression 🐍"}}, "168": {"inputs": {"comparison": "<=", "a": ["166", 1], "b": ["165", 0]}, "class_type": "SimpleComparison+", "_meta": {"title": "🔧 Simple Comparison"}}, "169": {"inputs": {"boolean": ["168", 0], "on_true": ["165", 0], "on_false": ["166", 1]}, "class_type": "easy ifElse", "_meta": {"title": "If else"}}, "171": {"inputs": {"strength": 1.0000000000000002, "conditioning": ["178", 0]}, "class_type": "ConditioningSetAreaStrength", "_meta": {"title": "ConditioningSetAreaStrength"}}, "172": {"inputs": {"strength": 1.0000000000000002, "conditioning": ["144", 0]}, "class_type": "ConditioningSetAreaStrength", "_meta": {"title": "ConditioningSetAreaStrength"}}, "174": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": ["175", 0], "image": ["216", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "175": {"inputs": {"value": 2.0000000000000004}, "class_type": "easy float", "_meta": {"title": "首采分辨率大小设置（百万像素单位）"}}, "176": {"inputs": {"value": 1.0000000000000002}, "class_type": "easy float", "_meta": {"title": "CN全局控制强度"}}, "178": {"inputs": {"guidance": 3.5, "conditioning": ["179", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "179": {"inputs": {"text": ["211", 0], "clip": ["198", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "185": {"inputs": {"image": ["148", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "187": {"inputs": {"anything": ["129", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "189": {"inputs": {"vae_name": "Flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "190": {"inputs": {"anything": ["191", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "191": {"inputs": {"brightness": 0, "contrast": 1.1500000000000001, "saturation": 1.1700000000000002, "sharpness": 1.0000000000000002, "blur": 0, "gaussian_blur": 0, "edge_enhance": 0, "detail_enhance": "false", "image": ["152", 0]}, "class_type": "Image Filter Adjustments", "_meta": {"title": "Image Filter Adjustments"}}, "192": {"inputs": {"images": ["191", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "197": {"inputs": {"image": ["216", 0]}, "class_type": "easy imageSize", "_meta": {"title": "ImageSize"}}, "198": {"inputs": {"PowerLoraLoaderHeaderWidget": {"type": "PowerLoraLoaderHeaderWidget"}, "➕ Add Lora": "", "model": ["206", 0], "clip": ["207", 0]}, "class_type": "<PERSON> Lora <PERSON> (rgthree)", "_meta": {"title": "<PERSON> Lora <PERSON> (rgthree)"}}, "200": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": ["175", 0], "image": ["140", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "201": {"inputs": {"preprocessor": "DepthAnythingV2Preprocessor", "resolution": 1024, "image": ["148", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "202": {"inputs": {"type": "depth", "control_net": ["205", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "203": {"inputs": {"control_net_name": "FLUX.1-dev-ControlNet-Union-Pro-2.0.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "204": {"inputs": {"value": 0.6500000000000001}, "class_type": "easy float", "_meta": {"title": "CN-1深度控制强度"}}, "205": {"inputs": {"control_net_name": "FLUX.1-dev-ControlNet-Union-Pro-2.0.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "206": {"inputs": {"unet_name": "flux1_dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "207": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "208": {"inputs": {"model_name": "8x_NMKD-Typescale_175k.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "211": {"inputs": {"prompt": ""}, "class_type": "CR Prompt Text", "_meta": {"title": "prompt_input_01"}}, "212": {"inputs": {"strength": 0.4200000000000001, "start_percent": 0, "end_percent": 0.4000000000000001, "positive": ["118", 0], "negative": ["118", 1], "control_net": ["121", 0], "image": ["120", 0], "vae": ["189", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "213": {"inputs": {"images": ["129", 0], "filename_prefix": "langbot_temp"}, "class_type": "SaveImage", "_meta": {"title": "final_image_save"}}, "216": {"inputs": {"base64_data": "", "image_output": "<PERSON>de", "save_prefix": "ComfyUI"}, "class_type": "easy loadImageBase64", "_meta": {"title": "controlnet_image_input"}}, "217": {"inputs": {"value": 1059630169839465, "mode": "randomize", "action": "randomize", "last_seed": 67977614402230}, "class_type": "easy globalSeed", "_meta": {"title": "EasyGlobalSeed"}}}