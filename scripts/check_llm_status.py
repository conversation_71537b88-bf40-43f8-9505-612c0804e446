#!/usr/bin/env python3
"""
[二次开发] LLM状态检查脚本
检查langbot的LLM配置状态，诊断ComfyUI工作流中LLM调用失败的原因

开发说明：
- 此脚本为二次开发代码，不属于langbot原生代码
- 功能：诊断LLM配置问题，提供解决建议
- 维护者：开发团队
- 最后更新：2025-01-09
- 相关任务：LLM配置诊断
- 依赖关系：需要langbot容器运行
"""

import json
import sqlite3
import yaml
import os
import sys
from pathlib import Path

class LLMStatusChecker:
    def __init__(self):
        self.db_path = "data/langbot.db"
        self.config_path = "config/llm_config.yaml"
        self.pipeline_config_path = "templates/default-pipeline-config.json"
        
    def check_files_exist(self):
        """检查必要文件是否存在"""
        print("📁 检查文件存在性:")
        
        files_to_check = [
            (self.db_path, "LangBot数据库"),
            (self.config_path, "LLM配置文件"),
            (self.pipeline_config_path, "Pipeline模板配置")
        ]
        
        all_exist = True
        for file_path, description in files_to_check:
            if os.path.exists(file_path):
                print(f"  ✅ {description}: {file_path}")
            else:
                print(f"  ❌ {description}: {file_path} (不存在)")
                all_exist = False
                
        return all_exist
    
    def check_llm_config_file(self):
        """检查LLM配置文件"""
        print("\n🔧 检查LLM配置文件:")
        
        if not os.path.exists(self.config_path):
            print(f"  ❌ 配置文件不存在: {self.config_path}")
            return False
            
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                
            print(f"  ✅ 配置文件加载成功")
            
            # 检查DeepSeek配置
            if 'deepseek' in config:
                deepseek = config['deepseek']
                print(f"  📋 DeepSeek配置:")
                print(f"    - API Key: {deepseek.get('api_key', 'N/A')[:20]}...")
                print(f"    - Endpoint: {deepseek.get('endpoint', 'N/A')}")
                print(f"    - Model: {deepseek.get('model', 'N/A')}")
            else:
                print(f"  ❌ 未找到DeepSeek配置")
                
            return True
            
        except Exception as e:
            print(f"  ❌ 配置文件解析失败: {e}")
            return False
    
    def check_database_models(self):
        """检查数据库中的模型"""
        print("\n💾 检查数据库中的LLM模型:")
        
        if not os.path.exists(self.db_path):
            print(f"  ❌ 数据库文件不存在: {self.db_path}")
            print("  💡 建议: 确保langbot容器正在运行")
            return False, []
            
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='llm_models'")
            if not cursor.fetchone():
                print("  ❌ llm_models表不存在")
                conn.close()
                return False, []
            
            # 获取所有模型
            cursor.execute("SELECT uuid, name, requester, api_keys FROM llm_models")
            models = cursor.fetchall()
            
            if models:
                print(f"  ✅ 找到 {len(models)} 个LLM模型:")
                for model in models:
                    uuid_short = model[0][:8] + "..."
                    api_keys = json.loads(model[3]) if model[3] else []
                    api_key_info = f"{len(api_keys)} keys" if api_keys else "无API Key"
                    print(f"    - {model[1]} ({model[2]}) - {uuid_short} - {api_key_info}")
            else:
                print("  ❌ 数据库中没有LLM模型")
                
            conn.close()
            return True, models
            
        except Exception as e:
            print(f"  ❌ 数据库查询失败: {e}")
            return False, []
    
    def check_pipeline_configuration(self, models):
        """检查Pipeline配置"""
        print("\n⚙️ 检查Pipeline配置:")
        
        if not os.path.exists(self.db_path):
            return False
            
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取默认流水线
            cursor.execute("SELECT uuid, name, config FROM legacy_pipelines WHERE is_default = 1")
            pipeline = cursor.fetchone()
            
            if not pipeline:
                print("  ❌ 未找到默认流水线")
                conn.close()
                return False
                
            pipeline_uuid, pipeline_name, config_str = pipeline
            config = json.loads(config_str)
            
            print(f"  ✅ 默认流水线: {pipeline_name} ({pipeline_uuid[:8]}...)")
            
            # 检查AI配置
            ai_config = config.get('ai', {})
            runner = ai_config.get('runner', {}).get('runner', 'N/A')
            print(f"  📋 AI Runner: {runner}")
            
            # 检查local-agent配置
            local_agent = ai_config.get('local-agent', {})
            model_uuid = local_agent.get('model', '')
            
            if model_uuid:
                print(f"  ✅ 配置的模型UUID: {model_uuid[:8]}...")
                
                # 检查模型是否存在
                model_exists = any(model[0] == model_uuid for model in models)
                if model_exists:
                    model_name = next(model[1] for model in models if model[0] == model_uuid)
                    print(f"  ✅ 模型存在: {model_name}")
                    conn.close()
                    return True
                else:
                    print(f"  ❌ 模型不存在于数据库中")
            else:
                print(f"  ❌ 未配置模型UUID (字段为空)")
                
            conn.close()
            return False
            
        except Exception as e:
            print(f"  ❌ Pipeline配置检查失败: {e}")
            return False
    
    def provide_solutions(self, db_ok, models, pipeline_ok):
        """提供解决方案"""
        print("\n🔧 解决方案建议:")
        
        if not db_ok:
            print("  1. 确保langbot容器正在运行:")
            print("     docker ps | grep langbot")
            print("     docker start langbot")
            return
            
        if not models:
            print("  1. 数据库中没有LLM模型，需要添加模型:")
            print("     - 方案A: 使用Web界面添加 (http://localhost:5300)")
            print("     - 方案B: 运行修复脚本 (python scripts/fix_llm_config.py)")
            return
            
        if not pipeline_ok:
            print("  1. Pipeline配置问题，需要关联LLM模型:")
            print("     - 方案A: 使用Web界面配置流水线 (http://localhost:5300)")
            print("     - 方案B: 运行修复脚本 (python scripts/fix_llm_config.py)")
            print("  2. 可用的模型:")
            for model in models:
                print(f"     - {model[1]} (UUID: {model[0]})")
            return
            
        print("  ✅ 配置看起来正常！")
        print("  如果仍有问题，请检查:")
        print("  1. 重启langbot容器: docker restart langbot")
        print("  2. 查看日志: docker logs langbot --tail 50")
        print("  3. 测试LLM: 发送 'aigen 测试' 到微信")
    
    def run(self):
        """运行检查"""
        print("🔍 开始检查LLM配置状态...")
        
        # 1. 检查文件
        if not self.check_files_exist():
            return False
            
        # 2. 检查配置文件
        config_ok = self.check_llm_config_file()
        
        # 3. 检查数据库模型
        db_ok, models = self.check_database_models()
        
        # 4. 检查Pipeline配置
        pipeline_ok = self.check_pipeline_configuration(models)
        
        # 5. 提供解决方案
        self.provide_solutions(db_ok, models, pipeline_ok)
        
        # 6. 总结
        print(f"\n📊 检查结果总结:")
        print(f"  - 配置文件: {'✅' if config_ok else '❌'}")
        print(f"  - 数据库模型: {'✅' if models else '❌'}")
        print(f"  - Pipeline配置: {'✅' if pipeline_ok else '❌'}")
        
        return config_ok and models and pipeline_ok

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 LangBot LLM状态检查工具")
    print("=" * 60)
    
    checker = LLMStatusChecker()
    success = checker.run()
    
    if success:
        print("\n✅ 所有检查通过！")
    else:
        print("\n⚠️ 发现配置问题，请参考上述建议进行修复")

if __name__ == "__main__":
    main()
