#!/usr/bin/env python3
"""
Kontext工作流诊断脚本
用于在Linux服务器上诊断ComfyUI和工作流问题

使用方法:
python scripts/diagnose_kontext_issue.py
"""

import asyncio
import aiohttp
import json
import os
import sys
import time
from pathlib import Path

class KontextDiagnostic:
    def __init__(self, api_url="http://localhost:8188"):
        self.api_url = api_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30))
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def print_section(self, title):
        """打印分节标题"""
        print(f"\n{'='*60}")
        print(f" {title}")
        print(f"{'='*60}")
    
    async def check_comfyui_service(self):
        """检查ComfyUI服务状态"""
        self.print_section("1. ComfyUI服务检查")
        
        try:
            print(f"🔍 检查ComfyUI服务: {self.api_url}")
            async with self.session.get(f"{self.api_url}/") as response:
                if response.status == 200:
                    print("✅ ComfyUI服务正常运行")
                    return True
                else:
                    print(f"❌ ComfyUI服务响应异常: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ ComfyUI服务连接失败: {e}")
            return False
    
    async def check_system_resources(self):
        """检查系统资源"""
        self.print_section("2. 系统资源检查")
        
        try:
            async with self.session.get(f"{self.api_url}/system_stats") as response:
                if response.status == 200:
                    stats = await response.json()
                    print("📊 系统状态:")
                    for key, value in stats.items():
                        print(f"  {key}: {value}")
                    return True
                else:
                    print(f"❌ 无法获取系统状态: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 系统状态检查失败: {e}")
            return False
    
    async def check_queue_status(self):
        """检查队列状态"""
        self.print_section("3. 队列状态检查")
        
        try:
            async with self.session.get(f"{self.api_url}/queue") as response:
                if response.status == 200:
                    queue = await response.json()
                    running = queue.get('queue_running', [])
                    pending = queue.get('queue_pending', [])
                    
                    print(f"🔄 队列状态:")
                    print(f"  运行中任务: {len(running)}")
                    print(f"  等待中任务: {len(pending)}")
                    
                    if running:
                        print("  当前运行任务:")
                        for i, task in enumerate(running[:3]):  # 只显示前3个
                            print(f"    {i+1}. {task}")
                    
                    return True
                else:
                    print(f"❌ 无法获取队列状态: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 队列状态检查失败: {e}")
            return False
    
    def check_workflow_files(self):
        """检查工作流文件"""
        self.print_section("4. 工作流文件检查")
        
        workflow_files = [
            "workflows/kontext_local_1image.json",
            "workflows/kontext_local_2images.json",
            "workflows/kontext_local_3images.json"
        ]
        
        all_exist = True
        for workflow_file in workflow_files:
            if os.path.exists(workflow_file):
                print(f"✅ {workflow_file} - 存在")
                
                # 检查文件内容
                try:
                    with open(workflow_file, 'r', encoding='utf-8') as f:
                        workflow_data = json.load(f)
                    
                    # 统计关键节点
                    image_inputs = 0
                    prompt_nodes = 0
                    output_nodes = 0
                    
                    for node_id, node_data in workflow_data.items():
                        class_type = node_data.get('class_type', '')
                        title = node_data.get('_meta', {}).get('title', '').lower()
                        
                        if 'image_input' in title:
                            image_inputs += 1
                        elif class_type == 'CLIPTextEncode' or 'prompt' in title:
                            prompt_nodes += 1
                        elif class_type in ['VAEDecode', 'PreviewImage']:
                            output_nodes += 1
                    
                    print(f"    节点总数: {len(workflow_data)}")
                    print(f"    图片输入: {image_inputs}, 提示词: {prompt_nodes}, 输出: {output_nodes}")
                    
                except Exception as e:
                    print(f"    ⚠️ 文件解析失败: {e}")
                    all_exist = False
            else:
                print(f"❌ {workflow_file} - 不存在")
                all_exist = False
        
        return all_exist
    
    async def test_simple_workflow(self):
        """测试简单工作流提交"""
        self.print_section("5. 工作流提交测试")
        
        try:
            # 尝试加载kontext_local_1image.json进行测试
            workflow_file = "workflows/kontext_local_1image.json"
            if not os.path.exists(workflow_file):
                print(f"❌ 测试工作流文件不存在: {workflow_file}")
                return False
            
            with open(workflow_file, 'r', encoding='utf-8') as f:
                workflow_data = json.load(f)
            
            print(f"🧪 提交测试工作流: {workflow_file}")
            
            async with self.session.post(
                f"{self.api_url}/prompt",
                json={"prompt": workflow_data},
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    prompt_id = result.get('prompt_id')
                    print(f"✅ 工作流提交成功: {prompt_id}")
                    
                    # 等待几秒钟检查状态
                    await asyncio.sleep(3)
                    
                    async with self.session.get(f"{self.api_url}/history/{prompt_id}") as hist_response:
                        if hist_response.status == 200:
                            history = await hist_response.json()
                            if prompt_id in history:
                                status = history[prompt_id].get('status', {}).get('status_str', 'unknown')
                                print(f"📊 工作流状态: {status}")
                            else:
                                print("⏳ 工作流尚未出现在历史记录中")
                    
                    return True
                else:
                    print(f"❌ 工作流提交失败: {response.status}")
                    response_text = await response.text()
                    print(f"响应: {response_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ 工作流测试失败: {e}")
            return False
    
    def check_environment(self):
        """检查环境配置"""
        self.print_section("6. 环境配置检查")
        
        # 检查当前目录
        current_dir = os.getcwd()
        print(f"📁 当前目录: {current_dir}")
        
        # 检查关键目录
        dirs_to_check = ['workflows', 'temp', 'logs']
        for dir_name in dirs_to_check:
            if os.path.exists(dir_name):
                print(f"✅ {dir_name}/ - 存在")
            else:
                print(f"❌ {dir_name}/ - 不存在")
        
        # 检查Python环境
        print(f"🐍 Python版本: {sys.version}")
        
        # 检查关键模块
        modules_to_check = ['aiohttp', 'asyncio']
        for module in modules_to_check:
            try:
                __import__(module)
                print(f"✅ {module} - 已安装")
            except ImportError:
                print(f"❌ {module} - 未安装")

async def main():
    """主诊断函数"""
    print("🔍 Kontext工作流诊断开始...")
    print(f"⏰ 诊断时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    diagnostic = KontextDiagnostic()
    
    # 环境检查（不需要网络）
    diagnostic.check_environment()
    
    # 工作流文件检查
    workflow_files_ok = diagnostic.check_workflow_files()
    
    # ComfyUI服务检查
    async with diagnostic:
        service_ok = await diagnostic.check_comfyui_service()
        
        if service_ok:
            await diagnostic.check_system_resources()
            await diagnostic.check_queue_status()
            await diagnostic.test_simple_workflow()
        else:
            print("\n❌ ComfyUI服务未运行，跳过后续检查")
            print("\n🔧 建议:")
            print("1. 启动ComfyUI服务: cd /path/to/ComfyUI && python main.py --listen 0.0.0.0 --port 8188")
            print("2. 检查防火墙设置")
            print("3. 确认ComfyUI安装是否正确")
    
    # 总结
    print(f"\n{'='*60}")
    print(" 诊断总结")
    print(f"{'='*60}")
    
    if workflow_files_ok and service_ok:
        print("✅ 基础环境检查通过，可以尝试运行kontext工作流")
    else:
        print("❌ 发现问题，请根据上述建议进行修复")
    
    print(f"\n🔍 诊断完成: {time.strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    asyncio.run(main())
