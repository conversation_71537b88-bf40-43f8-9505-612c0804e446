#!/bin/bash

# 清理ComfyUI临时文件脚本
# 用于清理output目录下的langbot_temp文件

COMFYUI_OUTPUT_DIR="/home/<USER>/Workspace/ComfyUI/output"

echo "🗑️ 开始清理ComfyUI临时文件..."

if [ ! -d "$COMFYUI_OUTPUT_DIR" ]; then
    echo "❌ ComfyUI output目录不存在: $COMFYUI_OUTPUT_DIR"
    exit 1
fi

# 统计临时文件数量
TEMP_FILES_COUNT=$(ls -1 "$COMFYUI_OUTPUT_DIR"/langbot_temp_*.png 2>/dev/null | wc -l)

if [ "$TEMP_FILES_COUNT" -eq 0 ]; then
    echo "✅ 没有找到需要清理的临时文件"
    exit 0
fi

echo "📊 找到 $TEMP_FILES_COUNT 个临时文件需要清理"

# 列出要删除的文件
echo "📋 临时文件列表:"
ls -la "$COMFYUI_OUTPUT_DIR"/langbot_temp_*.png

# 确认删除
read -p "❓ 确认删除这些文件吗? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 取消删除操作"
    exit 0
fi

# 执行删除
echo "🗑️ 正在删除临时文件..."
DELETED_COUNT=0
for file in "$COMFYUI_OUTPUT_DIR"/langbot_temp_*.png; do
    if [ -f "$file" ]; then
        rm "$file"
        if [ $? -eq 0 ]; then
            echo "✅ 已删除: $(basename "$file")"
            ((DELETED_COUNT++))
        else
            echo "❌ 删除失败: $(basename "$file")"
        fi
    fi
done

echo "🎉 清理完成! 共删除 $DELETED_COUNT 个文件"

# 显示清理后的状态
REMAINING_COUNT=$(ls -1 "$COMFYUI_OUTPUT_DIR"/langbot_temp_*.png 2>/dev/null | wc -l)
echo "📊 剩余临时文件: $REMAINING_COUNT 个"
