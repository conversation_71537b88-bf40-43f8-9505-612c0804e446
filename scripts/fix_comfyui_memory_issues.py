#!/usr/bin/env python3
"""
ComfyUI内存泄漏和工作流验证问题修复脚本
解决Flux模型内存泄漏和工作流验证失败的问题
"""

import os
import sys
import json
import gc
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from pkg.workers.shared.shared_lora_manager import SharedLoraManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ComfyUIMemoryFixer:
    """ComfyUI内存问题修复器"""
    
    def __init__(self):
        self.lora_manager = SharedLoraManager()
        self.comfyui_config_path = Path("config/comfyui_config.yaml")
        self.workflow_cache_path = Path("temp/workflow_cache")
        
    def fix_memory_leaks(self):
        """修复内存泄漏问题"""
        logger.info("开始修复ComfyUI内存泄漏问题...")
        
        try:
            # 1. 清理工作流缓存
            self._cleanup_workflow_cache()
            
            # 2. 验证LoRA文件完整性
            self._validate_lora_files()
            
            # 3. 优化ComfyUI配置
            self._optimize_comfyui_config()
            
            # 4. 强制垃圾回收
            self._force_garbage_collection()
            
            logger.info("✅ 内存泄漏修复完成")
            
        except Exception as e:
            logger.error(f"❌ 内存泄漏修复失败: {e}")
    
    def _cleanup_workflow_cache(self):
        """清理工作流缓存"""
        logger.info("清理工作流缓存...")
        
        if self.workflow_cache_path.exists():
            try:
                import shutil
                shutil.rmtree(self.workflow_cache_path)
                logger.info(f"已删除缓存目录: {self.workflow_cache_path}")
            except Exception as e:
                logger.warning(f"清理缓存失败: {e}")
        
        # 创建新的缓存目录
        self.workflow_cache_path.mkdir(parents=True, exist_ok=True)
        logger.info("已重新创建缓存目录")
    
    def _validate_lora_files(self):
        """验证LoRA文件完整性"""
        logger.info("验证LoRA文件完整性...")
        
        invalid_files = []
        valid_files = 0
        
        for model_name, model in self.lora_manager.lora_models.items():
            if not model.is_active:
                continue
                
            file_path = model.file_path
            if not file_path or not os.path.exists(file_path):
                invalid_files.append(model_name)
                logger.warning(f"LoRA文件不存在: {model_name} -> {file_path}")
                continue
            
            # 检查文件大小
            try:
                file_size = os.path.getsize(file_path)
                if file_size < 1024:  # 小于1KB的文件可能损坏
                    invalid_files.append(model_name)
                    logger.warning(f"LoRA文件可能损坏: {model_name} (大小: {file_size} bytes)")
                    continue
                    
                valid_files += 1
                logger.debug(f"LoRA文件正常: {model_name} (大小: {file_size} bytes)")
                
            except Exception as e:
                invalid_files.append(model_name)
                logger.error(f"检查LoRA文件失败: {model_name} -> {e}")
        
        logger.info(f"LoRA文件验证完成: {valid_files}个有效, {len(invalid_files)}个无效")
        
        if invalid_files:
            logger.warning(f"发现无效LoRA文件: {invalid_files}")
    
    def _optimize_comfyui_config(self):
        """优化ComfyUI配置"""
        logger.info("优化ComfyUI配置...")
        
        config = {
            "memory_management": {
                "enable_memory_cleanup": True,
                "cleanup_interval": 5,  # 每5次生成后清理
                "max_workflow_cache_size": 100,  # 最大缓存100个工作流
                "force_garbage_collection": True
            },
            "lora_settings": {
                "max_lora_count": 3,  # 限制最大LoRA数量
                "weight_precision": 3,  # 权重精度
                "validate_lora_files": True,
                "skip_invalid_loras": True
            },
            "workflow_validation": {
                "strict_mode": False,  # 非严格模式，允许部分错误
                "skip_invalid_nodes": True,
                "log_validation_errors": True
            },
            "pytorch_settings": {
                "weights_only": True,  # 解决PyTorch安全警告
                "map_location": "cpu"  # 避免GPU内存问题
            }
        }
        
        # 保存配置
        config_path = Path("config/comfyui_optimized_config.yaml")
        try:
            import yaml
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"已保存优化配置: {config_path}")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    def _force_garbage_collection(self):
        """强制垃圾回收"""
        logger.info("执行强制垃圾回收...")
        
        # 多次垃圾回收确保清理
        for i in range(3):
            collected = gc.collect()
            logger.info(f"第{i+1}次垃圾回收: 清理了{collected}个对象")
        
        # 清理弱引用
        gc.collect(2)  # 清理第2代对象
        logger.info("垃圾回收完成")
    
    def create_workflow_validator(self):
        """创建工作流验证器"""
        logger.info("创建工作流验证器...")
        
        validator_code = '''
import json
import logging
from typing import Dict, Any, List, Optional

class WorkflowValidator:
    """工作流验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.required_nodes = ["UNETLoader", "FluxSamplerParams+"]
        self.optional_nodes = ["LoraLoader", "Power Lora Loader (rgthree)"]
    
    def validate_workflow(self, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证工作流"""
        result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "fixed_nodes": []
        }
        
        try:
            # 检查必需节点
            for node_type in self.required_nodes:
                if not self._has_node_type(workflow_data, node_type):
                    result["errors"].append(f"缺少必需节点: {node_type}")
                    result["valid"] = False
            
            # 验证LoRA节点
            lora_errors = self._validate_lora_nodes(workflow_data)
            result["errors"].extend(lora_errors)
            
            # 检查连接完整性
            connection_errors = self._validate_connections(workflow_data)
            result["errors"].extend(connection_errors)
            
            if result["errors"]:
                result["valid"] = False
            
            return result
            
        except Exception as e:
            result["valid"] = False
            result["errors"].append(f"验证过程出错: {e}")
            return result
    
    def _has_node_type(self, workflow_data: Dict[str, Any], node_type: str) -> bool:
        """检查是否包含指定类型的节点"""
        for node_data in workflow_data.values():
            if isinstance(node_data, dict) and node_data.get("class_type") == node_type:
                return True
        return False
    
    def _validate_lora_nodes(self, workflow_data: Dict[str, Any]) -> List[str]:
        """验证LoRA节点"""
        errors = []
        
        for node_id, node_data in workflow_data.items():
            if not isinstance(node_data, dict):
                continue
                
            class_type = node_data.get("class_type", "")
            if "LoraLoader" in class_type or "Power Lora Loader" in class_type:
                inputs = node_data.get("inputs", {})
                
                # 检查必需输入
                required_inputs = ["model", "clip", "lora_name"]
                for input_name in required_inputs:
                    if input_name not in inputs:
                        errors.append(f"节点{node_id}缺少必需输入: {input_name}")
                
                # 检查权重范围
                for weight_type in ["strength_model", "strength_clip"]:
                    if weight_type in inputs:
                        weight = inputs[weight_type]
                        if not isinstance(weight, (int, float)) or not (0.1 <= weight <= 1.0):
                            errors.append(f"节点{node_id}权重无效: {weight_type}={weight}")
        
        return errors
    
    def _validate_connections(self, workflow_data: Dict[str, Any]) -> List[str]:
        """验证节点连接"""
        errors = []
        
        for node_id, node_data in workflow_data.items():
            if not isinstance(node_data, dict):
                continue
                
            inputs = node_data.get("inputs", {})
            for input_name, input_value in inputs.items():
                if isinstance(input_value, list) and len(input_value) >= 2:
                    target_node = input_value[0]
                    if target_node not in workflow_data:
                        errors.append(f"节点{node_id}连接到不存在的节点: {target_node}")
        
        return errors

# 全局验证器实例
workflow_validator = WorkflowValidator()
'''
        
        # 保存验证器
        validator_path = Path("pkg/workers/flux/workflow_validator.py")
        try:
            with open(validator_path, 'w', encoding='utf-8') as f:
                f.write(validator_code)
            logger.info(f"已创建工作流验证器: {validator_path}")
        except Exception as e:
            logger.error(f"创建工作流验证器失败: {e}")
    
    def generate_fix_report(self):
        """生成修复报告"""
        logger.info("生成修复报告...")
        
        report = {
            "timestamp": str(Path().cwd()),
            "fixes_applied": [
                "工作流缓存清理",
                "LoRA文件完整性验证", 
                "ComfyUI配置优化",
                "强制垃圾回收",
                "工作流验证器创建"
            ],
            "recommendations": [
                "定期重启ComfyUI服务以释放内存",
                "监控LoRA文件完整性",
                "使用工作流验证器检查工作流",
                "限制同时使用的LoRA数量",
                "启用PyTorch weights_only模式"
            ],
            "next_steps": [
                "重启ComfyUI服务",
                "测试工作流生成",
                "监控内存使用情况",
                "检查日志中的错误信息"
            ]
        }
        
        # 保存报告
        report_path = Path("temp/memory_fix_report.json")
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            logger.info(f"已生成修复报告: {report_path}")
        except Exception as e:
            logger.error(f"生成报告失败: {e}")


def main():
    """主函数"""
    print("🔧 ComfyUI内存泄漏和工作流验证问题修复工具")
    print("=" * 50)
    
    fixer = ComfyUIMemoryFixer()
    
    try:
        # 执行修复
        fixer.fix_memory_leaks()
        
        # 创建验证器
        fixer.create_workflow_validator()
        
        # 生成报告
        fixer.generate_fix_report()
        
        print("\n✅ 修复完成！")
        print("\n📋 建议的后续步骤:")
        print("1. 重启ComfyUI服务")
        print("2. 测试图片生成功能")
        print("3. 监控内存使用情况")
        print("4. 检查生成的修复报告")
        
    except Exception as e:
        logger.error(f"修复过程失败: {e}")
        print(f"\n❌ 修复失败: {e}")


if __name__ == "__main__":
    main() 