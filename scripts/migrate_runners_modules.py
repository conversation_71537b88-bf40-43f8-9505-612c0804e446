#!/usr/bin/env python3
"""
[二次开发] Runners模块迁移脚本
将支持模块从runners目录迁移到shared目录，优化架构层级

开发说明：
- 此脚本为二次开发代码，不属于langbot原生代码
- 功能：重构runners目录架构，将工具类迁移到合适位置
- 维护者：开发团队
- 最后更新：2025-01-09
- 相关任务：优化runners目录架构
"""

import os
import shutil
from pathlib import Path

class RunnersModuleMigrator:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.runners_dir = self.project_root / "pkg/provider/runners"
        self.shared_dir = self.project_root / "pkg/workers/shared"
        
    def create_target_directories(self):
        """创建目标目录结构"""
        directories = [
            self.shared_dir / "image_handlers",
            self.shared_dir / "websocket", 
            self.shared_dir / "sync"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            # 创建__init__.py文件
            init_file = directory / "__init__.py"
            if not init_file.exists():
                init_file.write_text("# Shared modules\n")
        
        print("✅ 目标目录结构已创建")
    
    def migrate_files(self):
        """迁移文件"""
        migrations = [
            # (源文件, 目标目录)
            ("standard_image_handler.py", "image_handlers"),
            ("kontext_image_handler.py", "image_handlers"), 
            ("comfyui_websocket_client.py", "websocket"),
            ("admin_sync_handler.py", "sync")
        ]
        
        for source_file, target_subdir in migrations:
            source_path = self.runners_dir / source_file
            target_dir = self.shared_dir / target_subdir
            target_path = target_dir / source_file
            
            if source_path.exists():
                print(f"📦 迁移 {source_file} -> {target_subdir}/")
                shutil.copy2(source_path, target_path)
                
                # 更新文件头注释
                self._update_file_header(target_path, target_subdir)
            else:
                print(f"⚠️ 源文件不存在: {source_file}")
    
    def _update_file_header(self, file_path: Path, category: str):
        """更新文件头注释"""
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # 添加迁移说明
            migration_note = f'''"""
[迁移说明] 此文件已从 pkg/provider/runners/ 迁移到 pkg/workers/shared/{category}/
原因：优化架构层级，将工具类与运行器分离
迁移时间：2025-01-09
"""

'''
            
            # 在第一个docstring后添加迁移说明
            if '"""' in content:
                parts = content.split('"""', 2)
                if len(parts) >= 3:
                    new_content = f'"""{parts[1]}"""\n\n{migration_note}{parts[2]}'
                    file_path.write_text(new_content, encoding='utf-8')
                    
        except Exception as e:
            print(f"⚠️ 更新文件头失败 {file_path}: {e}")
    
    def delete_redundant_files(self):
        """删除冗余文件"""
        redundant_files = [
            "unified_routing_mixin_v2.py",
            "unified_agent.py", 
            "smart_workflow_handler.py"
        ]
        
        for file_name in redundant_files:
            file_path = self.runners_dir / file_name
            if file_path.exists():
                print(f"🗑️ 删除冗余文件: {file_name}")
                file_path.unlink()
            else:
                print(f"ℹ️ 文件不存在，跳过: {file_name}")
    
    def update_imports(self):
        """更新import语句（生成更新脚本）"""
        import_updates = {
            "from .standard_image_handler import StandardImageHandler": 
                "from ...workers.shared.image_handlers.standard_image_handler import StandardImageHandler",
            "from .kontext_image_handler import KontextImageHandler":
                "from ...workers.shared.image_handlers.kontext_image_handler import KontextImageHandler", 
            "from .admin_sync_handler import AdminSyncHandler":
                "from ...workers.shared.sync.admin_sync_handler import AdminSyncHandler",
            "from .comfyui_websocket_client import ComfyUIWebSocketClient":
                "from ...workers.shared.websocket.comfyui_websocket_client import ComfyUIWebSocketClient"
        }
        
        print("\n📝 需要更新的import语句:")
        for old_import, new_import in import_updates.items():
            print(f"  {old_import}")
            print(f"  -> {new_import}")
        
        print("\n🔧 请手动更新以下文件中的import语句:")
        print("  - pkg/provider/runners/comfyui_agent.py")
        print("  - 其他引用这些模块的文件")
    
    def generate_summary(self):
        """生成迁移总结"""
        print("\n" + "="*60)
        print("📊 Runners模块架构优化总结")
        print("="*60)
        
        print("\n✅ 保留的核心运行器:")
        core_runners = [
            "localagent.py - LangBot原生本地Agent",
            "base_agent.py - 公共基础功能", 
            "comfyui_agent.py - ComfyUI工作流执行器",
            "smart_hybrid_agent.py - 智能混合网关",
            "dashscopeapi.py - 阿里云API运行器",
            "difysvapi.py - Dify API运行器",
            "n8nsvapi.py - n8n API运行器"
        ]
        for runner in core_runners:
            print(f"  ✅ {runner}")
        
        print("\n📦 迁移到shared的模块:")
        migrated_modules = [
            "image_handlers/ - 图片处理器",
            "websocket/ - WebSocket客户端", 
            "sync/ - 同步处理器"
        ]
        for module in migrated_modules:
            print(f"  📦 {module}")
        
        print("\n🗑️ 删除的冗余文件:")
        deleted_files = [
            "unified_routing_mixin_v2.py - 已废弃的路由Mixin",
            "unified_agent.py - 冗余的统一Agent",
            "smart_workflow_handler.py - 冗余的工作流处理器"
        ]
        for file in deleted_files:
            print(f"  🗑️ {file}")
        
        print("\n🎯 优化效果:")
        print("  ✅ 架构层级更清晰")
        print("  ✅ 运行器与工具类分离") 
        print("  ✅ 消除功能重复")
        print("  ✅ 提高代码可维护性")
        
        print("\n📝 后续步骤:")
        print("  1. 手动更新import语句")
        print("  2. 运行测试确保功能正常")
        print("  3. 提交代码变更")
    
    def run(self):
        """执行迁移"""
        print("🚀 开始Runners模块架构优化...")
        
        # 1. 创建目标目录
        self.create_target_directories()
        
        # 2. 迁移文件
        self.migrate_files()
        
        # 3. 删除冗余文件
        self.delete_redundant_files()
        
        # 4. 生成import更新指南
        self.update_imports()
        
        # 5. 生成总结
        self.generate_summary()
        
        print("\n🎉 架构优化完成！")

def main():
    """主函数"""
    migrator = RunnersModuleMigrator()
    migrator.run()

if __name__ == "__main__":
    main()
