{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:local": "NEXT_PUBLIC_API_BASE_URL=http://localhost:5300 next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint-staged": "lint-staged"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["next lint --fix", "prettier --write"]}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.5", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "i18next": "^25.1.2", "i18next-browser-languagedetector": "^8.1.0", "input-otp": "^1.4.2", "lodash": "^4.17.21", "lucide-react": "^0.507.0", "next": "15.2.4", "next-themes": "^0.4.6", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-i18next": "^15.5.1", "react-photo-view": "^1.2.7", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5", "uuidjs": "^5.1.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "lint-staged": "^15.5.1", "prettier": "^3.5.3", "tw-animate-css": "^1.2.9", "typescript": "^5.8.3", "typescript-eslint": "^8.31.1"}}