# --again 功能说明文档

## 📋 功能概述

`--again` 功能允许用户快速重新生成上一次成功的图片，无需重新输入提示词、图片和参数。这大大提高了用户体验，特别是在需要多次生成相似图片时。

## 🎯 功能特点

### 核心特性
- **一键重新生成**: 使用 `aigen --again` 即可重新生成上一次的图片
- **完整配置保存**: 保存完整的工作流数据、提示词、参数、LoRA配置等
- **用户隔离**: 每个用户的数据独立存储，不会互相干扰
- **自动过期**: 数据保存24小时后自动过期，避免占用过多存储空间
- **跨工作流支持**: 支持 AIGEN、KONTEXT、KONTEXT_API 等所有工作流类型

### 技术特性
- **持久化存储**: 使用JSON文件存储工作流快照
- **内存优化**: 使用弱引用避免内存泄漏
- **错误处理**: 完善的错误处理和用户提示
- **历史记录**: 保存最近10次的工作流历史

## 🚀 使用方法

### 基本使用流程

1. **正常生成图片**:
   ```
   aigen 生成一只可爱的猫咪，动漫风格
   go
   ```

2. **重新生成相同图片**:
   ```
   aigen --again
   ```

### 支持的触发词

- `aigen --again` - 重新生成AIGEN工作流
- `kontext --again` - 重新生成KONTEXT工作流  
- `kontext_api --again` - 重新生成KONTEXT_API工作流

### 参数组合

虽然可以与其他参数组合使用，但 `--again` 会忽略其他参数：

```bash
# 这些参数会被忽略，仍然使用上一次的配置
aigen --again --seed 12345 --civitai animal
```

## 📁 技术实现

### 核心组件

#### 1. 参数解析器 (`parameter_parser.py`)
- 新增 `use_again` 字段
- 添加 `--again` 参数模式识别
- 优先级处理（--again 优先检查）

#### 2. 工作流持久化管理器 (`workflow_persistence_manager.py`)
- `WorkflowSnapshot` 数据类：存储完整工作流快照
- `WorkflowPersistenceManager` 类：管理数据的保存和加载
- 支持用户隔离、数据过期、历史记录等功能

#### 3. Flux工作流管理器 (`flux_workflow_manager.py`)
- 新增 `handle_again_request()` 方法
- 在成功执行后自动保存工作流数据
- 集成持久化管理器

#### 4. ComfyUI Agent (`comfyui_agent.py`)
- 在用户输入处理中检测 `--again` 参数
- 新增 `_handle_again_request()` 方法
- 格式化重新生成的完成消息

### 数据结构

#### WorkflowSnapshot
```python
@dataclass
class WorkflowSnapshot:
    workflow_data: Dict[str, Any]      # 完整的工作流JSON数据
    user_prompt: str                   # 用户原始提示词
    optimized_prompt: str              # 优化后的英文提示词
    workflow_type: str                 # 工作流类型
    parameters: Dict[str, Any]         # 工作流参数
    lora_info: Dict[str, Any]          # LoRA模型信息
    image_info: Dict[str, Any]         # 图片信息
    timestamp: float                   # 保存时间戳
    user_id: str                       # 用户ID
    chat_id: str                       # 聊天ID
    success: bool = True               # 是否成功生成
```

### 存储文件

- `temp/last_successful_workflow.json` - 最新成功的工作流数据
- `temp/workflow_history.json` - 工作流历史记录（最多10条）

## 🔧 配置选项

### 持久化管理器配置
```python
# 最大历史记录数量
max_history = 10

# 数据过期时间（小时）
max_age_hours = 24

# 存储目录
storage_dir = "temp"
```

## ⚠️ 注意事项

### 使用限制
1. **需要先执行一次正常生成**: 必须有成功的工作流记录才能使用 `--again`
2. **用户隔离**: 只能重新生成自己的工作流，不能访问其他用户的数据
3. **时间限制**: 工作流数据24小时后过期
4. **参数忽略**: `--again` 会忽略同时提供的其他参数

### 错误处理
- 没有历史数据时会提示用户先执行正常生成
- 数据过期时会提示重新生成
- 执行失败时会显示具体错误信息

## 🧪 测试验证

运行测试脚本验证功能：
```bash
python test_again_feature.py
```

测试内容包括：
- 参数解析功能
- 持久化管理器
- 文件结构检查
- 使用示例展示

## 🔄 工作流程

```mermaid
graph TD
    A[用户输入 aigen --again] --> B[参数解析器检测 --again]
    B --> C[ComfyUI Agent 处理]
    C --> D[调用 Flux 工作流管理器]
    D --> E[加载上一次工作流数据]
    E --> F{数据是否存在?}
    F -->|否| G[返回错误提示]
    F -->|是| H[直接执行工作流]
    H --> I[发送生成的图片]
    I --> J[发送完成消息]
```

## 📈 性能优化

1. **内存管理**: 使用弱引用避免循环引用
2. **文件缓存**: JSON文件存储，读取速度快
3. **数据清理**: 自动清理过期数据
4. **错误恢复**: 完善的异常处理机制

## 🔮 未来扩展

### 可能的改进方向
1. **多历史选择**: 支持选择历史记录中的特定工作流
2. **参数覆盖**: 允许部分参数覆盖（如只改变种子）
3. **批量重新生成**: 支持一次重新生成多张图片
4. **云端同步**: 支持跨设备的工作流数据同步

### 命令扩展
```bash
# 未来可能支持的命令
aigen --again --history 2    # 使用第2个历史记录
aigen --again --seed 12345   # 覆盖种子参数
aigen --again --count 3      # 重新生成3张
```

## 📝 更新日志

- **v1.0** (2025-01-10): 初始版本发布
  - 基本的 --again 功能
  - 工作流数据持久化
  - 用户隔离和数据过期
  - 完整的错误处理
