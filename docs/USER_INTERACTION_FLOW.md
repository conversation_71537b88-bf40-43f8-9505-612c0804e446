# 🎯 用户交互流程详解

## 📋 **系统概述**

LangBot图片生成系统采用智能路由和会话式交互机制，为用户提供简单直观的图片生成体验。

## 🚀 **三种触发指令**

### 1. **Aigen本地工作流**
```
aigen [提示词]
```
- **用途**：本地ComfyUI环境下的图片生成
- **支持模式**：
  - 纯文生图（无图片）
  - 控制图+文生图（草图、线稿等）
  - 参考图+文生图（风格、内容参考）
  - 混合模式（控制图+参考图+文字）

### 2. **Kontext本地模式**
```
kontext [指令]
```
- **用途**：本地ComfyUI环境下的图片编辑
- **支持模式**：
  - 单图编辑（1张图片）
  - 双图编辑（2张图片）
  - 多图编辑（3张图片）

### 3. **Kontext API模式**
```
kontext api [指令]
```
- **用途**：云端ComfyUI API的图片编辑
- **支持模式**：同Kontext本地模式
- **优势**：快速生成，无需本地资源

## 🔄 **完整交互流程**

### **阶段1：触发检测**
```
用户发送消息 → 系统检测前缀 → 启动对应工作流
```

**检测逻辑**：
- 检查消息是否以 `aigen`、`kontext`、`kontext api` 开头
- 提取提示词/指令
- 分析上传的图片类型和数量

### **阶段2：智能路由**
```
消息分析 → 意图识别 → 工作流选择 → 环境确定
```

**路由决策**：
- **Aigen工作流**：根据图片类型选择ControlNet、Redux或混合模式
- **Kontext工作流**：根据图片数量选择1图、2图、3图工作流
- **环境选择**：本地或API模式

### **阶段3：会话建立**
```
工作流启动 → 会话创建 → 状态管理 → 用户反馈
```

**会话状态**：
- `WAITING_FOR_PROMPT`：等待提示词
- `WAITING_FOR_IMAGES`：等待图片
- `READY_FOR_GENERATION`：准备生成
- `GENERATING`：生成中
- `COMPLETED`：已完成
- `CANCELLED`：已取消

### **阶段4：交互式调整（仅在同一会话内）**
```
用户输入 → 状态更新 → 参数调整 → 反馈确认
```

**支持操作**：
- 修改提示词
- 上传图片
- 调整参数
- 查看状态

**重要说明**：只有在同一工作流会话内才能进行交互式调整。生成完成后会话结束，后续消息会被视为普通聊天。

### **阶段5：执行生成**
```
触发指令 → 参数验证 → 工作流执行 → 结果返回 → 会话结束
```

**触发指令**：
- `开始`、`go`、`执行`、`run`、`start`、`generate`
- `确定`、`ok`、`yes`、`继续`、`proceed`、`完成`

**取消指令**：
- `取消`、`cancel`、`stop`、`停止`、`退出`、`exit`、`quit`

**会话结束**：生成完成后会话自动结束，用户需要重新发送前缀指令启动新的工作流。

## 📝 **详细使用示例**

### **示例1：Aigen纯文生图（单次生成）**
```
用户: "aigen 一只可爱的橘猫"
系统: "🎨 开始Aigen工作流...
📝 提示词: 一只可爱的橘猫
🔧 工作流: 标准文生图
💡 发送'开始'指令生成图片"

用户: "开始"
系统: "🚀 开始生成图片..."
[生成并返回图片]
[工作流结束]

用户: "让它更可爱一些"
系统: [忽略，因为工作流已结束，这是普通聊天]
```

### **示例2：Aigen控制图生成（单次生成）**
```
用户: "aigen 生成一个科幻机器人"
[上传草图]
系统: "🎨 检测到草图，选择ControlNet工作流
📝 提示词: 生成一个科幻机器人
🔧 工作流: 草图控制生成"

用户: "开始"
系统: "🚀 开始生成图片..."
[生成并返回图片]
[工作流结束]
```

### **示例3：基于生成图片的二次生成**
```
用户: "aigen 一只可爱的橘猫"
系统: "🎨 开始Aigen工作流...
📝 提示词: 一只可爱的橘猫
🔧 工作流: 标准文生图
💡 发送'开始'指令生成图片"

用户: "开始"
系统: "🚀 开始生成图片..."
[生成并返回图片]
[工作流结束]

用户: [引用刚才生成的图片] "aigen 让它更可爱一些"
系统: "🎨 开始Aigen工作流...
📝 提示词: 让它更可爱一些
🔧 工作流: 参考图生成
💡 发送'开始'指令生成图片"

用户: "开始"
系统: "🚀 开始生成图片..."
[生成并返回新图片]
[工作流结束]
```

### **示例4：Kontext单图编辑（单次生成）**
```
用户: "kontext 编辑这张图片"
[上传1张图片]
系统: "🎨 选择单图Kontext工作流
📝 指令: 编辑这张图片
🔧 工作流: 单图编辑"

用户: "开始"
系统: "🚀 开始生成图片..."
[生成并返回图片]
[工作流结束]
```

### **示例5：多轮调整（同一会话内）**
```
用户: "aigen 一只可爱的橘猫"
系统: "🎨 开始Aigen工作流...
📝 提示词: 一只可爱的橘猫
🔧 工作流: 标准文生图
💡 发送'开始'指令生成图片"

用户: "让它更可爱一些"
系统: "📝 更新提示词: 一只更可爱的橘猫"

用户: "开始"
系统: "🚀 开始生成图片..."
[生成并返回图片]
[工作流结束]
```

## ⚙️ **技术实现细节**

### **智能路由系统**
- **意图分析器**：分析用户文本意图
- **图像分析器**：识别图像类型和特征
- **决策引擎**：基于多维度信息选择工作流
- **环境选择器**：确定本地或API执行环境

### **会话管理系统**
- **状态机**：管理会话生命周期
- **超时处理**：自动清理过期会话
- **并发控制**：限制用户并发会话数
- **数据持久化**：保存会话状态

### **工作流执行器**
- **本地执行器**：调用本地ComfyUI API
- **API执行器**：调用云端ComfyUI API
- **参数优化器**：根据用户意图优化生成参数
- **结果处理器**：处理生成结果和错误

## 🔧 **配置和扩展**

### **工作流配置**
```json
{
  "workflow_type": "TEXT_TO_IMAGE",
  "file_path": "default_workflow.json",
  "default_params": {
    "steps": 18,
    "guidance": 3.0,
    "width": 1024,
    "height": 1024
  }
}
```

### **会话配置**
```json
{
  "timeout_seconds": 300,
  "max_images": 3,
  "min_images": 1,
  "max_concurrent_sessions": 5
}
```

### **路由规则配置**
```json
{
  "trigger_prefixes": ["aigen", "kontext", "kontext api"],
  "execution_triggers": ["开始", "go", "执行"],
  "cancel_triggers": ["取消", "cancel", "停止"]
}
```

## 🎯 **用户体验优化**

### **简化指令**
- 只需记住三种前缀
- 智能自动选择工作流
- 会话式交互，无需重复前缀

### **智能反馈**
- 实时状态更新
- 清晰的错误提示
- 友好的操作指导

### **灵活控制**
- 支持多轮调整
- 随时可以取消
- 超时自动保护

### **性能优化**
- 本地/API双模式
- 并发控制
- 资源管理 