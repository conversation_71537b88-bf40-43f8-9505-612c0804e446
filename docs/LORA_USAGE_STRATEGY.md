# Lora模型使用策略

## 概述

基于你的建议，我们重新设计了Lora模型的使用策略，采用**本地优先 + 按需下载**的方式，避免用户长时间等待和下载不合适的模型。

## 核心策略

### 1. 本地优先策略
- **优先使用本地模型**：系统首先从本地已下载的Lora模型中寻找匹配的模型
- **智能触发词匹配**：根据用户输入的关键词自动匹配最合适的本地模型
- **优先级模型**：通用性强的模型（如细节增强、写实风格）标记为优先模型，在没有特定匹配时优先使用

### 2. 按需下载策略
- **明确触发**：只有用户明确使用 `--civitai` 参数时才会进行远程搜索和下载
- **智能搜索**：根据用户输入的关键词在Civitai上搜索评分最高的模型
- **自动下载**：找到合适的模型后自动下载到本地并集成到配置中

## 模型选择逻辑

### 第一步：本地模型匹配
```python
# 检查用户输入中的触发词
for model in local_models:
    for trigger in model.trigger_words:
        if trigger in user_input:
            match_score += 1
```

### 第二步：优先级排序
```python
# 按匹配分数、优先级、评分排序
models.sort(key=lambda x: (x.match_score, x.is_priority, x.rating), reverse=True)
```

### 第三步：默认模型
```python
# 如果没有匹配，使用细节增强模型
if not matched_models:
    use_default_detail_model()
```

### 第四步：Civitai搜索（可选）
```python
# 仅当用户明确要求时
if use_civitai and civitai_query:
    search_and_download_from_civitai(civitai_query)
```

## 优先级模型

以下模型被标记为优先级模型（通用性强）：

### 🎯 细节增强类
- `detail_aidmafluxproultra-FLUX-v0.1` - 主要细节增强模型
- `add-detail-xl` - XL版本细节增强

### 🎨 写实风格类
- `flux_realism_lora` - Flux写实风格

这些模型在没有特定匹配时会优先使用，确保图像质量。

## 使用示例

### 1. 普通使用（本地模型）
```
用户: "生成一张建筑图片"
系统: 自动选择 ASTRA_Flux_OC_Vbeta-2（建筑类模型）
```

### 2. 无匹配时（默认模型）
```
用户: "生成一张抽象画"
系统: 使用 detail_aidmafluxproultra-FLUX-v0.1（细节增强模型）
```

### 3. 明确要求Civitai搜索
```
用户: "生成一张建筑图片 --civitai"
系统:
1. 解析参数：--civitai
2. 先尝试本地建筑模型
3. 在Civitai搜索"建筑"相关模型
4. 下载评分最高的模型
5. 使用新下载的模型生成
```

### 4. 指定Civitai搜索关键词
```
用户: "生成现代建筑 --civitai architecture"
系统:
1. 解析参数：--civitai architecture
2. 在Civitai搜索"architecture"相关模型
3. 下载并使用最佳模型
```

### 5. 组合参数使用
```
用户: "生成建筑图片 --civitai modern --quality high --seed 12345"
系统:
1. 解析参数：Civitai搜索"modern"，高质量，指定种子
2. 搜索下载合适的LoRA模型
3. 使用指定参数生成
```

## 配置管理

### 优先级标记
```json
{
  "name": "detail_aidmafluxproultra-FLUX-v0.1",
  "is_priority": true,
  "category": "detail",
  "trigger_words": ["细节", "detail", "高清", "high quality"]
}
```

### 触发词配置
每个模型都有详细的触发词配置，支持中英文匹配：
```json
{
  "trigger_words": ["建筑", "architecture", "building", "house"]
}
```

## 性能优化

### 1. 响应速度
- **本地模型**：毫秒级响应
- **Civitai搜索**：网络延迟 + 下载时间（通常30秒-2分钟）

### 2. 存储管理
- **自动清理**：定期清理未使用的远程模型信息
- **配置备份**：重要配置自动备份

### 3. 缓存策略
- **搜索结果缓存**：避免重复搜索
- **模型信息缓存**：减少API调用

## 用户体验

### 优势
1. **快速响应**：大部分情况下使用本地模型，响应迅速
2. **智能选择**：自动匹配最合适的模型
3. **按需扩展**：需要时可以从Civitai获取新模型
4. **质量保证**：优先模型确保基础质量

### 使用建议
1. **日常使用**：直接输入提示词，系统自动选择
2. **特殊需求**：使用 `--civitai` 参数获取专业模型
3. **批量处理**：预先下载常用模型到本地
4. **模型管理**：定期使用 `/lora stats` 查看模型状态

## 故障排除

### 1. 模型未找到
- 检查模型文件是否存在
- 确认配置文件正确
- 使用 `/lora list` 查看可用模型

### 2. 下载失败
- 检查网络连接
- 确认Civitai服务状态
- 查看错误日志

### 3. 效果不理想
- 调整模型权重
- 尝试不同的触发词
- 使用 `/lora search` 查找更合适的模型

## 未来改进

### 1. 智能权重调整
- 根据图像内容自动调整模型权重
- 多模型组合优化

### 2. 用户偏好学习
- 记录用户选择偏好
- 个性化模型推荐

### 3. 批量模型管理
- 一键下载模型包
- 模型效果对比工具

### 4. 社区模型库
- 用户分享模型配置
- 社区评分系统

## 总结

新的Lora模型使用策略实现了：
- ✅ **快速响应**：本地优先，避免网络延迟
- ✅ **智能选择**：触发词匹配 + 优先级排序
- ✅ **按需扩展**：支持 `--civitai` 参数触发远程搜索
- ✅ **参数化控制**：支持多种生成参数（种子、质量、LoRA等）
- ✅ **质量保证**：优先级模型确保基础质量
- ✅ **用户友好**：简单易用的参数语法

## 🆕 新增功能 (v1.1.0)

### 参数化生成
现在支持在生图指令中直接使用参数：

```bash
# Civitai集成
aigen 生成建筑 --civitai architecture
aigen 现代风格 --civitai modern --quality high

# 模型控制
aigen 人像摄影 --lora portrait_model 0.9 --seed 12345
aigen 风景画 --quality high --steps 50 --cfg 7.5

# 样式控制
aigen 艺术作品 --style realistic --ar 16:9
```

### 智能参数解析
- 自动提取和解析用户输入中的参数
- 清理提示词，移除参数标记
- 保持原有的智能路由功能

### 无缝集成
- 与现有的两级路由系统完全兼容
- 不影响普通用户的使用体验
- 为高级用户提供精确控制

这种策略既保证了用户体验，又提供了扩展性和精确控制，是一个平衡且强大的解决方案。