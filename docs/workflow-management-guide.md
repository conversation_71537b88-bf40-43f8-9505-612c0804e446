# LangBot 多工作流管理系统使用指南

## 📋 任务状态跟踪

✅ **完成状态**：[multi_workflow_system] REQ-01 完成  
📌 **验证点**：
- 功能边界是否明确: ✅ 已定义多工作流选择、参数配置、智能推荐功能
- 输出指令格式是否定义清晰: ✅ 已定义API接口和参数映射规范  
- 数据记录需求是否完整: ✅ 已包含工作流配置、参数历史记录

## 概述

LangBot 多工作流管理系统支持根据不同的用户生成要求自动选择最适合的 ComfyUI 工作流，提升图像生成的质量和针对性。

## 🎯 支持的工作流类型

### 1. 默认工作流 (DEFAULT)
- **适用场景**: 通用图像生成
- **关键词**: `默认`, `通用`, `普通`
- **默认参数**: 1024x1024, 20步, 引导3.5
- **特点**: 平衡的质量和速度

### 2. 人像工作流 (PORTRAIT)
- **适用场景**: 人物肖像、头像生成
- **关键词**: `人像`, `头像`, `肖像`, `人物`, `脸部`, `selfie`
- **默认参数**: 800x1280, 25步, 引导4.0
- **特点**: 优化面部细节，竖版比例

### 3. 风景工作流 (LANDSCAPE)
- **适用场景**: 自然风景、景观图片
- **关键词**: `风景`, `景观`, `自然`, `山`, `海`, `天空`, `森林`, `湖`
- **默认参数**: 1024x768, 20步, 引导3.0
- **特点**: 横版比例，自然色彩

### 4. 动漫工作流 (ANIME)
- **适用场景**: 动漫风格、二次元图片
- **关键词**: `动漫`, `二次元`, `卡通`, `anime`, `manga`, `可爱`
- **默认参数**: 800x1280, 28步, 引导4.5
- **特点**: 鲜艳色彩，细腻线条

## 🔧 使用方法

### 自动工作流选择

系统会根据用户输入的内容自动选择最适合的工作流：

```python
# 用户输入示例
"画一个美女肖像"          → 选择人像工作流
"生成一张山水风景图"       → 选择风景工作流  
"画个可爱的动漫女孩"       → 选择动漫工作流
"创建一张图片"            → 选择默认工作流
```

### API 接口使用

#### 1. 获取所有工作流列表
```bash
GET /api/v1/workflow/list

# 响应
{
  "success": true,
  "data": {
    "workflows": [
      {
        "type": "portrait",
        "name": "人像生成工作流",
        "description": "专门用于生成人像的工作流",
        "keywords": ["人像", "头像", "肖像"],
        "aspect_ratios": [[800, 1280], [720, 1440]],
        "default_steps": 25,
        "max_steps": 40
      }
    ],
    "count": 4
  }
}
```

#### 2. 智能选择工作流
```bash
POST /api/v1/workflow/select
Content-Type: application/json

{
  "user_input": "画一个美女头像",
  "analysis": {}
}

# 响应
{
  "success": true,
  "data": {
    "workflow": {
      "type": "portrait",
      "name": "人像生成工作流",
      "description": "专门用于生成人像的工作流",
      "file_path": "portrait_workflow.json",
      "default_params": {
        "steps": 25,
        "guidance": 4.0,
        "width": 768,
        "height": 1024
      }
    }
  }
}
```

#### 3. 分析用户输入并推荐
```bash
POST /api/v1/workflow/analyze
Content-Type: application/json

{
  "user_input": "生成一张高质量的横版风景图"
}

# 响应
{
  "success": true,
  "data": {
    "recommended_workflow": {
      "type": "landscape",
      "name": "风景生成工作流"
    },
    "content_type": "landscape",
    "recommended_params": {
      "steps": 30,
      "guidance": 4.0,
      "width": 1344,
      "height": 896
    }
  }
}
```

## 🛠️ 添加新工作流

### 方法1: 使用 API 创建模板

```bash
POST /api/v1/workflow/create
Content-Type: application/json

{
  "workflow_type": "realistic",
  "base_workflow": "default_workflow.json"
}
```

### 方法2: 手动创建

1. **创建工作流文件**
   ```bash
   cp workflows/default_workflow.json workflows/realistic_workflow.json
   ```

2. **修改工作流参数**
   - 调整节点参数以适应特定风格
   - 修改默认提示词
   - 设置合适的采样参数

3. **在代码中注册**
   ```python
   # 在 ComfyUIWorkflowManager._register_default_workflows() 中添加
   self.workflows[WorkflowType.REALISTIC] = WorkflowConfig(
       name="写实风格工作流",
       file_path="realistic_workflow.json",
       workflow_type=WorkflowType.REALISTIC,
       description="生成写实风格图片的工作流",
       keywords=["写实", "真实", "照片", "realistic"],
       default_params={
           "steps": 30,
           "guidance": 3.8,
           "width": 1024,
           "height": 1024,
           "seed": -1
       },
       param_mapping={
           "prompt": "6",
           "steps": "55",
           "guidance": "55",
           "width": "50",
           "height": "50",
           "seed": "55"
       },
       aspect_ratios=[(1024, 1024), (1280, 720)],
       max_steps=50,
       default_steps=30
   )
   ```

## 📊 工作流配置参数说明

### WorkflowConfig 字段说明

- **name**: 工作流显示名称
- **file_path**: 工作流JSON文件路径
- **workflow_type**: 工作流类型枚举
- **description**: 工作流描述
- **keywords**: 触发关键词列表
- **default_params**: 默认参数配置
- **param_mapping**: 参数到节点ID的映射
- **aspect_ratios**: 支持的宽高比列表
- **max_steps**: 最大采样步数
- **default_steps**: 默认采样步数

### 参数映射 (param_mapping)

```python
param_mapping = {
    "prompt": "6",     # 提示词节点ID
    "steps": "55",     # 采样步数节点ID
    "guidance": "55",  # 引导值节点ID
    "width": "50",     # 图像宽度节点ID
    "height": "50",    # 图像高度节点ID
    "seed": "55"       # 种子节点ID
}
```

## 🎨 工作流定制指南

### 针对不同风格的优化建议

#### 人像工作流优化
- 使用更高的引导值 (4.0-5.0) 确保面部细节
- 竖版比例 (3:4 或 2:3) 适合人像构图
- 增加采样步数 (25-30) 提升质量

#### 风景工作流优化
- 使用较低的引导值 (3.0-3.5) 保持自然感
- 横版比例 (16:9 或 4:3) 适合风景构图
- 标准采样步数 (20-25) 平衡质量和速度

#### 动漫工作流优化
- 使用更高的引导值 (4.5-5.0) 增强风格化
- 灵活的比例支持多种构图
- 较多采样步数 (25-35) 确保线条清晰

## 🔍 智能选择算法

### 关键词匹配优先级

1. **显式关键词匹配**: 直接匹配工作流的 keywords 列表
2. **内容分析匹配**: 基于语义分析判断内容类型
3. **默认工作流**: 未匹配到特定类型时使用

### 参数推荐逻辑

```python
# 质量关键词检测
if '高质量' in user_input or '精细' in user_input:
    steps = max(default_steps + 5, 25)
    guidance += 0.5

# 速度关键词检测  
elif '快速' in user_input or '简单' in user_input:
    steps = max(default_steps - 5, 15)
    guidance -= 0.5

# 尺寸关键词检测
if '横版' in user_input or 'landscape' in user_input:
    width, height = 1280, 800
elif '竖版' in user_input or 'portrait' in user_input:
    width, height = 800, 1280
```

## 📈 性能监控

### 工作流使用统计

系统会记录每种工作流的使用频率，帮助优化：

- 工作流选择准确率
- 平均生成时间
- 用户满意度反馈
- 参数调优效果

### 日志记录

```python
self.ap.logger.info(f"选择工作流: {workflow_config.name} ({workflow_config.workflow_type.value})")
self.ap.logger.info(f"工作流文件: {workflow_file}")
self.ap.logger.info(f"最终参数: {workflow_params}")
```

## 🚀 扩展建议

### 计划中的工作流类型

- **ARCHITECTURAL**: 建筑设计工作流
- **PRODUCT**: 产品设计工作流  
- **CONCEPT_ART**: 概念艺术工作流
- **ILLUSTRATION**: 插画工作流
- **PHOTOGRAPHY**: 摄影风格工作流

### 高级功能规划

1. **学习型推荐**: 基于用户历史选择学习偏好
2. **A/B 测试**: 同一输入测试不同工作流效果
3. **自定义工作流**: 用户可视化创建专属工作流
4. **工作流市场**: 社区分享和下载工作流
5. **实时性能监控**: 工作流执行性能实时监控

## ❓ 常见问题

### Q: 如何添加新的工作流类型？
A: 1) 在 `WorkflowType` 枚举中添加新类型，2) 创建对应的 JSON 工作流文件，3) 在 `_register_default_workflows()` 中注册配置。

### Q: 工作流选择不准确怎么办？
A: 可以调整关键词列表或优化 `_analyze_content_type()` 方法的判断逻辑。

### Q: 如何优化特定工作流的参数？
A: 修改对应工作流的 `default_params` 配置，或调整 ComfyUI JSON 文件中的节点参数。

### Q: 可以同时使用多个工作流吗？
A: 目前每次生成使用一个工作流，但可以通过 batch 处理同时运行多个工作流。

## 📚 相关文档

- [ComfyUI 官方文档](https://github.com/comfyanonymous/ComfyUI)
- [Flux 模型文档](https://github.com/black-forest-labs/flux)
- [LangBot API 文档](../api-specs/API_V1.md)
- [部署指南](../deployment/DEV_GUIDE.md) 