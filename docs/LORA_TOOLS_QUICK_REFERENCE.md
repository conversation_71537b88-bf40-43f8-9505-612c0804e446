# LoRA工具快速参考

## 🚀 快速开始

### 1. 自动发现新模型
```bash
# 检查有哪些新模型（不实际修改）
python3 discover_lora_models.py --dry-run

# 发现并添加新模型到配置
python3 discover_lora_models.py
```

### 2. 分析模型用途
```bash
# 查看可用模板
python3 analyze_lora_models.py templates

# 分析单个模型
python3 analyze_lora_models.py analyze "模型名称"

# 应用模板
python3 analyze_lora_models.py template "模型名称" --template "建筑渲染" --save
```

### 3. 检查模型状态
```bash
# 列出所有模型
python3 check_lora_status.py list

# 检查特定模型
python3 check_lora_status.py status "模型名称"
```

## 📋 常用场景

### 场景1：下载新模型后
```bash
# 1. 将.safetensors文件放到ComfyUI/models/loras/
# 2. 自动发现
python3 discover_lora_models.py
# 3. 分析用途
python3 analyze_lora_models.py analyze "新模型名"
# 4. 应用模板
python3 analyze_lora_models.py template "新模型名" --template "建筑渲染" --save
```

### 场景2：批量优化现有模型
```bash
# 1. 查看所有模型
python3 check_lora_status.py list
# 2. 逐个分析重要模型
python3 analyze_lora_models.py analyze "ASTRA_XL_OC_V3"
python3 analyze_lora_models.py template "ASTRA_XL_OC_V3" --template "建筑渲染" --save
```

### 场景3：从Civitai获取模型
```bash
# 1. 搜索模型
python3 check_lora_status.py search "architecture"
# 2. 下载模型
python3 check_lora_status.py download "civitai_12345_awesome_model"
# 3. 自动发现
python3 discover_lora_models.py
# 4. 标注用途
python3 analyze_lora_models.py template "新模型名" --template "建筑渲染" --save
```

## 🎯 可用模板

| 模板名称 | 用途 | 适用场景 |
|---------|------|----------|
| `建筑渲染` | 专业建筑效果图渲染 | 建筑、室内、景观设计 |
| `室内设计` | 室内设计和装饰 | 家居、商业空间 |
| `人像美化` | 人像美化和优化 | 人物肖像、美化 |
| `风景摄影` | 自然风景和景观摄影 | 自然风光、城市景观 |
| `动漫风格` | 动漫和二次元风格 | 动漫、游戏角色 |
| `细节增强` | 图像细节和质量增强 | 通用质量提升 |
| `写实风格` | 写实和照片级渲染 | 真实感渲染 |
| `艺术风格` | 艺术风格和创意渲染 | 创意艺术作品 |

## 🔧 高级用法

### 自定义更新模型
```bash
python3 analyze_lora_models.py update "模型名称" \
  --category "architecture" \
  --trigger-words "建筑,architecture,building" \
  --description "专业建筑渲染模型" \
  --priority \
  --save
```

### 批量处理
```bash
# 为多个模型应用相同模板
for model in "模型1" "模型2" "模型3"; do
  python3 analyze_lora_models.py template "$model" --template "建筑渲染" --save
done
```

### 定期维护
```bash
# 每周运行一次
python3 discover_lora_models.py --dry-run  # 先检查
python3 discover_lora_models.py            # 确认后执行
```

## 📊 状态检查

### 查看模型统计
```bash
python3 check_lora_status.py list | grep -E "(architecture|detail|portrait)"
```

### 检查配置文件
```bash
cat config/lora_models.json | jq '.models[] | select(.category == "architecture")'
```

## 🚨 故障排除

### 模型未发现
- 检查文件是否在正确的目录：`ComfyUI/models/loras/`
- 确认文件格式为 `.safetensors`
- 检查文件权限

### 模板应用失败
- 确认模型名称完全匹配
- 检查模板名称是否正确
- 验证配置文件权限

### Civitai连接失败
- 检查网络连接
- 验证API Key配置
- 确认Civitai服务状态

## 📚 相关文档

- [完整管理指南](LORA_MANAGEMENT_GUIDE.md)
- [使用策略](LORA_USAGE_STRATEGY.md)
- [工作流管理](workflow-management-guide.md)

---

*最后更新: 2024年12月* 