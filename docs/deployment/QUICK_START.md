# 🚀 LangBot 快速启动指南

## 📋 服务器重启后的启动步骤

### 方法1: 一键启动 (推荐)

```bash
cd /home/<USER>/Workspace/langbot
./start-all-services.sh
```

### 方法2: 手动启动

```bash
cd /home/<USER>/Workspace/langbot

# 1. 启动 WeChatPadPro
docker-compose -f wechatpad-docker-compose.yml up -d

# 2. 等待15秒，然后启动 LangBot
sleep 15
docker-compose up -d

# 3. 检查服务状态
docker-compose ps
```

## 🔍 服务状态检查

### 检查容器状态
```bash
docker-compose ps
```

### 查看日志
```bash
# 查看LangBot日志
docker-compose logs -f langbot

# 查看WeChatPadPro日志
docker-compose -f wechatpad-docker-compose.yml logs -f wechatpadpro
```

## 🛑 停止服务

### 一键停止
```bash
./stop-all-services.sh
```

### 手动停止
```bash
# 停止LangBot
docker-compose down

# 停止WeChatPadPro
docker-compose -f wechatpad-docker-compose.yml down
```

## 🔧 常见问题

### 1. 端口冲突
如果遇到端口占用问题：
```bash
# 检查端口占用
netstat -tulpn | grep :5300
netstat -tulpn | grep :2285

# 杀死占用进程
sudo kill -9 <PID>
```

### 2. 容器启动失败
```bash
# 查看详细错误信息
docker-compose logs langbot

# 重新构建镜像
docker-compose build --no-cache
```

### 3. 微信连接问题
- 确保WeChatPadPro先启动
- 检查网络连接
- 验证配置文件
