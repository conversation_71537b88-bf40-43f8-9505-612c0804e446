# LangBot 开发部署规范

## 环境分工
```mermaid
graph LR
    DEV[开发环境] -->|提交| CI[CI流水线]
    CI -->|通过| ART[构件仓库]
    ART -->|拉取| PROD[生产环境]
开发流程
创建特性分支 git checkout -b feat/xxx
本地验证 make test
提交PR请求审查 EOF
cat > docs/adr/0001-use-poetry.md << 'EOF'

ADR 0001: 使用Poetry管理依赖
决策背景
需要精确控制依赖版本
支持可重复构建
备选方案
Pipenv
原生pip+requirements.txt EOF

### 4. 添加Makefile构建支持
```bash
cat > Makefile << 'EOF'
.PHONY: test build

test:
	poetry run pytest -v

build:
	poetry build -f wheel

lint:
	poetry run flake8 src
