# 服务器重启后的服务启动指南

## 📋 概述

当服务器关机重启后，需要按照正确的顺序启动所有服务。本指南将详细说明启动步骤和相关命令。

## 🔄 服务启动顺序

**重要**: 必须按照以下顺序启动服务，确保依赖关系正确：

```
1. WeChatPadPro 服务 (微信机器人核心)
   ├── MySQL 数据库
   ├── Redis 缓存
   └── WeChatPadPro 主服务

2. LangBot 服务 (AI对话机器人)
```

## 🚀 详细启动步骤

### 步骤1: 进入项目目录

```bash
cd /home/<USER>/Workspace/langbot
```

### 步骤2: 启动 WeChatPadPro 服务

```bash
# 使用专用的 docker-compose 文件启动 WeChatPadPro
docker-compose -f wechatpad-docker-compose.yml up -d
```

**预期输出**:
```
Creating network "deploy_wechatpadpro_net" if not exists
Creating wechatpadpro_mysql_1 ... done
Creating wechatpadpro_redis_1 ... done
Creating wechatpadpro_1 ... done
```

### 步骤3: 验证 WeChatPadPro 启动状态

```bash
# 检查容器状态
docker ps | grep wechatpadpro

# 检查服务健康状态
docker-compose -f wechatpad-docker-compose.yml ps
```

**预期输出**:
```
CONTAINER ID   IMAGE                    STATUS
xxxxx         wechatpadpro:latest      Up 30 seconds (healthy)
xxxxx         mysql:8.0                Up 30 seconds (healthy)  
xxxxx         redis:alpine             Up 30 seconds
```

### 步骤4: 等待 WeChatPadPro 完全启动

```bash
# 查看 WeChatPadPro 启动日志，确保服务完全就绪
docker logs wechatpadpro -f
```

**等待看到类似日志**:
```
[INFO] WeChatPadPro 服务已启动
[INFO] HTTP服务器监听端口: 1239
[INFO] WebSocket服务器已就绪
```

按 `Ctrl+C` 退出日志查看。

### 步骤5: 启动 LangBot 服务

```bash
# 启动 LangBot Docker 容器
docker run -d \
  --name langbot \
  --network host \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/pkg:/app/pkg \
  -v $(pwd)/config:/app/config \
  -v $(pwd)/plugins:/app/plugins \
  -v $(pwd)/workflows:/app/workflows \
  -v $(pwd)/templates:/app/templates \
  -v $(pwd)/res:/app/res \
  --restart on-failure \
  -e TZ=Asia/Shanghai \
  docker.langbot.app/langbot-public/rockchin/langbot:latest
```

### 步骤6: 验证 LangBot 启动状态

```bash
# 检查 LangBot 容器状态
docker ps | grep langbot

# 查看 LangBot 启动日志
docker logs langbot --tail 20
```

**预期输出**:
```
[INFO] WebSocket connected successfully!
[INFO] 获取用户信息成功
[INFO] Running on http://0.0.0.0:5300 (CTRL + C to quit)
```

## 🌐 服务访问地址

启动完成后，可以通过以下地址访问服务：

- **WeChatPadPro 管理界面**: http://localhost:1239
- **LangBot WebUI**: http://localhost:5300
- **LangBot API**: http://localhost:2285

## ✅ 验证服务正常运行

### 1. 检查所有容器状态

```bash
docker ps -a
```

**预期输出**:
```
CONTAINER ID   IMAGE                    STATUS
xxxxx         langbot:latest           Up 2 minutes
xxxxx         wechatpadpro:latest      Up 5 minutes (healthy)
xxxxx         mysql:8.0                Up 5 minutes (healthy)
xxxxx         redis:alpine             Up 5 minutes
```

### 2. 测试网络连接

```bash
# 测试 WeChatPadPro 服务
curl -s http://localhost:1239 | head -10

# 测试 LangBot WebUI
curl -s -o /dev/null -w "%{http_code}" http://localhost:5300
```

### 3. 微信登录

1. 访问 http://localhost:1239
2. 扫码登录微信
3. 确认登录状态显示"在线"

### 4. 测试消息响应

向微信机器人发送测试消息，验证是否正常响应。

## 🛠️ 常用管理命令

### 查看服务日志

```bash
# 查看 WeChatPadPro 日志
docker logs wechatpadpro -f

# 查看 LangBot 日志  
docker logs langbot -f

# 查看所有服务状态
docker-compose -f wechatpad-docker-compose.yml ps && docker ps | grep langbot
```

### 重启服务

```bash
# 重启 WeChatPadPro
docker-compose -f wechatpad-docker-compose.yml restart

# 重启 LangBot
docker restart langbot
```

### 停止服务

```bash
# 停止 LangBot
docker stop langbot && docker rm langbot

# 停止 WeChatPadPro
docker-compose -f wechatpad-docker-compose.yml down
```

## 🚨 故障排除

### 问题1: WeChatPadPro 启动失败

```bash
# 检查端口占用
netstat -tlnp | grep 1239

# 查看详细错误日志
docker logs wechatpadpro
```

### 问题2: LangBot 无法连接 WeChatPadPro

```bash
# 检查网络连接
docker exec langbot ping 127.0.0.1

# 检查配置文件
docker exec langbot cat /app/config/wechatpad.yaml
```

### 问题3: 微信登录状态异常

1. 重新访问 http://localhost:1239
2. 重新扫码登录
3. 检查 WeChatPadPro 日志是否有错误

## 📝 快速启动脚本

您也可以创建一个快速启动脚本：

```bash
# 创建启动脚本
cat > start-all-services.sh << 'EOF'
#!/bin/bash

echo "🚀 启动所有服务..."

# 进入项目目录
cd /home/<USER>/Workspace/langbot

echo "📱 启动 WeChatPadPro 服务..."
docker-compose -f wechatpad-docker-compose.yml up -d

echo "⏳ 等待 WeChatPadPro 启动完成..."
sleep 15

echo "🤖 启动 LangBot 服务..."
docker stop langbot 2>/dev/null || true
docker rm langbot 2>/dev/null || true

docker run -d \
  --name langbot \
  --network host \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/pkg:/app/pkg \
  -v $(pwd)/config:/app/config \
  -v $(pwd)/plugins:/app/plugins \
  -v $(pwd)/workflows:/app/workflows \
  -v $(pwd)/templates:/app/templates \
  -v $(pwd)/res:/app/res \
  --restart on-failure \
  -e TZ=Asia/Shanghai \
  docker.langbot.app/langbot-public/rockchin/langbot:latest

echo "⏳ 等待 LangBot 启动完成..."
sleep 10

echo "✅ 所有服务启动完成！"
echo ""
echo "📋 服务访问地址："
echo "   - WeChatPadPro: http://localhost:1239"
echo "   - LangBot WebUI: http://localhost:5300"
echo ""
echo "📱 请访问 http://localhost:1239 扫码登录微信"
EOF

# 赋予执行权限
chmod +x start-all-services.sh
```

使用快速启动脚本：

```bash
./start-all-services.sh
```

## 📞 技术支持

如果遇到问题，请：

1. 检查本文档的故障排除部分
2. 查看服务日志定位具体错误
3. 确认网络连接和端口状态
4. 验证配置文件是否正确

---

**最后更新**: 2025-06-21  
**适用版本**: LangBot v3.x + WeChatPadPro 