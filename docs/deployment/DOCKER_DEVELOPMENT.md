# LangBot Docker 开发环境指南

## 项目概述

这是一个基于LangBot的二次开发项目，通过Docker容器化部署，支持微信集成、LLM路由和ComfyUI图像生成功能。

## 技术架构

```mermaid
graph TB
    A[微信消息] --> B[GeWeChat适配器]
    B --> C[LangBot核心]
    C --> D[LLM路由]
    D --> E[DeepSeek/Qwen]
    C --> F[ComfyUI]
    F --> G[图像生成]
    G --> C
    C --> B
    B --> A
```

## 目录结构

```
langbot/
├── docker-compose.yaml          # LangBot主服务配置
├── gewechat-compose.yaml        # GeWeChat服务配置
├── start-services.sh            # 启动脚本
├── stop-services.sh             # 停止脚本
├── config/                      # 配置文件目录
│   ├── platform.yaml           # 平台配置
│   ├── gewechat.yaml           # GeWeChat配置
│   ├── llm_config.yaml         # LLM配置
│   └── service.yaml            # 服务配置
├── pkg/                         # 二次开发代码（挂载到容器）
├── data/                        # 数据目录
├── plugins/                     # 插件目录
├── workflows/                   # 工作流文件
├── gewechat-data/              # GeWeChat数据目录
└── templates/                   # 模板文件
```

## 快速开始

### 1. 启动服务

```bash
./start-services.sh
```

### 2. 访问服务

- **LangBot WebUI**: http://localhost:5300
- **GeWeChat管理界面**: http://localhost:19088

### 3. 停止服务

```bash
./stop-services.sh
```

## 配置说明

### LangBot配置

1. **platform.yaml**: 启用GeWeChat适配器
2. **gewechat.yaml**: 配置GeWeChat连接参数
3. **llm_config.yaml**: 配置DeepSeek API密钥
4. **service.yaml**: 配置服务端口和ComfyUI集成

### 二次开发

- 修改 `pkg/` 目录下的代码会自动同步到容器内
- 配置文件修改后需要重启容器生效
- 日志查看：`docker-compose logs -f`

## 开发流程

### 1. 代码修改

```bash
# 修改pkg目录下的代码
vim pkg/your_module.py

# 重启服务使修改生效
docker-compose restart
```

### 2. 配置更新

```bash
# 修改配置文件
vim config/your_config.yaml

# 重启服务
docker-compose restart
```

### 3. 日志调试

```bash
# 查看LangBot日志
docker-compose logs -f langbot

# 查看GeWeChat日志
docker-compose -f gewechat-compose.yaml logs -f
```

## 微信集成配置

### 1. 访问GeWeChat管理界面

打开 http://localhost:19088

### 2. 创建应用

1. 点击"创建应用"
2. 设置回调URL: `http://your_server_ip:2285/gewechat/callback`
3. 设置Token（与config/gewechat.yaml中的token一致）
4. 记录生成的AppID

### 3. 登录微信

1. 点击"扫码登录"
2. 使用手机微信扫描二维码
3. 登录成功后显示"已登录"状态

### 4. 更新配置

将GeWeChat生成的AppID填入 `config/gewechat.yaml` 的 `app_id` 字段。

## ComfyUI集成

### 1. 启动ComfyUI

```bash
# 如果需要本地ComfyUI服务
docker run -d --name comfyui -p 8188:8188 comfyanonymous/comfyui
```

### 2. 创建工作流

1. 访问 http://localhost:8188
2. 创建图像生成工作流
3. 保存为JSON文件到 `workflows/` 目录

### 3. 配置工作流

在 `config/service.yaml` 中配置工作流文件路径。

## 故障排除

### 常见问题

1. **Docker权限问题**
   ```bash
   sudo usermod -aG docker $USER
   # 重新登录或执行
   newgrp docker
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :5300
   # 修改docker-compose.yaml中的端口映射
   ```

3. **容器启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs langbot
   ```

4. **微信连接失败**
   - 检查GeWeChat服务状态
   - 验证Token和AppID配置
   - 确认网络连接正常

### 重置环境

```bash
# 停止所有服务
./stop-services.sh

# 清理数据（谨慎操作）
sudo rm -rf data/* gewechat-data/*

# 重新启动
./start-services.sh
```

## 开发建议

1. **代码版本控制**: 确保pkg目录下的代码已提交到Git
2. **配置备份**: 定期备份config目录
3. **日志监控**: 使用 `docker-compose logs -f` 监控服务状态
4. **测试验证**: 修改代码后及时测试功能

## 参考资源

- [LangBot官方文档](https://docs.langbot.app/)
- [GeWeChat项目](https://github.com/gewechat/gewechat)
- [ComfyUI项目](https://github.com/comfyanonymous/ComfyUI) 