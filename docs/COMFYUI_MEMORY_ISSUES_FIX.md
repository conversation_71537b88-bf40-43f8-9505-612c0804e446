# ComfyUI内存泄漏和工作流验证问题修复指南

## 问题描述

根据你提供的调试信息，系统出现了以下问题：

### 1. 内存泄漏警告
```
WARNING, memory leak with model Flux. Please make sure it is not being referenced from somewhere.
Potential memory leak detected with model Flux, doing a full garbage collect, for maximum performance avoid circular references in the model code.
```

### 2. 工作流验证失败
```
Failed to validate prompt for output 63:
* FluxSamplerParams+ 55:
  - Exception when validating inner node: '62'
Output will be ignored
invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
```

### 3. PyTorch安全警告
```
FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value)
```

## 问题原因分析

### 1. 内存泄漏原因
- **循环引用**：LoRA节点之间可能存在循环引用
- **弱引用缺失**：没有使用弱引用来管理对象生命周期
- **资源未释放**：工作流执行后没有及时清理资源
- **缓存累积**：工作流缓存过多，占用大量内存

### 2. 工作流验证失败原因
- **节点连接错误**：LoRA节点连接到不存在的节点
- **权重范围错误**：LoRA权重超出有效范围
- **文件路径错误**：LoRA文件路径不正确或文件不存在
- **节点类型不匹配**：使用了不兼容的节点类型

### 3. PyTorch安全警告原因
- **安全设置缺失**：没有启用`weights_only=True`模式
- **模型加载方式**：使用了不安全的模型加载方式

## 解决方案

### 1. 内存管理优化

#### 代码层面修复
```python
# 使用弱引用避免循环引用
self._workflow_cache = weakref.WeakValueDictionary()
self._model_references = weakref.WeakSet()

# 强制垃圾回收
def _cleanup_resources(self):
    self._workflow_cache.clear()
    self._model_references.clear()
    gc.collect()
```

#### 配置层面优化
```yaml
memory_management:
  enable_memory_cleanup: true
  cleanup_interval: 5  # 每5次生成后清理
  max_workflow_cache_size: 100
  force_garbage_collection: true
```

### 2. 工作流验证增强

#### 节点验证
```python
def _validate_lora_node(self, lora: LoRAConfig, model_input, clip_input) -> bool:
    # 验证LoRA文件名
    if not lora.filename or not lora.filename.strip():
        return False
    
    # 验证权重范围
    if not (0.1 <= lora.weight <= 1.0):
        return False
    
    # 验证输入连接
    if not model_input or not clip_input:
        return False
    
    return True
```

#### 连接验证
```python
def _validate_connections(self, workflow_data):
    errors = []
    for node_id, node_data in workflow_data.items():
        inputs = node_data.get("inputs", {})
        for input_name, input_value in inputs.items():
            if isinstance(input_value, list) and len(input_value) >= 2:
                target_node = input_value[0]
                if target_node not in workflow_data:
                    errors.append(f"节点{node_id}连接到不存在的节点: {target_node}")
    return errors
```

### 3. PyTorch安全设置

#### 启用安全模式
```python
# 在模型加载时使用
torch.load(model_path, weights_only=True, map_location='cpu')
```

#### 配置文件设置
```yaml
pytorch_settings:
  weights_only: true
  map_location: cpu
```

## 修复工具

### 1. 内存修复脚本
```bash
python3 scripts/fix_comfyui_memory_issues.py
```

**功能：**
- 清理工作流缓存
- 验证LoRA文件完整性
- 优化ComfyUI配置
- 强制垃圾回收
- 创建工作流验证器

### 2. 服务重启脚本
```bash
./scripts/restart_comfyui_with_fixes.sh
```

**功能：**
- 停止ComfyUI服务
- 清理内存和缓存
- 应用内存修复
- 重启ComfyUI服务
- 验证服务状态

## 预防措施

### 1. 定期维护
- **每周清理**：清理工作流缓存和临时文件
- **内存监控**：监控系统内存使用情况
- **日志检查**：定期检查错误日志

### 2. 最佳实践
- **限制LoRA数量**：同时使用不超过3个LoRA
- **权重控制**：确保LoRA权重在0.1-1.0范围内
- **文件验证**：定期验证LoRA文件完整性
- **服务重启**：定期重启ComfyUI服务

### 3. 监控指标
- **内存使用率**：保持在80%以下
- **工作流缓存大小**：不超过100个
- **错误日志频率**：监控错误日志增长趋势
- **生成成功率**：保持在95%以上

## 故障排除

### 1. 内存泄漏持续
**症状：** 内存使用率持续增长
**解决方案：**
```bash
# 强制重启服务
./scripts/restart_comfyui_with_fixes.sh

# 检查进程
ps aux | grep comfyui

# 清理系统缓存
sudo sync && sudo sysctl -w vm.drop_caches=3
```

### 2. 工作流验证失败
**症状：** 生成失败，日志显示验证错误
**解决方案：**
```bash
# 验证LoRA文件
python3 scripts/fix_comfyui_memory_issues.py

# 检查工作流
python3 -c "from pkg.workers.flux.workflow_validator import workflow_validator; print('验证器可用')"
```

### 3. 服务无法启动
**症状：** ComfyUI服务启动失败
**解决方案：**
```bash
# 检查端口占用
netstat -tlnp | grep 8188

# 检查日志
tail -f /home/<USER>/Workspace/ComfyUI/comfyui.log

# 重新安装依赖
cd /home/<USER>/Workspace/ComfyUI
pip install -r requirements.txt
```

## 性能优化建议

### 1. 系统层面
- **增加内存**：建议16GB以上
- **使用SSD**：提高文件读写速度
- **GPU优化**：确保GPU驱动最新

### 2. 应用层面
- **缓存策略**：合理设置缓存大小
- **并发控制**：限制同时生成的任务数
- **资源监控**：实时监控资源使用情况

### 3. 代码层面
- **异步处理**：使用异步操作提高性能
- **批量处理**：批量处理相似任务
- **错误恢复**：实现自动错误恢复机制

## 总结

通过以上修复措施，可以有效解决ComfyUI的内存泄漏和工作流验证问题：

1. **✅ 内存管理优化**：使用弱引用和垃圾回收
2. **✅ 工作流验证增强**：添加节点和连接验证
3. **✅ PyTorch安全设置**：启用weights_only模式
4. **✅ 自动化工具**：提供修复和重启脚本
5. **✅ 预防措施**：建立定期维护机制

建议按照以下顺序执行修复：

1. 运行内存修复脚本
2. 重启ComfyUI服务
3. 测试图片生成功能
4. 监控系统性能
5. 建立定期维护计划

如果问题仍然存在，请检查系统资源使用情况，并考虑升级硬件配置。 