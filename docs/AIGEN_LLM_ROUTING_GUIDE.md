# 🎯 AIGEN管道LLM智能路由指南

## 📋 概述

AIGEN管道现在采用**完全基于LLM的智能路由系统**，摒弃了传统的关键词匹配，通过大语言模型的语义理解能力来智能选择最合适的工作流。

## 🧠 核心理念

### ControlNet vs Redux 的区别

#### 🎛️ **ControlNet（控制图）**
- **作用**: 控制图像的**结构、姿势、轮廓、构图**
- **原理**: 从输入图像提取结构信息（边缘、深度、姿势等），指导生成图像的几何形状
- **适用场景**: 
  - 保持特定的人物姿势
  - 复制建筑物的结构布局
  - 维持物体的轮廓形状
  - 控制画面的构图和透视

#### 🎨 **Redux（参考图）**
- **作用**: 提供**风格、色彩、氛围、视觉特征**参考
- **原理**: 从参考图中提取风格特征，进行风格迁移和视觉特征复制
- **适用场景**:
  - 模仿特定的艺术风格
  - 复制图像的色调和氛围
  - 获得相似的视觉效果
  - 风格迁移和重新演绎

## 🔄 智能路由流程

### 1. **无图片输入（纯文生图）**
```
用户: "aigen 生成一只可爱的小猫"
↓
LLM分析: 无图片输入，纯文生图需求
↓
选择: flux_default.json (TEXT_ONLY)
↓
优化提示词: "a cute small cat, high quality, detailed, masterpiece"
```

### 2. **单张图片输入**
```
用户: "aigen 生成一个和这张图片姿势相同的女孩" + [人物姿势图]
↓
LLM分析: 用户想要复制图片中的姿势结构
↓
判断: 这是ControlNet用途（控制姿势）
↓
选择: flux_controlnet.json (CONTROL_ONLY)
```

```
用户: "aigen 生成一张和这个风格类似的风景画" + [艺术风格图]
↓
LLM分析: 用户想要模仿图片的艺术风格
↓
判断: 这是Redux用途（风格参考）
↓
选择: flux_redux.json (REFERENCE_ONLY)
```

### 3. **双张图片输入**
```
用户: "aigen 用第一张图的姿势，第二张图的风格，生成新图片" + [姿势图] + [风格图]
↓
LLM分析: 第一张用于控制姿势，第二张用于风格参考
↓
判断: 混合用途（ControlNet + Redux）
↓
选择: flux_controlnet_redux.json (CONTROL_REFERENCE)
```

## 🤖 LLM系统提示词

### 核心分析能力
LLM被训练来理解：

1. **用户意图识别**
   - 是否需要控制图像结构？
   - 是否需要风格参考？
   - 是否是纯文生图？

2. **图片用途分析**
   - 分析每张图片在用户需求中的作用
   - 区分结构控制和风格参考
   - 处理混合用途场景

3. **工作流选择**
   - TEXT_ONLY: 纯文生图
   - CONTROL_ONLY: 仅ControlNet
   - REFERENCE_ONLY: 仅Redux
   - CONTROL_REFERENCE: 混合工作流

## 📝 实际示例

### 示例1: 姿势控制
```
输入: "aigen 生成一个做瑜伽动作的女孩，要和图片中的姿势一样" + [瑜伽姿势图]

LLM分析:
- 用户明确要求"姿势一样"
- 这是结构控制需求
- 图片用于ControlNet

输出:
- workflow_subtype: CONTROL_ONLY
- workflow_file: flux_controlnet.json
- reasoning: "用户要求复制图片中的瑜伽姿势，这是典型的ControlNet用途"
```

### 示例2: 风格参考
```
输入: "aigen 画一幅山水画，要有这种古典的感觉" + [古典山水画]

LLM分析:
- 用户要求"古典的感觉"
- 这是风格和氛围需求
- 图片用于Redux风格参考

输出:
- workflow_subtype: REFERENCE_ONLY
- workflow_file: flux_redux.json
- reasoning: "用户要求模仿古典山水画的风格和氛围，这是Redux的典型应用"
```

### 示例3: 混合应用
```
输入: "aigen 用这个人的姿势，画成这种油画风格" + [人物姿势图] + [油画风格图]

LLM分析:
- 第一张图：控制人物姿势（ControlNet）
- 第二张图：提供油画风格（Redux）
- 需要混合工作流

输出:
- workflow_subtype: CONTROL_REFERENCE
- workflow_file: flux_controlnet_redux.json
- reasoning: "需要同时使用第一张图控制姿势和第二张图提供油画风格"
```

## 🛡️ 回退机制

当LLM分析失败时，系统提供智能回退：

1. **无图片**: 默认TEXT_ONLY工作流
2. **单张图片**: 默认REFERENCE_ONLY，但会询问用户确认
3. **多张图片**: 默认CONTROL_REFERENCE，要求用户明确指定用途

## ✨ 优势

### 相比关键词匹配的优势：

1. **语义理解**: 理解用户的真实意图，而非表面词汇
2. **上下文感知**: 结合图片数量和用户描述进行综合判断
3. **灵活适应**: 能处理各种表达方式和复杂场景
4. **智能推理**: 能够推断隐含的需求和意图

### 实际效果：

- ✅ **准确率更高**: 基于语义理解而非关键词匹配
- ✅ **用户友好**: 支持自然语言描述
- ✅ **扩展性强**: 易于添加新的工作流类型
- ✅ **智能回退**: 当分析失败时有合理的默认选择

## 🔧 技术实现

### 核心方法：
- `_route_aigen_with_llm()`: 主要的LLM路由方法
- `_get_aigen_workflow_system_prompt()`: 专门的系统提示词
- `_build_aigen_analysis_prompt()`: 构建分析提示
- `_parse_aigen_llm_response()`: 解析LLM响应

### 工作流映射：
```python
{
    'TEXT_ONLY': 'flux_default.json',
    'CONTROL_ONLY': 'flux_controlnet.json', 
    'REFERENCE_ONLY': 'flux_redux.json',
    'CONTROL_REFERENCE': 'flux_controlnet_redux.json'
}
```

这个智能路由系统代表了从规则驱动到AI驱动的重要转变，为用户提供了更自然、更智能的图像生成体验。
