# 二次开发代码清单

## 📋 概述

本文档记录所有二次开发的代码文件和模块，用于区分langbot原生代码和二次开发代码。

## 🏷️ 标识规范

### 文件头注释格式
```python
"""
[二次开发] 模块名称
功能描述

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：[具体功能描述]
- 维护者：[开发团队]
- 最后更新：[日期]
- 相关任务：[任务编号]
"""
```

### 目录标识
在二次开发目录的 `__init__.py` 文件中添加标识：
```python
"""
[二次开发目录] 目录名称
此目录下的所有文件均为二次开发代码，不属于langbot原生代码
"""
```

## 📁 二次开发代码清单

### 1. Flux工作流模块 (`pkg/workers/flux/`)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `flux_workflow_manager.py` | Flux工作流管理器 | ✅ 已标识 | 2024-12-20 |
| `flux_prompt_optimizer.py` | Flux提示词优化器 | ✅ 已标识 | 2024-12-20 |
| `image_file_manager.py` | 图片文件管理器 | ✅ 已标识 | 2024-12-20 |
| `seed_manager.py` | 种子管理器 | ✅ 已标识 | 2024-12-20 |
| `standard_nodes.py` | 标准节点映射器 | ✅ 已标识 | 2024-12-20 |
| `flux_workflow_models.py` | Flux工作流数据模型 | ✅ 已标识 | 2024-12-20 |
| `lora_integration.py` | LoRA集成模块 | ✅ 已标识 | 2024-12-20 |
| `__init__.py` | 模块初始化文件 | ✅ 已标识 | 2024-12-20 |

### 2. Kontext工作流模块 (`pkg/workers/kontext/`)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `local_kontext_workflow_manager.py` | 本地Kontext工作流管理器 | ✅ 已标识 | 2024-12-20 |
| `kontext_prompt_optimizer.py` | Kontext提示词优化器 | ✅ 已标识 | 2024-12-20 |
| `custom_nodes.py` | 自定义节点处理器 | ✅ 已标识 | 2024-12-20 |
| `multi_image_handler.py` | 多图片处理器 | ✅ 已标识 | 2024-12-20 |
| `prompt_upsampler.py` | 提示词增强器 | ✅ 已标识 | 2024-12-20 |
| `kontext_image_processor.py` | 图片处理器 | ✅ 已标识 | 2024-12-20 |
| `kontext_workflow_models.py` | Kontext工作流数据模型 | ✅ 已标识 | 2024-12-20 |
| `local_executor.py` | 本地执行器 | ✅ 已标识 | 2024-12-20 |
| `__init__.py` | 模块初始化文件 | ✅ 已标识 | 2024-12-20 |

### 3. Kontext API工作流模块 (`pkg/workers/kontext_api/`)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `kontext_api_workflow_manager.py` | Kontext API工作流管理器 | ✅ 已标识 | 2024-12-20 |
| `kontext_api_upload_manager.py` | 图片上传管理器 | ✅ 已标识 | 2024-12-20 |
| `kontext_api_auth_handler.py` | 认证处理器 | ✅ 已标识 | 2024-12-20 |
| `kontext_api_queue_monitor.py` | 队列监控器 | ✅ 已标识 | 2024-12-20 |

### 4. 共享模块 (`pkg/workers/shared/`)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `shared_lora_manager.py` | 共享LoRA管理器 | ✅ 已标识 | 2024-12-20 |
| `shared_comfyui_client.py` | 共享ComfyUI客户端 | ✅ 已标识 | 2024-12-20 |
| `shared_civitai_client.py` | Civitai客户端 | ✅ 已标识 | 2024-12-20 |
| `__init__.py` | 模块初始化文件 | ✅ 已标识 | 2024-12-20 |

### 5. 核心模块 (部分文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `pkg/core/workflow/unified_routing_system.py` | 统一路由系统 | ✅ 已标识 | 2024-12-20 |
| `pkg/core/session/manager.py` | 会话管理器 | ✅ 已标识 | 2024-12-20 |
| `pkg/core/workflow/manager_base.py` | 工作流管理器基类 | ✅ 已标识 | 2024-12-20 |

### 6. 适配器模块

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `pkg/adapters/comfyui_adapter.py` | ComfyUI适配器 | ✅ 已标识 | 2024-12-20 |
| `pkg/adapters/wechat_adapter.py` | 微信适配器 | ✅ 已标识 | 2024-12-20 |

## 🔧 实施步骤

### 第一步：添加文件头标识
为每个二次开发文件添加标准化的文件头注释：

```python
"""
[二次开发] 模块名称
功能描述

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：[具体功能描述]
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：[任务编号]
- 依赖关系：[依赖说明]
"""
```

### 第二步：更新目录标识
在二次开发目录的 `__init__.py` 文件中添加标识：

```python
"""
[二次开发目录] 目录名称
此目录下的所有文件均为二次开发代码，不属于langbot原生代码

包含模块：
- [模块1]: [功能描述]
- [模块2]: [功能描述]
- ...

维护者：开发团队
最后更新：2024-12-20
"""
```

### 第三步：创建代码地图
生成可视化的代码地图，清晰显示二次开发代码的分布：

```mermaid
graph TD
    A[pkg/workers/] --> B[flux/]
    A --> C[kontext/]
    A --> D[kontext_api/]
    A --> E[shared/]
    
    B --> B1[flux_workflow_manager.py]
    B --> B2[flux_prompt_optimizer.py]
    B --> B3[image_file_manager.py]
    
    C --> C1[local_kontext_workflow_manager.py]
    C --> C2[kontext_prompt_optimizer.py]
    C --> C3[custom_nodes.py]
    
    D --> D1[kontext_api_workflow_manager.py]
    D --> D2[kontext_api_upload_manager.py]
    
    E --> E1[shared_lora_manager.py]
    E --> E2[shared_comfyui_client.py]
    
    style B fill:#e1f5fe
    style C fill:#e1f5fe
    style D fill:#e1f5fe
    style E fill:#e1f5fe
```

## 📊 统计信息

### 代码分布统计
- **总文件数**: 25个二次开发文件
- **Flux模块**: 6个文件
- **Kontext模块**: 8个文件
- **Kontext API模块**: 4个文件
- **共享模块**: 3个文件
- **核心模块**: 3个文件
- **适配器模块**: 2个文件

### 功能分类统计
- **工作流管理**: 8个文件
- **提示词优化**: 2个文件
- **图片处理**: 3个文件
- **节点处理**: 2个文件
- **客户端服务**: 3个文件
- **数据模型**: 2个文件
- **路由系统**: 1个文件
- **会话管理**: 1个文件
- **适配器**: 2个文件

## 🎯 优势分析

### 1. 风险控制
- ✅ 不破坏现有目录结构
- ✅ 不影响现有功能
- ✅ 避免大规模重构风险

### 2. 清晰标识
- ✅ 明确区分原生代码和二次开发代码
- ✅ 便于代码审查和维护
- ✅ 符合规则要求

### 3. 易于维护
- ✅ 保持现有模块化结构
- ✅ 便于功能扩展
- ✅ 支持渐进式改进

## 📋 维护规范

### 新增二次开发代码
1. 在文件开头添加标准化的标识注释
2. 更新本文档的代码清单
3. 记录功能描述和依赖关系

### 修改二次开发代码
1. 更新文件头注释中的"最后更新"日期
2. 记录修改的功能和原因
3. 更新相关文档

### 删除二次开发代码
1. 确认不再需要该功能
2. 从本文档中移除记录
3. 清理相关依赖关系

---

**维护者**: 开发团队  
**创建日期**: 2024-12-20  
**最后更新**: 2024-12-20  
**状态**: 实施中 