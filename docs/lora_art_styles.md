# LoRA艺术风格识别与优先级系统

## 概述

本文档描述了LoRA模型选择系统中的艺术风格识别机制和优先级排序规则。系统能够智能识别用户提示词中的艺术风格，并据此优先选择相应的LoRA模型。

## 支持的艺术风格分类

### 1. 动漫/卡通风格 (anime)
**优先级**: 最高 (+200分)
**关键词**: 
- 中文: 动漫, 漫画, 二次元, 卡通
- 英文: anime, manga, cartoon, semi-realistic anime, niji, cel shading, flat color

### 2. 摄影/写实风格 (photography)
**优先级**: 高 (+180分)
**关键词**:
- 中文: 摄影, 照片, 写实, 真实
- 英文: photography, photo, photographic, realistic, photorealistic, hyperrealistic, lifelike

### 3. 绘画风格 - 数字艺术 (digital_painting)
**优先级**: 高 (+170分)
**关键词**:
- 中文: 数字绘画, 数字艺术, 概念艺术
- 英文: digital painting, digital art, cg, concept art, matte painting

### 4. 绘画风格 - 传统媒介
**优先级**: 高 (+170分)

#### 油画 (oil_painting)
- 中文: 油画
- 英文: oil painting, oil on canvas, classical painting

#### 水彩 (watercolor)
- 中文: 水彩
- 英文: watercolor, watercolour, aquarelle, wet on wet

#### 丙烯 (acrylic)
- 中文: 丙烯
- 英文: acrylic, acrylic painting, acrylic on canvas

#### 中国画 (chinese_painting)
- 中文: 中国画, 国画, 水墨
- 英文: chinese painting, ink wash, sumi-e

#### 马克笔 (marker)
- 中文: 马克笔
- 英文: marker, copic, alcohol marker, felt tip

### 5. 插画风格 (illustration)
**优先级**: 中高 (+160分)
**关键词**:
- 中文: 插画
- 英文: illustration, illustrative, book illustration

### 6. 特殊主题风格
**优先级**: 中高 (+150分)

#### 赛博朋克 (cyberpunk)
- 中文: 赛博朋克
- 英文: cyberpunk, futuristic, sci-fi, neon, dystopian

#### 蒸汽朋克 (steampunk)
- 中文: 蒸汽朋克
- 英文: steampunk, victorian, brass, mechanical

#### 奇幻 (fantasy)
- 中文: 奇幻
- 英文: fantasy, magical, mythical, fairy tale

### 7. 抽象/超现实风格
**优先级**: 中 (+140分)

#### 抽象 (abstract)
- 中文: 抽象
- 英文: abstract, non-representational, geometric abstract

#### 超现实 (surreal)
- 中文: 超现实
- 英文: surreal, surrealism, dreamlike, fantastical

#### 拼贴 (collage)
- 中文: 拼贴, 拼贴画
- 英文: collage, mixed media, photomontage

### 8. 技术特征 (detail)
**优先级**: 中 (+120分)
**关键词**:
- 中文: 细节, 增强, 高清
- 英文: detail, enhance, quality, upscale, high resolution, sharp, crisp

## LoRA选择优先级规则

### 分类平衡策略
1. **第一轮选择**: 优先选择不同分类的高分模型，确保分类多样性
2. **第二轮选择**: 填补剩余空位，选择剩余的高分模型
3. **数量限制**: 最多3个LoRA模型（最佳实践）

### 评分机制
- **基础分类优先级**: 根据LoRA分类获得基础分数
- **风格匹配加分**: 根据检测到的艺术风格给予额外分数
- **触发词匹配**: 精确匹配触发词 +50分
- **模型评分**: 模型评分 × 10分
- **优先级模型**: +100分

### 理想选择顺序
1. **第一选择**: 风格类LoRA（anime、style等）
2. **第二选择**: 细节类LoRA（detail、enhance等）
3. **第三选择**: 内容类LoRA（architecture、character等）

## 使用示例

### 示例1: 动漫风格
**输入**: "A beautiful anime girl with semi-realistic anime styling"
**检测风格**: anime
**优先选择**: anime类LoRA → detail类LoRA → 其他类LoRA

### 示例2: 摄影风格
**输入**: "A photorealistic portrait, photography style, high quality"
**检测风格**: photography, detail
**优先选择**: style类LoRA → detail类LoRA → portrait类LoRA

### 示例3: 数字绘画
**输入**: "Digital painting of a fantasy landscape, concept art"
**检测风格**: digital_painting, fantasy
**优先选择**: style类LoRA → detail类LoRA → landscape类LoRA

## 注意事项

1. **关键词重叠**: 某些关键词可能同时匹配多个风格，这是正常现象
2. **中英文支持**: 系统同时支持中文和英文关键词识别
3. **动态调整**: 可根据实际使用情况调整关键词和优先级分数
4. **扩展性**: 新的艺术风格可以轻松添加到系统中
