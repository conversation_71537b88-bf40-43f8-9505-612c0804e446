# LangBot 二次开发集成指南

## 概述

本指南展示了如何在不修改 LangBot 核心文件的情况下，通过新增文件的方式实现功能扩展。这种方式确保了：

- ✅ **升级兼容性**：LangBot 更新时不会影响二次开发代码
- ✅ **模块化设计**：功能独立，易于维护和调试
- ✅ **插件化架构**：通过 LangBot 的扩展机制自动集成

## 核心原则

### 1. 不修改核心文件
```
❌ 避免修改的文件：
- pkg/core/
- pkg/platform/
- pkg/pipeline/pipelinemgr.py
- main.py

✅ 推荐的扩展位置：
- pkg/services/          # 业务服务
- pkg/processors/        # 消息处理器
- pkg/pipeline/         # 新的pipeline阶段
- pkg/adapters/         # 平台适配器
```

### 2. 使用正确的导入路径
```python
# ✅ 正确的导入方式
from pkg.platform.types import events as platform_events
from pkg.platform.types import message as platform_message

# ❌ 避免的导入方式（可能导致循环导入）
from pkg.platform import platform_events
```

### 3. 使用类型注解和延迟导入
```python
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from pkg.platform.adapter import MessagePlatformAdapter

# 在函数内使用延迟导入
def some_function():
    from pkg.services.admin_sync_service import AdminSyncService
```

## 扩展功能架构

### 文件结构
```
pkg/
├── services/
│   └── admin_sync_service.py      # 管理员同步服务
├── processors/
│   └── llm_preprocessor.py        # LLM消息预处理器
└── pipeline/
    └── comfyui_integration.py     # ComfyUI集成阶段
```

### 1. 服务层（Services）

**文件：`pkg/services/admin_sync_service.py`**

```python
class AdminSyncService:
    """管理员消息同步服务"""
    
    def __init__(self, pipeline_config, query, logger):
        self.pipeline_config = pipeline_config
        self.query = query
        self.logger = logger
        
    def is_sync_enabled(self) -> bool:
        """检查是否启用管理员同步"""
        return self.pipeline_config.get("ai", {}).get("comfyui-agent", {}).get("admin-sync-enabled", False)
```

**特点：**
- 独立的业务逻辑封装
- 通过配置控制功能开关
- 错误处理和日志记录

### 2. 处理器层（Processors）

**文件：`pkg/processors/llm_preprocessor.py`**

```python
class LLMPreprocessor:
    """LLM预处理器，用于处理微信群消息并提取关键信息"""
    
    async def process_message(self, message_chain) -> Dict[str, Any]:
        """处理消息链并提取关键信息"""
        # 使用LLM分析消息内容
        # 返回结构化的分析结果
```

**特点：**
- 专门处理特定类型的消息
- 与LLM服务集成
- 返回结构化数据供后续使用

### 3. Pipeline 集成层

**文件：`pkg/pipeline/comfyui_integration.py`**

```python
@stage.stage_class('ComfyUIIntegration')
class ComfyUIIntegrationStage(stage.PipelineStage):
    """ComfyUI集成阶段"""
    
    async def process(self, query, stage_inst_name):
        """处理消息，检测图像生成需求"""
        # 延迟导入避免循环依赖
        from ..services.admin_sync_service import AdminSyncService
        
        # 业务逻辑处理
        # 返回处理结果
```

**特点：**
- 使用 `@stage.stage_class` 装饰器自动注册
- 延迟导入避免循环依赖
- 集成到 LangBot 的消息处理流水线

## 配置集成

### Pipeline 配置示例

```json
{
  "ai": {
    "comfyui-agent": {
      "enabled": true,
      "admin-sync-enabled": false,
      "workflow-management": {
        "default-workflow": "default",
        "auto-select": true
      }
    }
  }
}
```

### 阶段配置

在 LangBot 的管理界面中，将 `ComfyUIIntegration` 阶段添加到 pipeline 中：

```json
{
  "stages": [
    "ReplyRuleFilter",
    "BanSessionFilter", 
    "ContentFilter",
    "ComfyUIIntegration",  // 我们的扩展阶段
    "PreProcessor",
    "MessageTruncator",
    "AskAgent",
    "ResponseBack"
  ]
}
```

## 部署和挂载

### Docker 挂载方式

```yaml
version: '3.8'
services:
  langbot:
    image: langbot:latest
    volumes:
      # 挂载扩展文件
      - ./extensions/admin_sync_service.py:/app/pkg/services/admin_sync_service.py
      - ./extensions/llm_preprocessor.py:/app/pkg/processors/llm_preprocessor.py
      - ./extensions/comfyui_integration.py:/app/pkg/pipeline/comfyui_integration.py
      
      # 挂载配置文件
      - ./config:/app/config
```

### 开发模式挂载

```bash
# 直接将扩展文件复制到对应目录
cp extensions/admin_sync_service.py pkg/services/
cp extensions/llm_preprocessor.py pkg/processors/
cp extensions/comfyui_integration.py pkg/pipeline/
```

## 自动加载机制

### Stage 注册

LangBot 会自动扫描并注册所有使用 `@stage.stage_class` 装饰器的类：

```python
# pkg/pipeline/pipelinemgr.py 中的加载逻辑
from ..utils import importutil

importutil.import_modules_in_pkgs([...])  # 自动导入所有模块
```

### 验证注册成功

```python
from pkg.pipeline.stage import preregistered_stages
print(list(preregistered_stages.keys()))
# 输出应包含: ['ComfyUIIntegration', ...]
```

## 最佳实践

### 1. 错误处理

```python
try:
    # 业务逻辑
    result = await some_operation()
except Exception as e:
    self.logger.error(f"操作失败: {str(e)}", exc_info=True)
    # 确保pipeline继续运行
    return entities.StageProcessResult(
        result_type=entities.ResultType.CONTINUE,
        new_query=query
    )
```

### 2. 配置驱动

```python
def is_feature_enabled(self) -> bool:
    """通过配置控制功能开关"""
    return self.config.get("feature", {}).get("enabled", False)
```

### 3. 日志记录

```python
self.logger.info("功能初始化完成")
self.logger.debug(f"处理消息: {message}")
self.logger.error("处理失败", exc_info=True)
```

### 4. 资源管理

```python
async def initialize(self, config):
    """在初始化时设置资源"""
    await super().initialize(config)
    # 初始化服务连接、缓存等

async def cleanup(self):
    """清理资源"""
    # 关闭连接、清理缓存等
```

## 升级兼容性

### 版本兼容检查

```python
def check_compatibility(self):
    """检查与当前LangBot版本的兼容性"""
    if hasattr(self.ap, 'version'):
        version = self.ap.version
        # 根据版本调整行为
```

### 优雅降级

```python
try:
    # 尝试使用新API
    result = await new_api_call()
except AttributeError:
    # 降级到旧API
    result = await old_api_call()
```

## 总结

通过这种方式，我们实现了：

1. **完全非侵入式**的功能扩展
2. **自动集成**到LangBot的处理流水线
3. **配置驱动**的功能控制
4. **升级友好**的架构设计

这种方法确保了二次开发代码的独立性和可维护性，同时充分利用了LangBot的扩展机制。 