# 二次开发代码索引

## 📋 概述

本文档自动生成于 2025-07-15 14:39:38，记录了所有带有 `[二次开发]` 标识的代码文件。

## 🏷️ 标识规范

### 文件头标识格式
```python
"""
[二次开发] 模块名称
功能描述

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：[具体功能描述]
- 维护者：开发团队
- 最后更新：[日期]
- 相关任务：[任务编号]
- 依赖关系：[依赖说明]
"""
```

### 目录标识格式
```python
"""
[二次开发目录] 目录名称
此目录下的所有文件均为二次开发代码，不属于langbot原生代码
"""
```

## 📁 二次开发代码索引


### . (5个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `analyze_lora_models.py` | LoRA模型分析工具 | 🔥 活跃 | 2025-07-10 |
| `check_dev_index.py` | 二次开发文件索引检查工具 | 🔥 活跃 | 2025-07-09 |
| `check_lora_status.py` | LoRA模型状态检查工具 | 🔥 活跃 | 2025-07-15 |
| `discover_lora_models.py` | LoRA模型自动发现工具 | 🔥 活跃 | 2025-07-10 |
| `update_lora_model_types.py` | LoRA模型类型更新工具 | 🔥 活跃 | 2025-07-15 |

### pkg/adapters (2个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `comfyui_adapter.py` | ComfyUI适配器 | 🔥 活跃 | 2025-07-15 |
| `wechat_adapter.py` | 微信适配器 | 🔥 活跃 | 2025-07-15 |

### pkg/core/image (3个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `analyzer.py` | 图片分析器模块 | 🔥 活跃 | 2025-07-15 |
| `processor.py` | 统一图片处理器 | 🔥 活跃 | 2025-07-15 |
| `utils.py` | 图片处理通用工具函数 | 🔥 活跃 | 2025-07-15 |

### pkg/core/intent (3个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `analyzer.py` | 统一意图分析器 | 🔥 活跃 | 2025-07-15 |
| `models.py` | 意图分析数据模型 | 🔥 活跃 | 2025-07-15 |
| `parameter_parser.py` | 用户输入参数解析器 | 🔥 活跃 | 2025-07-15 |

### pkg/core/message (3个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `models.py` | 消息处理数据模型 | 🔥 活跃 | 2025-07-15 |
| `processor.py` | 统一消息处理器 | 🔥 活跃 | 2025-07-15 |
| `sender.py` | 统一消息发送器 | 🔥 活跃 | 2025-07-15 |

### pkg/core/session (3个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `manager.py` | 统一会话管理器 | 🔥 活跃 | 2025-07-15 |
| `models.py` | 会话管理数据模型 | 🔥 活跃 | 2025-07-15 |
| `states.py` | 会话状态工具模块 | 🔥 活跃 | 2025-07-15 |

### pkg/core/workflow (5个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `manager_base.py` | 工作流管理器基类 | 🔥 活跃 | 2025-07-15 |
| `query_utils.py` | Query对象工具函数 | 🔥 活跃 | 2025-07-14 |
| `shared_enums.py` | 工作流共享枚举类型 | 🔥 活跃 | 2025-07-15 |
| `unified_parameter_service.py` | 统一参数服务 | 🔥 活跃 | 2025-07-15 |
| `workflow_parameter_adapter.py` | 工作流参数适配器 | 🔥 活跃 | 2025-07-15 |

### pkg/workers (1个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `comfyui_worker.py` | ComfyUI工作器 | 🔥 活跃 | 2025-07-15 |

### pkg/workers/flux (4个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `flux_prompt_optimizer.py` | Flux 提示词优化模块 | 🔥 活跃 | 2025-07-14 |
| `flux_workflow_manager.py` | Flux 工作流管理器 | 🔥 活跃 | 2025-07-15 |
| `image_file_manager.py` | Flux图片文件管理器 | 🔥 活跃 | 2025-07-14 |
| `seed_manager.py` | Flux 种子管理器 | 🔥 活跃 | 2025-07-14 |

### pkg/workers/kontext (6个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `custom_nodes.py` | Kontext 自定义节点处理器 | 🔥 活跃 | 2025-07-14 |
| `kontext_prompt_optimizer.py` | Kontext 提示词优化模块 | 🔥 活跃 | 2025-07-14 |
| `local_executor.py` | Kontext 本地模式工作流执行器 | 🔥 活跃 | 2025-07-15 |
| `local_kontext_workflow_manager.py` | Local Kontext 工作流管理器 | 🔥 活跃 | 2025-07-14 |
| `multi_image_handler.py` | Kontext 多图片输入处理器 | 🔥 活跃 | 2025-07-14 |
| `prompt_upsampler.py` | Kontext 提示词增强器 | 🔥 活跃 | 2025-07-15 |

### pkg/workers/shared (3个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `again_manager.py` | 统一Again功能管理器 | 🔥 活跃 | 2025-07-15 |
| `shared_comfyui_client.py` | 共享 ComfyUI 客户端 | 🔥 活跃 | 2025-07-14 |
| `shared_lora_manager.py` | 共享 LoRA 管理器 | 🔥 活跃 | 2025-07-14 |

### pkg/workers/shared/image_handlers (2个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `kontext_image_handler.py` | 重构版 KontextImageHandler | 🔥 活跃 | 2025-07-15 |
| `standard_image_handler.py` | 标准图片处理器 | 🔥 活跃 | 2025-07-14 |

### pkg/workers/shared/image_senders (1个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `wechat_sender.py` | 微信图片发送器 | 🔥 活跃 | 2025-07-14 |

### pkg/workers/shared/sync (1个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `admin_sync_handler.py` | 管理员同步处理器 | 🔥 活跃 | 2025-07-14 |

### pkg/workers/shared/websocket (1个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `comfyui_websocket_client.py` | ComfyUI WebSocket 客户端 | 🔥 活跃 | 2025-07-14 |

### scripts (6个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `check_llm_status.py` | LLM状态检查脚本 | 🔥 活跃 | 2025-07-09 |
| `fix_llm_config.py` | LLM配置修复脚本 | 🔥 活跃 | 2025-07-09 |
| `migrate_runners_modules.py` | Runners模块迁移脚本 | 🔥 活跃 | 2025-07-09 |
| `test_comfyui_llm_config.py` | ComfyUI Agent LLM配置测试脚本 | 🔥 活跃 | 2025-07-09 |
| `test_import_migration.py` | 测试迁移后的import是否正常 | 🔥 活跃 | 2025-07-09 |
| `test_simple_import.py` | 简单的import测试，避免循环导入 | 🔥 活跃 | 2025-07-09 |

## 📊 统计信息

### 代码分布统计
- **总文件数**: 49个二次开发文件
- **Flux模块**: 4个文件
- **Kontext模块**: 6个文件
- **Kontext API模块**: 0个文件
- **共享模块**: 3个文件
- **核心模块**: 8个文件
- **适配器模块**: 2个文件

### 状态分布统计
- **🔥 活跃**: 49个文件

## 🔧 维护说明

### 自动生成
- 此文档由 `check_dev_index.py` 脚本自动生成
- 基于文件头注释中的 `[二次开发]` 标识进行扫描
- 建议定期运行脚本更新索引

### 手动维护
- 添加新文件时，确保包含标准化的 `[二次开发]` 标识
- 修改文件功能时，更新文件头注释中的描述
- 删除文件时，运行脚本重新生成索引

## 📋 使用说明

### 运行检查脚本
```bash
python check_dev_index.py
```

### 生成索引文档
```bash
python check_dev_index.py --generate
```

### 检查完整性
```bash
python check_dev_index.py --check
```

---

**维护者**: 开发团队  
**生成时间**: 2025-07-15 14:39:38  
**状态**: 自动生成
