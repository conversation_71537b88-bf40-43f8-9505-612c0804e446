# LangBot ComfyUI Agent LLM配置修复指南

## 📋 问题描述

**症状**：
- 在容器内可以测试到LLM连接正常
- 但ComfyUI工作流中LLM调用总是失败
- 智能路由、提示词优化等功能无法使用
- Web界面中ComfyUI Agent的模型选项消失

**根本原因**：
1. **配置层级错误**：ComfyUI Agent的LLM调用代码错误地从 `local-agent` 配置中获取模型，而不是从 `comfyui-agent` 配置
2. **Pipeline配置分离**：LangBot的LLM配置分为配置文件和数据库两个层面，ComfyUI工作流使用数据库配置
3. **代码逻辑缺陷**：`unified_routing_system.py` 中的 `_get_llm_model` 方法只支持 `local-agent`，不支持 `comfyui-agent`

## 🔍 快速诊断

首先运行诊断脚本检查当前状态：

```bash
# 进入langbot目录
cd /path/to/langbot

# 运行诊断脚本
python scripts/check_llm_status.py
```

诊断脚本会检查：
- ✅ 配置文件是否存在和正确
- ✅ 数据库中是否有LLM模型
- ✅ Pipeline是否正确关联了LLM模型

## 🛠️ 解决方案

### 方案1：代码修复 + 配置（推荐）

我已经修复了核心代码问题，现在需要配置LLM模型：

#### 1. 代码修复（已完成）
- ✅ 修复了 `unified_routing_system.py` 中的 `_get_llm_model` 方法，支持 `comfyui-agent`
- ✅ 修复了 `comfyui_agent.py` 中的模型配置获取逻辑
- ✅ 添加了详细的日志输出用于调试

#### 2. 配置LLM模型
```bash
# 1. 运行诊断脚本检查当前状态
python scripts/test_comfyui_llm_config.py

# 2. 如果需要，运行修复脚本添加模型
python scripts/fix_llm_config.py

# 3. 重启容器使配置生效
docker restart langbot

# 4. 查看日志确认修复
docker logs langbot --tail 50 | grep -i "llm\|model\|comfyui"
```

### 方案2：Web界面手动配置

1. **访问管理界面**：
   ```bash
   # 浏览器访问
   http://localhost:5300
   ```

2. **添加LLM模型**（如果还没有）：
   - 进入"模型管理" → "添加模型"
   - 配置信息：
     - **名称**：`DeepSeek Chat`
     - **提供商**：`deepseek-chat-completions`
     - **基础URL**：`https://api.deepseek.com/v1`
     - **API Key**：`***********************************`
     - **超时时间**：`120`

3. **🔥 关键：配置ComfyUI Agent的模型**：
   - 进入"流水线管理" → 编辑默认流水线
   - 确保"运行器"选择的是"ComfyUI 图片生成 Agent"
   - 在"ComfyUI 图片生成 Agent"配置中找到"模型"选项
   - 选择刚添加的DeepSeek模型
   - 保存配置

### 方案3：手动数据库操作（高级用户）

如果上述方案都不可行，可以直接操作数据库：

```bash
# 进入langbot容器
docker exec -it langbot bash

# 连接SQLite数据库
sqlite3 data/langbot.db

# 查看现有模型
SELECT uuid, name, requester FROM llm_models;

# 如果没有模型，需要先添加...
```

## 🧪 验证修复

修复完成后，按以下步骤验证：

1. **重启容器**：
   ```bash
   docker restart langbot
   ```

2. **检查日志**：
   ```bash
   docker logs langbot --tail 50 | grep -i "llm\|model"
   ```
   应该看到类似：
   ```
   Loading models from db...
   成功获取LLM模型: [model-uuid]
   ```

3. **测试功能**：
   - 发送微信消息：`aigen 一只可爱的猫咪`
   - 查看是否有智能路由和提示词优化的日志

4. **再次运行诊断**：
   ```bash
   python scripts/check_llm_status.py
   ```
   所有检查项应该显示 ✅

## 🔧 常见问题

### Q1: 脚本运行时提示"数据库文件不存在"
**A**: 确保langbot容器正在运行，数据库文件在容器启动时创建。

### Q2: Web界面无法访问
**A**: 检查容器端口映射和网络配置：
```bash
docker ps | grep langbot
# 确认5300端口已映射
```

### Q3: 添加模型后仍然无法调用LLM
**A**: 检查Pipeline配置是否正确关联了模型：
1. 运行诊断脚本确认
2. 在Web界面检查流水线配置
3. 重启容器

### Q4: API Key无效错误
**A**: 
1. 检查 `config/llm_config.yaml` 中的API Key是否正确
2. 确认API Key有足够余额
3. 测试API Key连接性：
   ```bash
   curl -H "Authorization: Bearer sk-your-api-key" \
        https://api.deepseek.com/v1/models
   ```

## 📚 技术原理

### LangBot的LLM架构

1. **配置文件** (`config/llm_config.yaml`)：
   - 仅用于存储API Key等敏感信息
   - 不直接用于运行时调用

2. **数据库模型** (`llm_models`表)：
   - 存储LLM模型的完整配置
   - 包含UUID、请求器类型、配置等

3. **Pipeline配置** (`legacy_pipelines`表)：
   - 关联具体的LLM模型UUID
   - 决定运行时使用哪个模型

4. **运行时调用**：
   ```
   ComfyUI工作流 → query.pipeline_config → ai.local-agent.model → 
   数据库查找模型 → RuntimeLLMModel → 实际API调用
   ```

### 为什么配置文件不够用？

LangBot设计为支持多模型、多Pipeline的企业级架构：
- 不同Pipeline可以使用不同的LLM模型
- 支持模型的热切换和管理
- 提供Web界面进行可视化配置
- 配置文件仅作为初始化参考

## 🎯 最佳实践

1. **使用Web界面管理**：推荐通过Web界面管理LLM模型和Pipeline配置
2. **定期备份配置**：备份 `data/langbot.db` 文件
3. **监控日志**：定期检查LLM调用日志，确保功能正常
4. **测试新配置**：每次修改后都要测试功能是否正常

## 🏗️ 架构更新说明

**重要**：在修复LLM配置问题的同时，我们还完成了runners目录的架构优化：

### 架构迁移完成
- ✅ **支持模块迁移**：将工具类从 `pkg/provider/runners/` 迁移到 `pkg/workers/shared/`
- ✅ **层级分离**：运行器与工具类职责分离
- ✅ **冗余清理**：删除废弃和重复的文件
- ✅ **Import更新**：所有相关import路径已更新

### 新架构结构
```
pkg/provider/runners/     # 🎯 纯运行器层级
├── base_agent.py         # ✅ 基础Agent类
├── comfyui_agent.py      # ✅ ComfyUI运行器
├── smart_hybrid_agent.py # ✅ 智能混合网关
└── localagent.py + 其他原生运行器

pkg/workers/shared/       # 🛠️ 共享工具层级
├── image_handlers/       # 📦 图片处理器
├── websocket/           # 📦 WebSocket客户端
└── sync/                # 📦 同步处理器
```

详细信息请参考：`docs/RUNNERS_ARCHITECTURE_MIGRATION_COMPLETE.md`

---

**最后更新**：2025-01-09
**维护者**：开发团队
**相关文档**：
- [ComfyUI集成指南](./COMFYUI_INTEGRATION.md)
- [Runners架构迁移完成报告](./RUNNERS_ARCHITECTURE_MIGRATION_COMPLETE.md)
- [ComfyUI LLM修复总结](./COMFYUI_LLM_FIX_SUMMARY.md)
