# 🔄 Kontext再次生成和修改提示词功能指南

本文档介绍Kontext工作流的两个重要新功能：**再次生成**和**修改提示词**。

## 🎯 功能概述

### ✅ 再次生成功能
**无需重新输入**：使用上一次的提示词和图片，重新生成图像。

### ✅ 修改提示词功能  
**保持图片不变**：仅修改提示词，保持上次上传的图片数据，重新生成。

---

## 🚀 使用方法

### 1️⃣ 再次生成

使用以下任意命令来重复上一次的生成：

**中文命令：**
```
再次生成
重新生成  
再来一次
再试一次
同样条件
```

**英文命令：**
```
again
repeat
regenerate
same conditions
try again
```

**💡 示例场景：**
```
用户: @kontext 将这个角色变成赛博朋克风格
系统: [生成完成]

用户: 再次生成
系统: 🔄 使用上次参数重新生成Kontext图片
      📝 提示词: 将这个角色变成赛博朋克风格
      🖼️ 图片数量: 1张
      🎨 开始生成...
```

### 2️⃣ 修改提示词

使用以下格式来修改提示词：

**中文格式：**
```
修改提示词为 [新提示词]
改成 [新提示词]
换成 [新提示词]
改为提示词 [新提示词]
换个提示词 [新提示词]
```

**英文格式：**
```
modify prompt to [新提示词]
change prompt to [新提示词]
update prompt to [新提示词]
new prompt [新提示词]
```

**💡 示例场景：**
```
用户: @kontext 将这个角色变成赛博朋克风格
系统: [生成完成]

用户: 修改提示词为 将这个角色变成古风武侠风格
系统: 🔄 修改提示词重新生成Kontext图片
      📝 新提示词: 将这个角色变成古风武侠风格
      🖼️ 使用上次的图片(1张)
      🎨 开始生成...
```

---

## 🔧 技术实现

### 📊 数据记录
- 系统自动记录每次Kontext生成请求
- 保存提示词、图片数据、时间戳等信息
- 支持1小时内的历史记录查找

### 🧠 智能识别
- **精确匹配**：完全匹配关键词
- **模糊匹配**：包含关键词且长度合理
- **正则表达式**：准确提取新提示词内容

### 🎨 无缝集成
- 与现有Kontext工作流完全兼容
- 保持智能比例判断功能
- 支持所有Kontext特性（LLM优化、参数调整等）

---

## ⚠️ 注意事项

### 📝 记录要求
- 需要有**上一次**的Kontext生成记录
- 记录**有效期为1小时**
- 只适用于**Kontext工作流**（@kontext命令）

### 🖼️ 图片要求  
- 必须有上次生成时的**图片数据**
- 系统会**自动保存**用户上传的图片
- 支持**1-3张图片**的重复使用

### 🔄 状态管理
- 每次使用功能都会**创建新的生成记录**
- 支持**链式修改**（可以多次修改提示词）
- **自动清理**过期的历史记录

---

## 🎯 使用技巧

### 💡 最佳实践

1. **先生成基础版本**
   ```
   @kontext 创建一个现代都市女性角色
   ```

2. **快速迭代优化**
   ```
   修改提示词为 创建一个现代都市女性角色，赛博朋克风格
   ```

3. **继续细化调整**
   ```
   修改提示词为 创建一个现代都市女性角色，赛博朋克风格，霓虹灯效果
   ```

4. **满意后可再次生成**
   ```
   再次生成
   ```

### 🚫 避免的情况

- ❌ 没有先使用@kontext命令就直接"再次生成"
- ❌ 超过1小时后尝试使用历史记录
- ❌ 在普通工作流后使用Kontext的再次生成功能

---

## 🆘 故障排除

### 常见错误信息

**"没有找到上次的Kontext生成记录"**
- 原因：没有历史记录或记录已过期
- 解决：先使用@kontext命令进行一次生成

**"上次生成没有图片记录"**  
- 原因：历史记录中缺少图片数据
- 解决：重新使用@kontext命令并上传图片

**"上次生成不是Kontext工作流"**
- 原因：上次使用的是普通工作流，不是Kontext
- 解决：先使用@kontext命令进行Kontext生成

---

## 🎉 总结

这两个新功能大大提升了Kontext的使用体验：

- **🔄 再次生成**：快速重新尝试，获得不同结果
- **📝 修改提示词**：无需重新上传图片，快速迭代创意

享受更流畅的AI图像创作体验吧！ 🎨✨ 

# Kontext重复生成修复指南

## 问题描述

用户在Kontext工作流完成后发送"again"指令时，会话状态显示不正确，出现以下问题：

```
📋 **Kontext会话状态**

**提示词**: Change the girl to be in an intense gunfight battle scene with cinematic widescreen framing while preserving her exact facial features, pose, and character identity
**图片数量**: 1/3
**状态**: completed

可以继续添加图片或发送 '开始' 指令
```

**核心问题**: 当用户有一个`completed`状态的Kontext会话时，发送"again"指令会被`is_repeat_command`检测并直接处理，**绕过了现有会话的状态检查**。

## 根因分析

### 原始处理流程（有问题）:
1. 用户发送"again"
2. `is_repeat_command("again")` 返回 `True`
3. 直接执行 `_handle_kontext_repeat_generation`
4. 创建**全新的session**，而不是处理现有的completed session

### 问题根源:
- **会话状态管理逻辑错误**: "again"指令绕过了现有session的状态检查
- **优先级处理错误**: repeat command检测优先级高于session interaction处理
- **重复session创建**: 每次"again"都创建新session，导致状态显示错误

## 修复方案

### 修复后的处理流程:
1. 用户发送"again" 
2. **首先检查是否有active session（包括completed状态）**
3. 如果有completed session，重置该session状态并复用
4. 如果没有active session，才使用repeat generation逻辑

### 具体修复代码:

在`pkg/provider/runners/kontext_image_handler.py`的`handle_kontext_session_interaction`方法中添加了特殊处理逻辑：

```python
# 检查是否为"again"类指令
if self.repeat_handler.is_repeat_command(user_text):
    # 如果有completed状态的session，重用它
    if (active_session and 
        hasattr(active_session, 'state') and 
        active_session.state.value == "completed"):
        
        # 重置session状态为ready_for_generation，保持现有的prompt和images
        from ...workers.kontext_workflow_manager import KontextSessionState
        active_session.state = KontextSessionState.READY_FOR_GENERATION
        active_session.update_timestamp()
        
        yield llm_entities.Message(
            role='assistant',
            content=(
                f"🔄 **重新生成Kontext图片**\n\n"
                f"📝 提示词: {active_session.prompt}\n"
                f"🖼️ 图片数量: {active_session.image_count}张\n\n"
                f"🎨 开始生成..."
            )
        )
        
        # 直接执行工作流，不需要重新trigger_generation
        active_session.state = KontextSessionState.GENERATING
        async for result_message in self._execute_kontext_workflow(active_session, query):
            yield result_message
        return
    else:
        # 没有active session或session不是completed状态，使用repeat generation逻辑
        async for message in self._handle_kontext_repeat_generation(user_id, chat_id, query):
            yield message
        return
```

## 修复效果

### 修复前:
- "again"指令创建新session
- 会话状态显示错误
- 用户困惑

### 修复后:
- "again"指令重用现有completed session
- 正确的状态反馈
- 立即开始重新生成

## 新增功能：引用消息提取

### 功能描述

新增了从引用消息中提取文本和图片的功能，让用户可以通过引用之前的消息来快速启动Kontext工作流。

### 使用场景

1. **引用图片进行编辑**
   - 用户引用一张图片
   - 输入：`kontext 将这个角色变成赛博朋克风格`
   - 系统自动提取引用的图片并启动工作流

2. **引用文本作为提示词**
   - 用户引用包含详细描述的消息
   - 输入：`kontext 再次生成`
   - 系统提取引用文本作为提示词

3. **引用消息的智能合并**
   - 引用的图片自动添加到工作流
   - 引用的文本与用户输入智能合并

### 实现特性

- **自动图片提取**: 从引用消息中提取所有图片
- **文本智能合并**: 将引用文本与用户输入合并为最终提示词
- **完整日志记录**: 详细记录提取过程，便于调试
- **错误容错**: 提取失败时优雅降级

### 代码实现

```python
async def handle_quoted_content_for_kontext(self, query: core_entities.Query, user_text: str) -> tuple[str, List[bytes]]:
    """
    智能处理引用消息内容，用于Kontext工作流
    返回: (最终提示词, 图片列表)
    """
    # 提取引用消息中的文本和图片
    quoted_text = self.extract_quoted_text(query)
    quoted_images = await self.extract_quoted_images(query)
    
    # 从用户消息中提取图片
    user_images = await self.extract_user_images(query)
    
    # 构建最终的提示词
    final_prompt = user_text
    if quoted_text:
        # 智能合并引用文本
        if len(user_text.strip()) < 20:
            final_prompt = f"{quoted_text} {user_text}".strip()
        else:
            final_prompt = f"{user_text} (参考: {quoted_text})"
    
    # 合并所有图片
    all_images = []
    if quoted_images:
        all_images.extend(quoted_images)
    if user_images:
        all_images.extend(user_images)
    
    return final_prompt, all_images
```

### 用户体验

#### 启动消息示例：
```
🎨 **Kontext工作流已启动**

**提示词**: 将这个角色变成赛博朋克风格 (参考: 一个穿着白色连衣裙的女孩站在花园里)
**已检测到图片**: 2张

✅ 已自动添加图片，发送 '开始' 指令生成图片！
```

## 测试验证

### 测试场景1: 有completed session时发送"again"
**期望结果**: 重用现有session，立即开始生成
**消息格式**:
```
🔄 **重新生成Kontext图片**

📝 提示词: [现有提示词]
🖼️ 图片数量: [现有图片数量]张

🎨 开始生成...
```

### 测试场景2: 没有active session时发送"again" 
**期望结果**: 使用repeat generation逻辑，从历史记录恢复

### 测试场景3: 引用消息启动Kontext
**期望结果**: 自动提取引用的图片和文本，智能合并提示词

### 测试场景4: 引用图片 + 用户图片
**期望结果**: 合并所有图片，显示正确的图片数量

## 相关任务编号

✅ **完成状态**: [kontext_again_fix]REV-03完成
📌 **验证点**:
- 错误处理检查: 已修复"again"指令绕过session状态检查的问题
- 会话状态管理: 已实现优先检查现有session状态的逻辑
- 用户体验优化: 已消除用户困惑，提供正确的状态反馈

✅ **完成状态**: [kontext_quote_extraction]DEV-04完成
📌 **验证点**:
- 业务逻辑实现: 已实现引用消息文本和图片提取功能
- 数据处理逻辑: 已实现智能提示词合并和图片去重
- 用户交互优化: 已提供清晰的反馈信息和自动化处理

## 技术债务记录

无新增技术债务，此次修复和新增功能均遵循了系统架构设计原则。 