# LoRA统一选择系统

## 概述

本文档描述了LoRA模型选择系统的统一改进，解决了建筑效果图等领域中LoRA模型重叠的问题，并提供了统一的用户干预机制。

## 核心问题

### 建筑效果图LoRA重叠问题
- **黑格建筑效果表现FLUX_new** - 触发词：建筑, architecture
- **比鲁斯商业建筑_V0.2** - 触发词：建筑, architecture  
- **ASTRA_Flux_OC_Vbeta-2** - 触发词：建筑效果图, architectural rendering
- **artchitecture_Flux1** - 触发词：建筑, architecture

当用户输入"建筑效果图"时，多个模型都可能被选中，导致功能重复和资源浪费。

## 解决方案

### 1. 简化用户干预参数

#### 新增参数
- `--lora+关键词` - 优先选择包含该关键词的模型
- `--lora-关键词` - 排除包含该关键词的模型

#### 标准语法
- `--lora 模型名 [权重]` - 指定使用特定LoRA模型
- `--lora no` - 强制不使用任何LoRA模型

### 2. 智能冲突解决

#### 冲突组定义
```python
conflict_groups = {
    'architecture_rendering': [
        '黑格建筑效果表现FLUX_new',
        '比鲁斯商业建筑_V0.2', 
        'ASTRA_Flux_OC_Vbeta-2',
        'artchitecture_Flux1'
    ],
    'detail_enhancement': [
        'detail_aidmafluxproultra-FLUX-v0.1',
        'add-detail-xl',
        'Detail Tweaker XL'
    ],
    # ... 更多冲突组
}
```

#### 选择优先级
1. **用户明确指定** (`--lora+关键词`) - 最高优先级 (+20.0分)
2. **优先级标志** (`is_priority: true`) - 中等优先级 (+2.0分)
3. **完全随机选择** - 同等条件下随机选择

### 3. 管线统一

#### Flux管线 ✅
- 支持所有新参数
- 使用智能冲突解决
- 完整的用户干预功能

#### Kontext管线 ✅ (新增)
- **按需使用LoRA**：只有用户明确指定LoRA参数时才使用
- 支持 `--lora+` 和 `--lora-` 参数
- 支持 `--lora 模型名` 指定特定模型

## 使用示例

### 基础用法
```bash
# 优先选择比鲁斯系列的建筑模型
建筑效果图 --lora+比鲁斯

# 排除黑格系列，从其他建筑模型中选择
建筑效果图 --lora-黑格

# 组合使用
现代建筑 --lora+ASTRA --lora-artchitecture

# 禁用LoRA
纯文本生成 --lora no
```

### 标准语法
```bash
# 指定特定LoRA模型
夜景建筑 --lora 某个夜景模型 0.9
室内设计 --lora 室内设计模型 0.7
```

## 技术实现

### 1. 参数解析器扩展
- 新增 `lora_prefer_keywords` 和 `lora_exclude_keywords` 字段
- 支持统一语法解析：`--lora dusk` → `use_dusk = True`

### 2. 智能选择算法
- 用户偏好加分机制
- 冲突组内随机选择
- 排除关键词过滤

### 3. 工作流集成
- Flux管线：通过 `_build_user_preferences` 方法
- Kontext管线：通过 `_build_user_preferences_from_query` 方法

## 预期效果

### 用户体验
- **简单直观**：统一的 `--lora` 参数语法
- **精确控制**：用户可以明确指定或排除特定模型
- **管线差异化**：Flux自动选择 vs Kontext按需使用

### 系统效果
- **结果多样性**：随机选择避免单调性
- **冲突解决**：自动处理重叠功能的模型
- **资源优化**：避免同时使用功能重复的模型

### 开发维护
- **代码统一**：减少重复逻辑
- **易于扩展**：新增冲突组和规则简单
- **测试友好**：统一的接口便于测试

## 配置示例

### LoRA模型配置
```json
{
  "name": "ASTRA_Flux_OC_Vbeta-2",
  "category": "architecture",
  "is_priority": true,
  "trigger_words": ["建筑效果图", "architectural rendering"]
}
```

### 用户偏好参数
```python
user_preferences = {
    'prefer_keywords': ['比鲁斯'],
    'exclude_keywords': ['黑格']
}
```

## 总结

这次统一改进解决了LoRA模型重叠的核心问题，提供了简洁的用户干预机制，并保持了系统的一致性和向后兼容性。用户可以更精确地控制LoRA选择，同时享受智能冲突解决带来的多样性。
