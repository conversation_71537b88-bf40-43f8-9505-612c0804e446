#!/bin/bash
# 安装WeChatPad自动清理服务

echo "🔧 安装WeChatPad自动清理服务"
echo "================================"

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请以root权限运行此脚本"
    echo "使用: sudo ./install_auto_cleanup.sh"
    exit 1
fi

# 复制服务文件到系统目录
echo "📋 安装系统服务..."
cp wechatpad-cleanup.service /etc/systemd/system/

# 重新加载systemd
echo "🔄 重新加载systemd..."
systemctl daemon-reload

# 启用服务
echo "✅ 启用自动清理服务..."
systemctl enable wechatpad-cleanup.service

# 检查服务状态
echo "📊 检查服务状态..."
systemctl status wechatpad-cleanup.service --no-pager

echo ""
echo "🎉 安装完成！"
echo ""
echo "💡 服务说明:"
echo "   - 服务名: wechatpad-cleanup.service"
echo "   - 启动时机: 系统启动后，Docker服务启动后"
echo "   - 功能: 清理WeChatPad的Redis消息队列数据"
echo ""
echo "🔧 管理命令:"
echo "   查看状态: systemctl status wechatpad-cleanup.service"
echo "   手动运行: systemctl start wechatpad-cleanup.service"
echo "   查看日志: journalctl -u wechatpad-cleanup.service"
echo "   禁用服务: systemctl disable wechatpad-cleanup.service"
echo ""
echo "⚠️ 注意:"
echo "   - 此服务会在每次系统启动时自动运行"
echo "   - 清理Redis中的消息队列数据，防止积压消息重发"
echo "   - 如需手动清理，可运行: ./restart_with_cleanup.sh"
