version: "3.3"
services:
  mysql_wx:
    # 指定容器的名称
    container_name: mysql_wxpad
    # 指定镜像和版本
    image: mysql:8.0
    ports:
      - "3306:3306"
    restart: always
    # 容器日志大小配置
    logging:
      driver: "json-file"
      options:
        max-size: "5g"
    environment:
      # 配置root密码
      MYSQL_ROOT_PASSWORD: test_mysql
      MYSQL_ROOT_HOST: '%'
      MYSQL_DATABASE: wechatpadpro
    volumes:
      # 挂载数据目录
      - "./mysql/data:/var/lib/mysql"
      # 挂载配置文件目录
      - "./mysql/config:/etc/mysql/conf.d"
    networks:
      - wechatpadpro_network

  #redis:
  redis_wx:
    # 指定容器的名称
    image: redis:alpine
    container_name: redis_wxpad
    restart: unless-stopped
    command: redis-server --requirepass test_redis
    environment:
      - REDIS_PASSWORD=test_redis
    volumes:
      - ./redis/data:/data
    ports:
      - "6381:6379"
    networks:
      - wechatpadpro_network

  wechatpad:
    # 指定容器的名称
    container_name: wechatpad
    # 指定镜像和版本
    image: alpine:latest
    ports:
      - "9090:8849"
    restart: always
    depends_on:
      - mysql_wx
      - redis_wx
    volumes:
      - ./app:/app # 映射数据目录，宿主机:容器
    # 指定工作目录
    working_dir: /app
    # 指定容器启动命令，执行./stay
    command: [ "/bin/sh", "-c", "chmod +x ./stay && ./stay" ]
    # 容器日志大小配置
    logging:
      driver: "json-file"
      options:
        max-size: "5g"
    # 设置时区
    environment:
      - TZ=Asia/Shanghai
      # 设置语言
      - LANG=zh_CN.UTF-8
      # 设置编码
      - LC_ALL=zh_CN.UTF-8
    networks:
      - wechatpadpro_network

networks:
  wechatpadpro_network:
    # ✅ 正确写法：键值对映射，键是网络名称，值是配置（可为空）
    # 可选的配置项（如 driver、自定义 IPAM 等）
    driver: bridge 