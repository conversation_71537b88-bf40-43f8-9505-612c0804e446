name: ai
label:
  en_US: AI Feature
  zh_Hans: AI 能力
stages:
  - name: runner
    label:
      en_US: Runner
      zh_Hans: 运行方式
    description:
      en_US: Strategy to call AI to process messages
      zh_Hans: 调用 AI 处理消息的方式
    config:
      - name: runner
        label:
          en_US: Runner
          zh_Hans: 运行器
        type: select
        required: true
        default: local-agent
        options:
          - name: local-agent
            label:
              en_US: Local Agent
              zh_Hans: 内置 Agent
          - name: dify-service-api
            label:
              en_US: Dify Service API
              zh_Hans: Dify 服务 API
          - name: dashscope-app-api
            label:
              en_US: Aliyun Dashscope App API
              zh_Hans: 阿里云百炼平台 API
          - name: n8n-service-api
            label:
              en_US: n8n Workflow API
              zh_Hans: n8n 工作流 API
          - name: comfyui-agent
            label:
              en_US: ComfyUI Agent
              zh_Hans: ComfyUI 图片生成 Agent
  - name: local-agent
    label:
      en_US: Local Agent
      zh_Hans: 内置 Agent
    description:
      en_US: Configure the embedded agent of the pipeline
      zh_Hans: 配置内置 Agent
    config:
      - name: model
        label:
          en_US: Model
          zh_Hans: 模型
        type: llm-model-selector
        required: true
      - name: max-round
        label:
          en_US: Max Round
          zh_Hans: 最大回合数
        description:
          en_US: The maximum number of previous messages that the agent can remember
          zh_Hans: 最大前文消息回合数
        type: integer
        required: true
        default: 10
      - name: prompt
        label:
          en_US: Prompt
          zh_Hans: 提示词
        description:
          en_US: The prompt of the agent
          zh_Hans: 除非您了解消息结构，否则请只使用 system 单提示词
        type: prompt-editor
        required: true
  - name: dify-service-api
    label:
      en_US: Dify Service API
      zh_Hans: Dify 服务 API
    description:
      en_US: Configure the Dify service API of the pipeline
      zh_Hans: 配置 Dify 服务 API
    config:
      - name: base-url
        label:
          en_US: Base URL
          zh_Hans: 基础 URL
        type: string
        required: true
      - name: app-type
        label:
          en_US: App Type
          zh_Hans: 应用类型
        type: select
        required: true
        default: chat
        options:
          - name: chat
            label:
              en_US: Chat
              zh_Hans: 聊天（包括Chatflow）
          - name: agent
            label:
              en_US: Agent
              zh_Hans: Agent
          - name: workflow
            label:
              en_US: Workflow
              zh_Hans: 工作流
      - name: api-key
        label:
          en_US: API Key
          zh_Hans: API 密钥
        type: string
        required: true
      - name: thinking-convert
        label:
          en_US: CoT Convert
          zh_Hans: 思维链转换策略
        type: select
        required: true
        default: plain
        options:
          - name: plain
            label:
              en_US: Convert to <think>...</think>
              zh_Hans: 转换成 <think>...</think>
          - name: original
            label:
              en_US: Original
              zh_Hans: 原始
          - name: remove
            label:
              en_US: Remove
              zh_Hans: 移除
  - name: dashscope-app-api
    label:
      en_US: Aliyun Dashscope App API
      zh_Hans: 阿里云百炼平台 API
    description:
      en_US: Configure the Aliyun Dashscope App API of the pipeline
      zh_Hans: 配置阿里云百炼平台 API
    config:
      - name: app-type
        label:
          en_US: App Type
          zh_Hans: 应用类型
        type: select
        required: true
        default: agent
        options:
          - name: agent
            label:
              en_US: Agent
              zh_Hans: Agent
          - name: workflow
            label:
              en_US: Workflow
              zh_Hans: 工作流
      - name: api-key
        label:
          en_US: API Key
          zh_Hans: API 密钥
        type: string
        required: true
      - name: app-id
        label:
          en_US: App ID
          zh_Hans: 应用 ID
        type: string
        required: true
      - name: references_quote
        label:
          en_US: References Quote
          zh_Hans: 引用文本
        description:
          en_US: The text prompt when the references are included
          zh_Hans: 包含引用资料时的文本提示
        type: string
        required: false
        default: '参考资料来自:'
  - name: n8n-service-api
    label:
      en_US: n8n Workflow API
      zh_Hans: n8n 工作流 API
    description:
      en_US: Configure the n8n workflow API of the pipeline
      zh_Hans: 配置 n8n 工作流 API
    config:
      - name: webhook-url
        label:
          en_US: Webhook URL
          zh_Hans: Webhook URL
        description:
          en_US: The webhook URL of the n8n workflow
          zh_Hans: n8n 工作流的 webhook URL
        type: string
        required: true
      - name: auth-type
        label:
          en_US: Authentication Type
          zh_Hans: 认证类型
        description:
          en_US: The authentication type for the webhook call
          zh_Hans: webhook 调用的认证类型
        type: select
        required: true
        default: 'none'
        options:
          - name: 'none'
            label:
              en_US: None
              zh_Hans: 无认证
          - name: 'basic'
            label:
              en_US: Basic Auth
              zh_Hans: 基本认证
          - name: 'jwt'
            label:
              en_US: JWT
              zh_Hans: JWT认证
          - name: 'header'
            label:
              en_US: Header Auth
              zh_Hans: 请求头认证
      - name: basic-username
        label:
          en_US: Username
          zh_Hans: 用户名
        description:
          en_US: The username for Basic Auth
          zh_Hans: 基本认证的用户名
        type: string
        required: false
        default: ''
      - name: basic-password
        label:
          en_US: Password
          zh_Hans: 密码
        description:
          en_US: The password for Basic Auth
          zh_Hans: 基本认证的密码
        type: string
        required: false
        default: ''
      - name: jwt-secret
        label:
          en_US: Secret
          zh_Hans: 密钥
        description:
          en_US: The secret for JWT authentication
          zh_Hans: JWT认证的密钥
        type: string
        required: false
        default: ''
      - name: jwt-algorithm
        label:
          en_US: Algorithm
          zh_Hans: 算法
        description:
          en_US: The algorithm for JWT authentication
          zh_Hans: JWT认证的算法
        type: string
        required: false
        default: 'HS256'
      - name: header-name
        label:
          en_US: Header Name
          zh_Hans: 请求头名称
        description:
          en_US: The header name for Header Auth
          zh_Hans: 请求头认证的名称
        type: string
        required: false
        default: ''
      - name: header-value
        label:
          en_US: Header Value
          zh_Hans: 请求头值
        description:
          en_US: The header value for Header Auth
          zh_Hans: 请求头认证的值
        type: string
        required: false
        default: ''
      - name: timeout
        label:
          en_US: Timeout
          zh_Hans: 超时时间
        description:
          en_US: The timeout in seconds for the webhook call
          zh_Hans: webhook 调用的超时时间（秒）
        type: integer
        required: false
        default: 120
      - name: output-key
        label:
          en_US: Output Key
          zh_Hans: 输出键名
        description:
          en_US: The key name of the output in the webhook response
          zh_Hans: webhook 响应中输出内容的键名
        type: string
        required: false
        default: 'response'
  - name: comfyui-agent
    label:
      en_US: ComfyUI Agent
      zh_Hans: ComfyUI 图片生成 Agent
    description:
      en_US: Configure the ComfyUI image generation agent
      zh_Hans: 配置 ComfyUI 图片生成 Agent
    config:
      - name: model
        label:
          en_US: Model
          zh_Hans: 模型
        type: llm-model-selector
        required: true
      - name: api-url
        label:
          en_US: API URL
          zh_Hans: API 地址
        description:
          en_US: The ComfyUI API endpoint URL
          zh_Hans: ComfyUI API 端点地址
        type: string
        required: true
        default: 'http://localhost:8188'
      - name: timeout
        label:
          en_US: Timeout
          zh_Hans: 超时时间
        description:
          en_US: The timeout in seconds for image generation
          zh_Hans: 图片生成的超时时间（秒）
        type: integer
        required: false
        default: 120
      - name: workflow-path
        label:
          en_US: Workflow Path
          zh_Hans: 工作流路径
        description:
          en_US: The directory path containing workflow files
          zh_Hans: 包含工作流文件的目录路径
        type: string
        required: false
        default: 'workflows'
      - name: default-workflow
        label:
          en_US: Default Workflow
          zh_Hans: 默认工作流
        description:
          en_US: The default workflow file name
          zh_Hans: 默认工作流文件名
        type: string
        required: false
        default: 'default_workflow.json'
