#!/usr/bin/env python3
"""
YAML到JSON触发词迁移工具

功能：
1. 从YAML文件中提取优质触发词
2. 更新JSON文件中对应模型的触发词
3. 标记为人工维护状态
4. 移除YAML文件

作者：二次开发标识
"""

import json
import yaml
import os
import sys
from pathlib import Path
from typing import Dict, List

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from pkg.workers.shared.shared_lora_manager import LoraCategory


class YAMLToJSONMigrator:
    """YAML到JSON迁移工具"""
    
    def __init__(self):
        self.yaml_file = "config/lora_models.yaml"
        self.json_file = "config/lora_models.json"
        self.backup_file = "config/lora_models_before_migration.json"
    
    def create_backup(self):
        """创建JSON文件备份"""
        if os.path.exists(self.json_file):
            with open(self.json_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            with open(self.backup_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 已创建备份: {self.backup_file}")
    
    def load_yaml_config(self) -> Dict:
        """加载YAML配置"""
        if not os.path.exists(self.yaml_file):
            print(f"❌ YAML文件不存在: {self.yaml_file}")
            return {}
        
        with open(self.yaml_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def load_json_config(self) -> Dict:
        """加载JSON配置"""
        with open(self.json_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def save_json_config(self, config: Dict):
        """保存JSON配置"""
        with open(self.json_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def extract_yaml_models(self, yaml_config: Dict) -> Dict[str, Dict]:
        """从YAML中提取模型信息"""
        models = {}
        
        for category_name, category_data in yaml_config.items():
            # 跳过全局配置和工作流预设
            if category_name in ['global_config', 'workflow_presets']:
                continue
            
            if isinstance(category_data, dict) and 'models' in category_data:
                for model_data in category_data['models']:
                    model_name = model_data['name']
                    
                    # 映射分类
                    category_mapping = {
                        'architecture': 'architecture',
                        'billus_series': 'architecture',
                        'anime': 'anime',
                        'utility': 'other',
                        'fast_generation': 'detail',
                        'special_effects': 'style',
                        'sdxl_specific': 'architecture',
                        'artistic_styles': 'style'
                    }
                    
                    category = category_mapping.get(category_name, 'other')
                    
                    models[model_name] = {
                        'trigger_words': model_data.get('tags', []),
                        'description': model_data.get('description', ''),
                        'category': category,
                        'weight': model_data.get('strength', 0.8),
                        'model_type': model_data.get('model_type', 'flux'),
                        'is_active': model_data.get('enabled', True)
                    }
        
        return models
    
    def enhance_trigger_words(self, original_tags: List[str], model_name: str, category: str) -> List[str]:
        """增强触发词，添加更多相关词汇"""
        enhanced_words = original_tags.copy()
        
        # 根据分类添加通用触发词
        category_enhancements = {
            'architecture': ['建筑', 'architecture', '设计', '渲染'],
            'anime': ['动漫', 'anime', '二次元', '卡通'],
            'detail': ['细节', 'detail', '增强', '高清'],
            'style': ['风格', 'style', '艺术', '效果'],
            'portrait': ['人像', 'portrait', '肖像', '人物'],
            'landscape': ['风景', 'landscape', '自然', '景观']
        }
        
        base_words = category_enhancements.get(category, [])
        
        # 添加基础词汇（如果不存在）
        for word in base_words:
            if word not in enhanced_words:
                enhanced_words.append(word)
        
        # 根据模型名称添加特定词汇
        name_lower = model_name.lower()
        
        # 建筑相关
        if any(keyword in name_lower for keyword in ['astra', '建筑', 'architecture']):
            specific_words = ['专业', '效果图', '渲染']
            for word in specific_words:
                if word not in enhanced_words:
                    enhanced_words.append(word)
        
        # 动漫相关
        if any(keyword in name_lower for keyword in ['niji', 'anime', '动漫']):
            specific_words = ['二次元', 'manga', '漫画']
            for word in specific_words:
                if word not in enhanced_words:
                    enhanced_words.append(word)
        
        # 快速生成相关
        if any(keyword in name_lower for keyword in ['hyper', 'turbo', 'lightning', 'fast']):
            specific_words = ['快速', 'fast', '加速', 'speed']
            for word in specific_words:
                if word not in enhanced_words:
                    enhanced_words.append(word)
        
        # 去重并限制数量
        enhanced_words = list(dict.fromkeys(enhanced_words))  # 保持顺序去重
        return enhanced_words[:8]  # 限制最多8个触发词
    
    def migrate_models(self):
        """执行迁移"""
        print("🔄 开始迁移YAML触发词到JSON...")
        
        # 创建备份
        self.create_backup()
        
        # 加载配置
        yaml_config = self.load_yaml_config()
        if not yaml_config:
            return
        
        json_config = self.load_json_config()
        
        # 提取YAML模型信息
        yaml_models = self.extract_yaml_models(yaml_config)
        print(f"📋 从YAML中提取了 {len(yaml_models)} 个模型的优质配置")
        
        # 更新JSON中的对应模型
        updated_count = 0
        created_count = 0
        
        # 创建JSON模型名称映射
        json_models = {model['name']: model for model in json_config['models']}
        
        for yaml_model_name, yaml_model_data in yaml_models.items():
            if yaml_model_name in json_models:
                # 更新现有模型
                json_model = json_models[yaml_model_name]
                
                # 增强触发词
                enhanced_triggers = self.enhance_trigger_words(
                    yaml_model_data['trigger_words'],
                    yaml_model_name,
                    yaml_model_data['category']
                )
                
                # 更新字段
                json_model['trigger_words'] = enhanced_triggers
                json_model['category'] = yaml_model_data['category']
                json_model['weight'] = yaml_model_data['weight']
                
                # 更新描述（如果YAML中有更好的描述）
                if (yaml_model_data['description'] and 
                    not yaml_model_data['description'].startswith('自动发现')):
                    json_model['description'] = yaml_model_data['description']
                
                # 标记为人工维护
                json_model['manually_maintained'] = True
                json_model['last_manual_update'] = "2025-01-17"
                json_model['migrated_from_yaml'] = True
                
                updated_count += 1
                print(f"✅ 更新: {yaml_model_name} -> {enhanced_triggers}")
            
            else:
                # 创建新模型条目（如果JSON中不存在）
                print(f"⚠️ JSON中未找到模型: {yaml_model_name}")
                # 这里可以选择创建新条目，但通常不需要
        
        # 保存更新后的JSON配置
        self.save_json_config(json_config)
        
        print(f"✅ 迁移完成！更新了 {updated_count} 个模型")
        print(f"📁 备份文件: {self.backup_file}")
    
    def remove_yaml_file(self):
        """移除YAML文件"""
        if os.path.exists(self.yaml_file):
            # 重命名为备份文件而不是直接删除
            backup_yaml = "config/lora_models_deprecated.yaml"
            os.rename(self.yaml_file, backup_yaml)
            print(f"✅ YAML文件已重命名为: {backup_yaml}")
        else:
            print("ℹ️ YAML文件不存在，无需移除")
    
    def show_migration_summary(self):
        """显示迁移摘要"""
        json_config = self.load_json_config()
        
        migrated_models = [
            model for model in json_config['models'] 
            if model.get('migrated_from_yaml', False)
        ]
        
        print(f"\n📊 迁移摘要:")
        print(f"   迁移模型数量: {len(migrated_models)}")
        
        # 按分类统计
        category_stats = {}
        for model in migrated_models:
            category = model['category']
            category_stats[category] = category_stats.get(category, 0) + 1
        
        print(f"   分类分布:")
        for category, count in category_stats.items():
            print(f"     {category}: {count} 个")
        
        print(f"\n🎯 优质触发词示例:")
        for model in migrated_models[:5]:  # 显示前5个
            print(f"   {model['name']}: {model['trigger_words']}")


def main():
    """主函数"""
    migrator = YAMLToJSONMigrator()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--dry-run":
        print("🔍 模拟模式：只显示将要执行的操作")
        # 这里可以添加模拟模式的逻辑
        return
    
    try:
        print("🚀 开始YAML到JSON迁移...")
        
        # 执行迁移
        migrator.migrate_models()
        
        # 显示摘要
        migrator.show_migration_summary()
        
        # 询问是否移除YAML文件
        response = input("\n❓ 是否移除YAML文件？(y/n): ").strip().lower()
        if response == 'y':
            migrator.remove_yaml_file()
        
        print("\n🎉 迁移完成！")
        print("💡 建议运行以下命令验证结果:")
        print("   python maintain_lora_triggers.py validate")
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        print("💡 可以使用备份文件恢复:")
        print(f"   cp {migrator.backup_file} {migrator.json_file}")


if __name__ == "__main__":
    main()
