#!/usr/bin/env python3
"""
[二次开发] LoRA模型分析工具

手动分析和标注LoRA模型的真实用途，提供准确的分类和触发词

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：手动分析LoRA模型，提供准确的分类和触发词
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：DEV-05 配置模块实现
- 依赖关系：依赖shared_lora_manager和现有配置文件
"""

import asyncio
import sys
import os
import json
import argparse
import re
from pathlib import Path
from typing import List, Dict, Set, Optional
from datetime import datetime

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from pkg.workers.shared.shared_lora_manager import SharedLoraManager, LoraCategory


class LoraModelAnalyzer:
    """LoRA模型分析器"""
    
    def __init__(self):
        self.config_file = "config/lora_models.json"
        self.manager = SharedLoraManager()
        self.manager.initialize()
        
        # 预定义的模型用途模板
        self.model_templates = {
            "建筑渲染": {
                "category": LoraCategory.ARCHITECTURE,
                "trigger_words": ["建筑", "architecture", "building", "渲染", "rendering"],
                "description": "专业建筑效果图渲染模型",
                "is_priority": True
            },
            "室内设计": {
                "category": LoraCategory.ARCHITECTURE,
                "trigger_words": ["室内", "interior", "房间", "room", "家具", "furniture"],
                "description": "室内设计和装饰模型",
                "is_priority": False
            },
            "人像美化": {
                "category": LoraCategory.PORTRAIT,
                "trigger_words": ["人像", "portrait", "美女", "beauty", "面部", "face"],
                "description": "人像美化和优化模型",
                "is_priority": True
            },
            "风景摄影": {
                "category": LoraCategory.LANDSCAPE,
                "trigger_words": ["风景", "landscape", "自然", "nature", "摄影", "photography"],
                "description": "自然风景和景观摄影模型",
                "is_priority": False
            },
            "动漫风格": {
                "category": LoraCategory.ANIME,
                "trigger_words": ["动漫", "anime", "二次元", "manga", "卡通", "cartoon"],
                "description": "动漫和二次元风格模型",
                "is_priority": False
            },
            "细节增强": {
                "category": LoraCategory.DETAIL,
                "trigger_words": ["细节", "detail", "高清", "high quality", "增强", "enhance"],
                "description": "图像细节和质量增强模型",
                "is_priority": True
            },
            "写实风格": {
                "category": LoraCategory.STYLE,
                "trigger_words": ["写实", "realistic", "真实", "photorealistic", "照片", "photo"],
                "description": "写实和照片级渲染模型",
                "is_priority": True
            },
            "艺术风格": {
                "category": LoraCategory.STYLE,
                "trigger_words": ["艺术", "artistic", "风格", "style", "创意", "creative"],
                "description": "艺术风格和创意渲染模型",
                "is_priority": False
            }
        }
    
    def analyze_model(self, model_name: str) -> Dict:
        """分析单个模型"""
        if model_name not in self.manager.lora_models:
            print(f"❌ 模型不存在: {model_name}")
            return {}
        
        model = self.manager.lora_models[model_name]
        
        print(f"🔍 分析模型: {model.name}")
        print(f"📁 文件: {model.filename}")
        print(f"🏷️ 当前分类: {model.category.value}")
        print(f"🏷️ 当前触发词: {', '.join(model.trigger_words)}")
        print(f"📝 当前描述: {model.description}")
        print(f"⭐ 优先级: {'是' if model.is_priority else '否'}")
        
        # 分析文件名特征
        features = self._analyze_filename_features(model.filename)
        print(f"🔍 文件名特征: {features}")
        
        # 提供建议
        suggestions = self._generate_suggestions(model, features)
        print(f"💡 分析建议:")
        for suggestion in suggestions:
            print(f"   - {suggestion}")
        
        return {
            "model": model,
            "features": features,
            "suggestions": suggestions
        }
    
    def _analyze_filename_features(self, filename: str) -> List[str]:
        """分析文件名特征"""
        features = []
        name_lower = filename.lower()
        
        # 检查模型类型
        if "flux" in name_lower:
            features.append("Flux专用模型")
        if "sdxl" in name_lower:
            features.append("SDXL模型")
        if "sd15" in name_lower or "sd1.5" in name_lower:
            features.append("SD1.5模型")
        
        # 检查用途关键词
        if any(word in name_lower for word in ["detail", "enhance", "quality", "ultra"]):
            features.append("细节增强")
        if any(word in name_lower for word in ["realistic", "photo", "真实"]):
            features.append("写实风格")
        if any(word in name_lower for word in ["anime", "manga", "动漫"]):
            features.append("动漫风格")
        if any(word in name_lower for word in ["architecture", "building", "建筑"]):
            features.append("建筑相关")
        if any(word in name_lower for word in ["portrait", "face", "人像"]):
            features.append("人像相关")
        if any(word in name_lower for word in ["landscape", "nature", "风景"]):
            features.append("风景相关")
        
        # 检查特殊功能
        if any(word in name_lower for word in ["lightning", "fast", "quick", "8step"]):
            features.append("快速生成")
        if any(word in name_lower for word in ["depth", "shallow"]):
            features.append("深度控制")
        if any(word in name_lower for word in ["canny", "edge"]):
            features.append("边缘检测")
        
        return features
    
    def _generate_suggestions(self, model, features: List[str]) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        # 基于特征的建议
        if "细节增强" in features and model.category != LoraCategory.DETAIL:
            suggestions.append("建议分类为 DETAIL (细节增强)")
        
        if "写实风格" in features and model.category != LoraCategory.STYLE:
            suggestions.append("建议分类为 STYLE (风格类)")
        
        if "动漫风格" in features and model.category != LoraCategory.ANIME:
            suggestions.append("建议分类为 ANIME (动漫类)")
        
        if "建筑相关" in features and model.category != LoraCategory.ARCHITECTURE:
            suggestions.append("建议分类为 ARCHITECTURE (建筑类)")
        
        if "人像相关" in features and model.category != LoraCategory.PORTRAIT:
            suggestions.append("建议分类为 PORTRAIT (人像类)")
        
        if "风景相关" in features and model.category != LoraCategory.LANDSCAPE:
            suggestions.append("建议分类为 LANDSCAPE (风景类)")
        
        # 触发词建议
        if len(model.trigger_words) < 3:
            suggestions.append("触发词数量较少，建议添加更多相关关键词")
        
        if "detail" in model.filename.lower() and "细节" not in model.trigger_words:
            suggestions.append("建议添加中文触发词 '细节'")
        
        if "realistic" in model.filename.lower() and "写实" not in model.trigger_words:
            suggestions.append("建议添加中文触发词 '写实'")
        
        # 优先级建议
        if "细节增强" in features and not model.is_priority:
            suggestions.append("建议设置为优先级模型（细节增强模型通用性强）")
        
        if "快速生成" in features and not model.is_priority:
            suggestions.append("建议设置为优先级模型（快速生成模型实用性强）")
        
        return suggestions
    
    def apply_template(self, model_name: str, template_name: str) -> bool:
        """应用预定义模板"""
        if model_name not in self.manager.lora_models:
            print(f"❌ 模型不存在: {model_name}")
            return False
        
        if template_name not in self.model_templates:
            print(f"❌ 模板不存在: {template_name}")
            print(f"可用模板: {', '.join(self.model_templates.keys())}")
            return False
        
        model = self.manager.lora_models[model_name]
        template = self.model_templates[template_name]
        
        # 应用模板
        model.category = template["category"]
        model.trigger_words = template["trigger_words"].copy()
        model.description = template["description"]
        model.is_priority = template["is_priority"]
        
        print(f"✅ 已应用模板 '{template_name}' 到模型 '{model_name}'")
        print(f"🏷️ 新分类: {model.category.value}")
        print(f"🏷️ 新触发词: {', '.join(model.trigger_words)}")
        print(f"📝 新描述: {model.description}")
        print(f"⭐ 新优先级: {'是' if model.is_priority else '否'}")
        
        return True
    
    def custom_update(self, model_name: str, **kwargs) -> bool:
        """自定义更新模型"""
        if model_name not in self.manager.lora_models:
            print(f"❌ 模型不存在: {model_name}")
            return False
        
        model = self.manager.lora_models[model_name]
        
        # 支持更新的字段
        valid_fields = ['category', 'trigger_words', 'description', 'is_priority', 'weight']
        
        for field, value in kwargs.items():
            if field in valid_fields:
                if field == 'category' and isinstance(value, str):
                    # 字符串转枚举
                    try:
                        value = LoraCategory(value)
                    except ValueError:
                        print(f"❌ 无效的分类: {value}")
                        continue
                
                setattr(model, field, value)
                print(f"✅ 更新 {field}: {value}")
            else:
                print(f"⚠️ 忽略无效字段: {field}")
        
        return True
    
    def save_changes(self) -> bool:
        """保存更改到配置文件"""
        try:
            # 读取现有配置
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 更新模型信息
            for model in self.manager.lora_models.values():
                # 找到对应的配置项
                for config_model in config["models"]:
                    if config_model["name"] == model.name:
                        config_model["category"] = model.category.value
                        config_model["trigger_words"] = model.trigger_words
                        config_model["description"] = model.description
                        config_model["is_priority"] = model.is_priority
                        config_model["weight"] = model.weight
                        break
            
            # 写回配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print("✅ 更改已保存到配置文件")
            return True
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False
    
    def list_templates(self):
        """列出可用模板"""
        print("📋 可用模板:")
        for template_name, template in self.model_templates.items():
            print(f"  🏷️ {template_name}")
            print(f"    分类: {template['category'].value}")
            print(f"    触发词: {', '.join(template['trigger_words'])}")
            print(f"    描述: {template['description']}")
            print(f"    优先级: {'是' if template['is_priority'] else '否'}")
            print()


async def main():
    parser = argparse.ArgumentParser(description="LoRA模型分析工具")
    parser.add_argument("command", choices=["analyze", "template", "update", "templates"], 
                       help="操作命令")
    parser.add_argument("model_name", nargs="?", help="模型名称")
    parser.add_argument("--template", help="模板名称")
    parser.add_argument("--category", help="分类")
    parser.add_argument("--trigger-words", help="触发词（逗号分隔）")
    parser.add_argument("--description", help="描述")
    parser.add_argument("--priority", action="store_true", help="设置为优先级")
    parser.add_argument("--weight", type=float, help="权重")
    parser.add_argument("--save", action="store_true", help="保存更改")
    
    args = parser.parse_args()
    
    try:
        analyzer = LoraModelAnalyzer()
        
        if args.command == "analyze":
            if not args.model_name:
                print("❌ 请指定要分析的模型名称")
                return
            analyzer.analyze_model(args.model_name)
        
        elif args.command == "templates":
            analyzer.list_templates()
        
        elif args.command == "template":
            if not args.model_name or not args.template:
                print("❌ 请指定模型名称和模板名称")
                return
            analyzer.apply_template(args.model_name, args.template)
            if args.save:
                analyzer.save_changes()
        
        elif args.command == "update":
            if not args.model_name:
                print("❌ 请指定要更新的模型名称")
                return
            
            # 构建更新参数
            update_kwargs = {}
            if args.category:
                update_kwargs['category'] = args.category
            if args.trigger_words:
                update_kwargs['trigger_words'] = [w.strip() for w in args.trigger_words.split(',')]
            if args.description:
                update_kwargs['description'] = args.description
            if args.priority:
                update_kwargs['is_priority'] = True
            if args.weight:
                update_kwargs['weight'] = args.weight
            
            if update_kwargs:
                analyzer.custom_update(args.model_name, **update_kwargs)
            else:
                print("❌ 请指定要更新的字段")
                return
        
        # 保存更改（仅在update命令中）
        if args.command == "update" and args.save:
            analyzer.save_changes()
    
    except KeyboardInterrupt:
        print("\n⏹️ 操作已取消")
    except Exception as e:
        print(f"❌ 操作失败: {e}")


if __name__ == "__main__":
    asyncio.run(main()) 