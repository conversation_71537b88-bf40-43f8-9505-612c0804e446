# Redis 内存管理使用指南

## 概述

本文档提供了系统Redis内存管理功能的详细使用说明。新版本引入的Redis内存自动管理机制解决了长期存在的内存泄漏问题，确保系统长时间稳定运行。本指南适用于系统管理员和开发人员。

## 自动内存管理功能

系统已实现全自动的Redis内存管理，主要包括以下功能：

### 1. 自适应内存限制

系统会根据服务器可用内存自动设置合理的Redis内存上限：

- 默认分配服务器物理内存的60%给Redis
- 最小限制为512MB，最大限制为8GB
- 会根据内存负载情况自动调整设置

### 2. 智能数据分级

根据数据重要性和使用频率，自动设置不同的过期时间：

| 数据类型 | 示例 | 保留时间 |
|---------|------|---------|
| 重要业务数据 | 消息同步记录、HTTP同步数据 | 3天 |
| 普通数据 | 一般业务数据 | 1天 |
| 临时数据 | 临时缓存、验证码等 | 2小时 |

### 3. 多级保护机制

系统实现了多级内存保护策略：

- **标准模式**（内存使用<80%）
  - 每30分钟检查一次内存使用情况
  - 自动为未设置过期时间的键设置合理的过期时间

- **警戒模式**（内存使用>80%）
  - 增加检查频率至每5分钟一次
  - 主动清理即将过期的键（TTL<1小时）

- **紧急模式**（内存使用>90%）
  - 立即执行紧急清理操作
  - 优先删除大体积键和非关键数据
  - 增加内存淘汰力度，确保系统稳定

## API接口使用

系统提供了REST API接口用于监控和管理Redis内存：

### 获取内存统计信息

```
GET /api/v1/other/RedisMemory?action=stats
```

**响应示例：**
```json
{
  "code": 0,
  "msg": "获取Redis内存统计信息成功",
  "data": {
    "used_memory": "1073741824",
    "used_memory_human": "1.00G",
    "used_memory_rss": "1181116416",
    "used_memory_rss_human": "1.10G",
    "maxmemory": "2147483648",
    "maxmemory_human": "2.00G",
    "maxmemory_policy": "allkeys-lru",
    "memory_usage_percent": "50.00%",
    "adaptive_mode": "true",
    "current_check_interval": "1800000000000"
  }
}
```

### 手动触发内存清理

```
GET /api/v1/other/RedisMemory?action=cleanup
```

**响应示例：**
```json
{
  "code": 0,
  "msg": "已触发Redis大键清理",
  "data": {
    "cleaned_keys": 15
  }
}
```

### 控制自适应内存模式

启用自适应模式（默认开启）：
```
GET /api/v1/other/RedisMemory?action=adaptive&enabled=true
```

禁用自适应模式（使用固定内存限制）：
```
GET /api/v1/other/RedisMemory?action=adaptive&enabled=false
```

## MCP管理界面使用

MCP后台提供了图形化的Redis内存管理界面，位于"系统管理" -> "Redis管理"菜单下：

### 主要功能

1. **实时监控面板**
   - 显示内存使用率、键数量等关键指标
   - 提供内存使用趋势图表

2. **内存管理操作**
   - 一键清理大键
   - 调整内存限制和策略
   - 启用/禁用自适应模式

3. **数据分析工具**
   - 大键分析报告
   - 内存占用分布统计
   - 访问频率热点图

## 最佳实践建议

1. **系统配置建议**
   - 对于4GB内存的服务器，建议Redis最大内存设置为2GB
   - 对于8GB以上内存的服务器，建议设置为总内存的60%
   - 建议使用默认的自适应模式，让系统自动优化内存配置

2. **监控与维护**
   - 定期查看内存使用状况，关注使用率趋势
   - 在业务高峰期前主动执行一次清理操作
   - 配置系统监控，当内存使用率达到阈值时发出警报

3. **业务开发注意事项**
   - 避免将Redis用作永久存储，合理设置数据过期时间
   - 控制存储对象大小，避免单个键超过100KB
   - 使用哈希结构存储关联数据，减少键数量

## 故障排查指南

如遇Redis内存相关问题，可参考以下排查步骤：

1. **内存使用过高**
   - 通过API或MCP查看内存统计信息
   - 执行`cleanup`操作尝试释放内存
   - 检查是否有特定业务产生大量数据

2. **系统性能下降**
   - 检查Redis响应时间，通常与内存压力相关
   - 查看内存碎片率，高碎片率可能需要重启Redis
   - 分析内存使用趋势，评估是否需要增加内存配置

3. **日志中出现OOM错误**
   - 立即执行紧急清理操作
   - 临时调高内存限制（如有可用物理内存）
   - 重启Redis服务（最后手段）

## 联系支持

如遇到无法解决的问题，请联系技术支持：

- 技术支持邮箱：<EMAIL>
- 在线支持：https://support.example.com
- 紧急联系电话：400-123-4567 