# 微信功能使用说明

## 目录
- [基本说明](#基本说明)
- [自动抢红包功能](#自动抢红包功能)
- [其他功能开关命令](#其他功能开关命令)
- [群管理命令](#群管理命令)
- [管理员特殊命令](#管理员特殊命令)
- [常见问题](#常见问题)

## 基本说明

本文档详细介绍了微信机器人系统的各种功能命令及其使用方法。

### 命令发送位置

**除特殊说明外，所有命令均需在【文件传输助手】中发送。**

在微信中，点击"通讯录" → 找到并点击"文件传输助手" → 在消息输入框中输入相应命令 → 发送。

### 命令格式说明

1. 命令区分大小写，必须严格按照格式发送
2. 大部分命令（如A101、A102等）是切换式的，每次发送都会在开启/关闭状态间切换
3. 带有参数的命令（如A103时间#3）需要按照指定格式输入

## 自动抢红包功能

### A101: 开启/关闭自动抢红包

**功能**: 控制自动抢红包功能的开启和关闭

**使用方法**:
1. 在【文件传输助手】中发送: `A101`
2. 系统会返回"自动抢红包已开启"或"自动抢红包已关闭"

**说明**:
- 默认会抢所有群的红包
- 如果某些群不想自动抢红包，只需将该群聊置顶
- 置顶群聊方法: 长按群聊 → 选择"置顶聊天"

### A102: 开启/关闭过滤测挂红包

**功能**: 控制是否过滤测试或诈骗红包

**使用方法**:
1. 在【文件传输助手】中发送: `A102`
2. 系统会返回"已设置抢测挂红包"或"已设置不抢测挂红包"

**说明**:
- 开启后会过滤包含"测"、"挂"、"cg"、"死"、"罚"等关键词的红包
- 建议开启，可避免被测试红包或诈骗红包影响

### A103: 获取当前设置的延迟抢红包时间

**功能**: 查看当前设置的延迟抢红包时间

**使用方法**:
1. 在【文件传输助手】中发送: `A103`
2. 系统会返回"当前设置的延迟抢红包时间为:X秒"

### A103时间#X: 设置延迟抢红包时间

**功能**: 设置延迟多少秒后抢红包

**使用方法**:
1. 在【文件传输助手】中发送: `A103时间#3` (数字可以自定义，表示延迟秒数)
2. 系统会返回"设置成功"

**说明**:
- 建议设置3-5秒的延迟，避免总是第一个抢到显得太过机械化
- 延迟时间不宜设置太长，以免错过抢红包时机

### A104: 开启/关闭抢到红包后自动感谢

**功能**: 控制是否在抢到红包后自动发送感谢消息

**使用方法**:
1. 在【文件传输助手】中发送: `A104`
2. 系统会返回"已设置自动感谢红包(抢红包后9秒回自动回复感谢)"或"已设置不自动感谢红包"

## 其他功能开关命令

### A201: 开启/关闭多开推送到bark

**功能**: 控制是否将消息推送到iOS设备的Bark应用

**使用方法**:
1. 在【文件传输助手】中发送: `A201`
2. 系统会返回"多开推送已开启"或"多开推送已关闭"

**说明**:
- 需要先在iOS设备上安装Bark应用(App Store可下载)
- 适合多开微信时使用，可将消息推送到手机

### A201绑定#devicetoken: 设置bark设备token

**功能**: 设置Bark应用的设备标识

**使用方法**:
1. 在【文件传输助手】中发送: `A201绑定#你的设备token`
2. 系统会返回"设置成功"

**说明**:
- devicetoken需要从Bark应用中获取
- 打开Bark应用，复制URL中的设备ID部分

### A301: 开启/关闭同意添加好友后自动回复

**功能**: 控制是否在同意好友请求后自动发送回复消息

**使用方法**:
1. 在【文件传输助手】中发送: `A301`
2. 系统会返回"同意好友后自动回复已开启"或"同意好友后自动回复已关闭"

### A301#回复内容: 设置添加好友同意后自动回复的内容

**功能**: 设置自动回复的具体内容

**使用方法**:
1. 在【文件传输助手】中发送: `A301#你好，很高兴认识你！`
2. 系统会返回"设置成功"

**说明**:
- 可以设置任意文本内容作为自动回复
- 建议设置个性化的回复内容，增加真实感

### A401: 开启/关闭自动点赞朋友圈

**功能**: 控制是否自动给好友的朋友圈点赞

**使用方法**:
1. 在【文件传输助手】中发送: `A401`
2. 系统会返回"已开启自动点赞朋友圈"或"已关闭自动点赞朋友圈"

### A501: 保存所有群聊到通讯录

**功能**: 将所有群聊保存到通讯录中

**使用方法**:
1. 在【文件传输助手】中发送: `A501`

**说明**:
- 执行后，所有群聊将显示在通讯录中
- 便于快速查找和管理群聊

### A502: 取消保存通讯录中的所有群聊

**功能**: 将所有群聊从通讯录中移除

**使用方法**:
1. 在【文件传输助手】中发送: `A502`

### A601: 开启/关闭消息防撤回功能

**功能**: 控制是否启用消息防撤回功能

**使用方法**:
1. 在【文件传输助手】中发送: `A601`
2. 系统会返回"消息防撤回已开启"或"消息防撤回已关闭"

**说明**:
- 开启后，即使对方撤回消息，你仍能看到原始消息内容
- 适用于所有聊天场景，包括私聊和群聊

### A801: 开启/关闭自动通过验证

**功能**: 控制是否自动接受好友请求

**使用方法**:
1. 在【文件传输助手】中发送: `A801`
2. 系统会返回"自动通过验证已开启"或"自动通过验证已关闭"

**说明**:
- 开启后，所有好友请求将被自动接受
- 谨慎使用此功能，可能导致接受不必要的好友请求

## 群管理命令

### @某人 333: 踢出群聊

**功能**: 将指定成员踢出群聊

**使用方法**:
1. 在**群聊**中@要踢的人，然后发送: `@张三 333`
2. 该成员将被踢出群聊

**说明**:
- 需要你是群主或管理员才能使用
- 被踢出的成员可以再次被邀请进群

### @某人 444: 拉黑并踢出

**功能**: 将指定成员拉入黑名单并踢出群聊

**使用方法**:
1. 在**群聊**中@要拉黑的人，然后发送: `@张三 444`
2. 该成员将被踢出群聊并加入黑名单

**说明**:
- 需要你是群主或管理员才能使用
- 被拉黑的成员再次进群会被自动踢出

### delsb#wxid: 将某人移出黑名单

**功能**: 解除对指定成员的黑名单限制

**使用方法**:
1. 在【文件传输助手】中发送: `delsb#wxid_abc123456` (替换为实际的wxid)

**说明**:
- wxid可以通过复制用户名片并查看详细信息获取
- 解除黑名单后，该成员可以正常进群

## 管理员特殊命令

> 注意: 以下命令仅限系统管理员使用，普通用户无权限执行

### 10086: 获取连接信息

**功能**: 查看当前系统的连接信息

**使用方法**:
1. 在【文件传输助手】中发送: `10086`
2. 系统会返回当前连接的详细信息

### 创建授权码#数量#有效天数

**功能**: 创建指定数量和有效期的授权码

**使用方法**:
1. 在【文件传输助手】中发送: `创建授权码#1#30` (创建1个有效期为30天的授权码)
2. 系统会返回创建的授权码信息

### 延期授权码#授权码#延长天数

**功能**: 延长指定授权码的有效期

**使用方法**:
1. 在【文件传输助手】中发送: `延期授权码#ABC123#30` (将授权码ABC123延长30天)
2. 系统会返回"延期成功, 到期日期: YYYY-MM-DD"

### A000#授权码: 踢出用户

**功能**: 强制使用指定授权码的用户下线

**使用方法**:
1. 在【文件传输助手】中发送: `A000#12345` (踢出使用授权码12345的用户)

## 常见问题

### 1. 为什么我的自动抢红包功能不生效？

可能原因:
- 确认是否正确发送了`A101`命令并收到"自动抢红包已开启"的回复
- 检查群聊是否被置顶，置顶的群聊不会自动抢红包
- 检查是否设置了过高的延迟时间

### 2. 如何知道当前功能是开启还是关闭状态？

解决方法:
- 再次发送相应命令，根据返回消息判断当前状态
- 例如，发送`A101`，如果返回"自动抢红包已关闭"，说明当前状态是开启的

### 3. 自动抢红包会不会导致封号？

建议:
- 建议设置合理的延迟时间（3-5秒）
- 不要在太多群同时开启自动抢红包
- 避免24小时持续开启自动抢红包

### 4. 如何获取某人的wxid？

方法:
- 保存对方为联系人
- 点击对方头像进入详情页
- 点击右上角"..."，选择"复制"
- 粘贴到记事本中查看

### 5. 为什么有些命令没有反应？

可能原因:
- 确认是否在文件传输助手中发送命令
- 检查命令格式是否正确，包括大小写
- 对于管理员命令，确认账号是否有管理员权限 