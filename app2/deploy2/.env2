# MySQL配置
MYSQL_ROOT_PASSWORD=root123456
MYSQL_DATABASE=weixin
MYSQL_USER=weixin
MYSQL_PASSWORD=123456
MYSQL_PORT=3306

# Redis配置
REDIS_PASSWORD=123456
REDIS_PORT=6379

# WeChat Pad Pro配置
WECHAT_PORT=8080
DB_HOST=wechatpadpro_mysql
DB_PORT=3306
DB_DATABASE=weixin
DB_USERNAME=weixin
DB_PASSWORD=123456
REDIS_HOST=wechatpadpro_redis
REDIS_DB=0

# 是否开启调试模式（true: 开启，false: 关闭）
DEBUG=false

# 服务监听地址（0.0.0.0表示监听所有网卡）
HOST=0.0.0.0

# 服务端口号
PORT=1238

# API版本前缀（如 /v1, /v2）
API_VERSION=

# MCP服务端口（用于AI大模型集成服务）
MCP_PORT=8099

# 推广公众号微信ID（用于新用户首次登录时推广）
GH_WXID=

# 管理员密钥（建议使用复杂的随机字符串）
ADMIN_KEY=999222

# ==========================================
# Redis配置
# ==========================================
# Redis服务器地址
REDIS_HOST=***************

# Redis端口
REDIS_PORT=6379

# Redis数据库编号
REDIS_DB=1

# Redis密码
REDIS_PASS=123456

# Redis最大空闲连接数
REDIS_MAX_IDLE=30

# Redis最大活动连接数
REDIS_MAX_ACTIVE=100

# Redis空闲连接超时时间（毫秒）
REDIS_IDLE_TIMEOUT=5000

# Redis连接最大生命周期（秒）
REDIS_MAX_CONN_LIFETIME=3600

# Redis连接超时时间（毫秒）
REDIS_CONNECT_TIMEOUT=5000

# Redis读取超时时间（毫秒）
REDIS_READ_TIMEOUT=10000

# Redis写入超时时间（毫秒）
REDIS_WRITE_TIMEOUT=10000

# ==========================================
# 应用配置
# ==========================================
# 时区设置
TZ=Asia/Shanghai

# 工作池大小（并发处理任务的goroutine数量）
WORKER_POOL_SIZE=500

# 工作池最大任务队列长度
MAX_WORKER_TASK_LEN=1000

# Web域名（设置为localhost:1238表示使用本地服务器，留空则不上报状态）
WEB_DOMAIN=localhost:1238

# Web任务名称
WEB_TASK_NAME=

# Web任务应用编号
WEB_TASK_APP_NUMBER=

# 是否按微信ID同步消息
NEWS_SYN_WXID=true

# 是否启用DT
DT=true

# ==========================================
# 消息队列配置
# ==========================================
# 消息主题
TOPIC=wx_sync_msg_topic

# 是否启用RocketMQ
ROCKET_MQ_ENABLED=false

# RocketMQ服务器地址
ROCKET_MQ_HOST=127.0.0.1:9876

# RocketMQ访问密钥
ROCKET_ACCESS_KEY=123

# RocketMQ密钥
ROCKET_SECRET_KEY=123!#@13$

# 是否启用RabbitMQ
RABBIT_MQ_ENABLED=false

# RabbitMQ连接URL（格式：amqp://用户名:密码@主机:端口/）
RABBIT_MQ_URL=amqp://yunkong:123456@127.0.0.1:5672/

# 是否启用Kafka
KAFKA_ENABLED=false

# Kafka服务器地址列表
KAFKA_URL=

# Kafka用户名
KAFKA_USERNAME=

# Kafka密码
KAFKA_PASSWORD=

# ==========================================
# 任务配置
# ==========================================
# 任务重试次数
TASK_RETRY_COUNT=3

# 任务重试间隔（秒）
TASK_RETRY_INTERVAL=5

# 心跳包间隔（秒）
HEARTBEAT_INTERVAL=25

# 自动认证间隔（分钟）
AUTO_AUTH_INTERVAL=30

# 自动同步间隔（分钟）
AUTO_SYNC_INTERVAL_MINUTES=30

# 任务执行等待时间（毫秒）
TASK_EXEC_WAIT_TIMES=500

# 队列过期时间（秒）
QUEUE_EXPIRE_TIME=86400

# ==========================================
# WebSocket配置
# ==========================================
# WebSocket握手超时时间（秒）
WS_HANDSHAKE_TIMEOUT=10

# WebSocket读缓冲区大小（字节）
WS_READ_BUFFER_SIZE=4096

# WebSocket写缓冲区大小（字节）
WS_WRITE_BUFFER_SIZE=4096

# WebSocket读取超时时间（秒）
WS_READ_DEADLINE=120

# WebSocket写入超时时间（秒）
WS_WRITE_DEADLINE=60

# WebSocket心跳间隔（秒）
WS_PING_INTERVAL=25

# WebSocket连接检查间隔（秒）
WS_CONNECTION_CHECK_INTERVAL=45

# WebSocket最大消息大小（字节）
WS_MAX_MESSAGE_SIZE=8192

# ==========================================
# 集群配置
# ==========================================
# 集群名称
CLUSTER_NAME=

# ZooKeeper地址
ZK_ADDR=

# ETCD地址
ETCD_ADDR=

# ==========================================
# 禁用命令配置
# ==========================================
# 禁用命令列表（逗号分隔）
DISABLED_CMD_LIST=

# ==========================================
# Docker配置
# ==========================================
# MySQL root密码
MYSQL_ROOT_PASSWORD=change_this_root_password

# MySQL数据库
MYSQL_DATABASE=weixin

# MySQL用户名
MYSQL_USER=weixin

# MySQL密码
MYSQL_PASSWORD=123456
