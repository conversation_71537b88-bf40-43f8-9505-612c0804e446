#!/usr/bin/env python3
"""
测试运行脚本
运行所有公共模块的单元测试
"""

import sys
import os
import subprocess
import time

def run_tests():
    """运行所有测试"""
    print("🧪 开始运行公共模块单元测试...")
    print("=" * 60)
    
    # 测试文件列表
    test_files = [
        "tests/workers/test_session_manager.py",
        "tests/workers/test_image_utils.py", 
        "tests/workers/test_message_processor.py"
    ]
    
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n📋 运行测试: {test_file}")
            print("-" * 40)
            
            try:
                # 运行单个测试文件
                result = subprocess.run([
                    sys.executable, "-m", "pytest", test_file, "-v", "--tb=short"
                ], capture_output=True, text=True, timeout=60)
                
                # 解析结果
                if result.returncode == 0:
                    print("✅ 测试通过")
                    # 统计测试数量
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'passed' in line and 'failed' in line:
                            parts = line.split()
                            for i, part in enumerate(parts):
                                if part == 'passed':
                                    passed = int(parts[i-1])
                                    passed_tests += passed
                                elif part == 'failed':
                                    failed = int(parts[i-1])
                                    failed_tests += failed
                else:
                    print("❌ 测试失败")
                    print("错误输出:")
                    print(result.stderr)
                    failed_tests += 1
                    
            except subprocess.TimeoutExpired:
                print("⏰ 测试超时")
                failed_tests += 1
            except Exception as e:
                print(f"💥 运行测试时出错: {e}")
                failed_tests += 1
        else:
            print(f"⚠️  测试文件不存在: {test_file}")
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("-" * 40)
    print(f"总测试数: {passed_tests + failed_tests}")
    print(f"通过: {passed_tests}")
    print(f"失败: {failed_tests}")
    
    if failed_tests == 0:
        print("🎉 所有测试通过！")
        return True
    else:
        print("❌ 有测试失败，请检查代码")
        return False

def check_dependencies():
    """检查测试依赖"""
    print("🔍 检查测试依赖...")
    
    try:
        import pytest
        print("✅ pytest 已安装")
    except ImportError:
        print("❌ pytest 未安装，请运行: pip install pytest")
        return False
    
    try:
        import unittest.mock
        print("✅ unittest.mock 可用")
    except ImportError:
        print("❌ unittest.mock 不可用")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 智能路由系统重构 - 单元测试")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装必要的包")
        sys.exit(1)
    
    # 运行测试
    success = run_tests()
    
    if success:
        print("\n✅ 测试完成，所有公共模块功能正常")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，请修复问题后重新运行")
        sys.exit(1)

if __name__ == "__main__":
    main() 