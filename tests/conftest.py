"""
pytest配置文件
"""

import pytest
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


@pytest.fixture
def mock_query():
    """创建模拟的查询对象"""
    from unittest.mock import Mock
    
    query = Mock()
    query.user_message = Mock()
    query.message_event = Mock()
    query.message_chain = []
    query.session = None
    return query


@pytest.fixture
def mock_session():
    """创建模拟的会话对象"""
    from unittest.mock import Mock
    from pkg.workers.session_manager import SessionState
    
    session = Mock()
    session.user_id = "test_user"
    session.workflow_type = "kontext"
    session.state = SessionState.COLLECTING
    session.prompt = ""
    session.images = []
    session.quoted_text = ""
    session.quoted_images = []
    session.is_active.return_value = True
    session.can_execute.return_value = False
    session.get_image_count.return_value = 0
    session.get_image_purposes.return_value = []
    session.add_image.return_value = True
    session.set_prompt = Mock()
    session.set_quoted_text = Mock()
    session.update_activity = Mock()
    
    return session


@pytest.fixture
def sample_image_data():
    """创建示例图片数据"""
    # 创建一个简单的JPEG头部
    jpeg_header = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00'
    return jpeg_header + b'fake_image_data'


@pytest.fixture
def sample_base64_image():
    """创建示例base64编码的图片"""
    import base64
    image_data = b'fake_image_data'
    return base64.b64encode(image_data).decode('utf-8')


@pytest.fixture
def mock_intent_analysis():
    """创建模拟的意图分析结果"""
    from unittest.mock import Mock
    
    analysis = Mock()
    analysis.content_type.value = 'unknown'
    analysis.confidence = 0.5
    analysis.keywords = []
    analysis.recommended_workflow = None
    analysis.suggested_params = {}
    analysis.input_mode = None
    analysis.required_image_types = []
    analysis.min_images = 0
    analysis.max_images = 0
    analysis.image_purpose = None
    
    return analysis 