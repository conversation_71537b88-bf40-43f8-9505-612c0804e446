# 统一路由系统配置文件
# 基于PRD-********-统一路由系统设计规划

unified_routing:
  # 第一级路由配置（关键词触发）
  level_1:
    enabled: true
    keywords:
      aigen: "AIGEN"
      kontext: "KONTEXT" 
      kontext_api: "KONTEXT_API"
    case_sensitive: false
    performance_target_ms: 5
    reliability_target: 0.9999
    
  # 第二级路由配置（LLM智能分析）
  level_2:
    enabled: true
    llm:
      enabled: true
      timeout_ms: 2000
      model_name: "default"
      system_prompt: |
        你是一个智能工作流路由器，负责理解用户意图并选择最合适的AI工作流。

        可用的工作流类型：
        1. aigen - 文生图工作流（本地Flux）
           - 适用于：从文本描述生成图片
           - 可选输入：文本提示词 + 可选参考图片（控制图/参考图）
           - 特点：支持ControlNet、LoRA、高质量生成

        2. kontext - 图生图工作流（本地Kontext）  
           - 适用于：基于现有图片进行编辑、修改、风格转换
           - 必需输入：至少1张图片 + 编辑指令
           - 特点：保持原图结构，精准编辑

        3. kontext_api - 远程API工作流
           - 适用于：复杂的图像处理，当本地资源不足时
           - 必需输入：图片 + 处理指令
           - 特点：云端处理，功能丰富

        路由规则：
        - 如果用户明确提到"aigen"、"文生图"、"生成图片"等 → aigen
        - 如果用户明确提到"kontext"、"编辑"、"修改图片"等 → kontext  
        - 如果用户请求复杂处理或提到"api"、"云端"等 → kontext_api
        - 根据用户描述的任务类型智能选择最合适的工作流
        - 考虑输入条件（是否有图片、图片数量）

        请以JSON格式返回结果：
        {
            "workflow_type": "aigen|kontext|kontext_api",
            "confidence": "high|medium|low|unknown", 
            "reasoning": "选择这个工作流的理由",
            "suggested_prompt": "优化后的提示词（可选）",
            "alternative_workflows": ["备选工作流列表"],
            "needs_clarification": true/false,
            "clarification_question": "需要用户确认的问题（如果有）"
        }
    
    # 启发式后备配置
    heuristic:
      enabled: true
      timeout_ms: 10
      default_workflow: "AIGEN"
      rules:
        # 基于图片数量的规则
        image_based:
          enabled: true
          thresholds:
            low_image_count: 3  # 图片数量阈值
            high_image_count: 5  # 高图片数量阈值
          workflows:
            no_images: "AIGEN"
            low_images: "KONTEXT"
            high_images: "KONTEXT_API"
        
        # 基于文本关键词的规则
        text_based:
          enabled: true
          keywords:
            generation: ["生成", "画", "创建", "制作", "绘制"]
            editing: ["编辑", "修改", "变换", "调整", "优化"]
            api: ["api", "远程", "云端", "在线"]
          workflows:
            generation: "AIGEN"
            editing: "KONTEXT"
            api: "KONTEXT_API"
      
  # 监控配置
  monitoring:
    enabled: true
    log_level: "INFO"
    metrics_collection: true
    performance_tracking: true
    
    # 性能指标
    performance_targets:
      level_1_timeout_ms: 5
      level_2_llm_timeout_ms: 2000
      level_2_heuristic_timeout_ms: 10
      overall_timeout_ms: 3000
    
    # 准确率指标
    accuracy_targets:
      level_1_success_rate: 0.9999
      level_2_accuracy_rate: 0.90
      overall_satisfaction_rate: 0.95
    
    # 日志配置
    logging:
      enabled: true
      level: "INFO"
      format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
      file: "logs/unified_routing.log"
      max_size_mb: 100
      backup_count: 5
    
    # 指标收集
    metrics:
      enabled: true
      collection_interval_seconds: 60
      retention_days: 30
      
  # 错误处理配置
  error_handling:
    # 降级策略
    fallback_strategy: "graceful"  # graceful, aggressive, conservative
    
    # 超时处理
    timeout_handling:
      level_1_timeout_action: "skip_to_level_2"
      level_2_llm_timeout_action: "use_heuristic"
      level_2_heuristic_timeout_action: "use_default"
    
    # 异常处理
    exception_handling:
      log_exceptions: true
      return_default_on_exception: true
      default_workflow: "AIGEN"
    
  # 用户交互配置
  user_interaction:
    # 确认机制
    confirmation:
      enabled: true
      low_confidence_threshold: 0.6
      unknown_confidence_action: "ask_confirmation"
      
    # 提示信息
    messages:
      routing_success: "🎯 已为您选择最适合的工作流"
      routing_confirmation: "🤔 需要确认您的工作流选择"
      routing_fallback: "⚠️ 使用后备路由策略"
      routing_error: "❌ 路由失败，使用默认工作流"
      
    # 工作流描述
    workflow_descriptions:
      aigen: "🎨 文生图工作流 - 根据文本描述生成图片"
      kontext: "✏️ 图生图工作流 - 编辑和修改现有图片"
      kontext_api: "☁️ 远程API工作流 - 云端图像处理"
      
  # 开发调试配置
  debug:
    enabled: false
    verbose_logging: false
    log_routing_decisions: true
    log_performance_metrics: true
    log_user_interactions: true 