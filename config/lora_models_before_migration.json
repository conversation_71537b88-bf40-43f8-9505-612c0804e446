{"models": [{"name": "flux1-canny-dev-lora", "filename": "flux1-canny-dev-lora.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/flux1-canny-dev-lora.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: flux1-canny-dev-lora.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "黑格建筑效果表现FLUX_new", "filename": "黑格建筑效果表现FLUX_new.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/黑格建筑效果表现FLUX_new.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["建筑", "architecture", "building", "house", "building"], "description": "自动发现的Lora模型: 黑格建筑效果表现FLUX_new.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "比鲁斯商业建筑_V0.2", "filename": "比鲁斯商业建筑_V0.2.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/比鲁斯商业建筑_V0.2.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["建筑", "architecture", "building", "house", "building"], "description": "自动发现的Lora模型: 比鲁斯商业建筑_V0.2.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "detail_aidmafluxproultra-FLUX-v0.1", "filename": "detail_aidmafluxproultra-FLUX-v0.1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/detail_aidmafluxproultra-FLUX-v0.1.safetensors", "category": "detail", "weight": 0.8, "trigger_words": ["细节", "detail", "高清", "high quality"], "description": "自动发现的Lora模型: detail_aidmafluxproultra-FLUX-v0.1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": true, "model_type": "flux"}, {"name": "add-detail-xl", "filename": "add-detail-xl.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/add-detail-xl.safetensors", "category": "detail", "weight": 0.8, "trigger_words": ["细节", "detail", "高清", "high quality"], "description": "自动发现的Lora模型: add-detail-xl.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": false, "is_priority": true, "model_type": "sdxl"}, {"name": "Hyper-FLUX.1-dev-8steps-lora", "filename": "Hyper-FLUX.1-dev-8steps-lora.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Hyper-FLUX.1-dev-8steps-lora.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: Hyper-FLUX.1-dev-8steps-lora.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "加州别墅-1.0", "filename": "加州别墅-1.0.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/加州别墅-1.0.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: 加州别墅-1.0.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "ASTRA_Flux_OC_Vbeta-2", "filename": "ASTRA_Flux_OC_Vbeta-2.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/ASTRA_Flux_OC_Vbeta-2.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["建筑", "architecture", "building", "house", "building"], "description": "自动发现的Lora模型: ASTRA_Flux_OC_Vbeta-2.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "Flux Turbo Lora_StarAi_Flux Turbo Lora", "filename": "Flux Turbo Lora_StarAi_Flux Turbo Lora.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Flux Turbo Lora_StarAi_Flux Turbo Lora.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: Flux Turbo Lora_StarAi_Flux Turbo Lora.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "flux1-depth-dev-lora", "filename": "flux1-depth-dev-lora.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/flux1-depth-dev-lora.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: flux1-depth-dev-lora.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "sdxl_lightning_8step_lora", "filename": "sdxl_lightning_8step_lora.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL-Lightning/sdxl_lightning_8step_lora.safetensors", "category": "other", "weight": 0.8, "trigger_words": ["sdxl", "lightning", "8step"], "description": "自动发现的Lora模型: sdxl_lightning_8step_lora.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl"}, {"name": "Hui-Style Architecture 徽派建筑风格", "filename": "Hui-Style Architecture 徽派建筑风格.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Hui-Style Architecture 徽派建筑风格.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["hui", "architecture", "style", "徽派建筑风格"], "description": "自动发现的Lora模型: Hui-Style Architecture 徽派建筑风格.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "ShallowDepth", "filename": "ShallowDepth.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/ShallowDepth.safetensors", "category": "other", "weight": 0.8, "trigger_words": ["shallowdepth"], "description": "自动发现的Lora模型: ShallowDepth.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "比鲁斯别墅度假酒店Holiday cottages in Billus_XL1.0_PRO", "filename": "比鲁斯别墅度假酒店Holiday cottages in Billus_XL1.0_PRO.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/比鲁斯别墅度假酒店Holiday cottages in Billus_XL1.0_PRO.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["billus", "cottages", "比鲁斯别墅度假酒店", "holiday"], "description": "自动发现的Lora模型: 比鲁斯别墅度假酒店Holiday cottages in Billus_XL1.0_PRO.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": true, "model_type": "sdxl"}, {"name": "Minimalism_Flux", "filename": "Minimalism_Flux.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Minimalism_Flux.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["minimalism"], "description": "自动发现的Lora模型: Minimalism_Flux.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "Flux Flat Anime", "filename": "Flux Flat Anime.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Flux Flat Anime.safetensors", "category": "anime", "weight": 0.8, "trigger_words": ["anime", "flat"], "description": "自动发现的Lora模型: Flux Flat Anime.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "比鲁斯Flux1中古室内Medieval Ancient Style _V0.1", "filename": "比鲁斯Flux1中古室内Medieval Ancient Style _V0.1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/比鲁斯Flux1中古室内Medieval Ancient Style _V0.1.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["style", "ancient", "中古室内", "medieval", "比鲁斯"], "description": "自动发现的Lora模型: 比鲁斯Flux1中古室内Medieval Ancient Style _V0.1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "NeoChineseModernInterior", "filename": "NeoChineseModernInterior.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/NeoChineseModernInterior.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["neochinesemoderninterior"], "description": "自动发现的Lora模型: NeoChineseModernInterior.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "比鲁斯现代酒店Modern Hotel Design_XL1.0-PRO", "filename": "比鲁斯现代酒店Modern Hotel Design_XL1.0-PRO.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/比鲁斯现代酒店Modern Hotel Design_XL1.0-PRO.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["比鲁斯现代酒店", "hotel", "design", "modern"], "description": "自动发现的Lora模型: 比鲁斯现代酒店Modern Hotel Design_XL1.0-PRO.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": true, "model_type": "sdxl"}, {"name": "Detail Tweaker XL", "filename": "Detail Tweaker XL.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Detail Tweaker XL.safetensors", "category": "detail", "weight": 0.8, "trigger_words": ["detail", "tweaker"], "description": "自动发现的Lora模型: Detail Tweaker XL.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": true, "model_type": "sdxl"}, {"name": "artchitecture_Flux1", "filename": "artchitecture_Flux1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/artchitecture_Flux1.safetensors", "category": "style", "weight": 0.8, "trigger_words": ["artchitecture"], "description": "自动发现的Lora模型: artchitecture_Flux1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "Anime_niji", "filename": "Anime_niji.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Anime_niji.safetensors", "category": "anime", "weight": 0.8, "trigger_words": ["anime", "niji"], "description": "自动发现的Lora模型: Anime_niji.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "niji_flux", "filename": "niji_flux.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/niji_flux.safetensors", "category": "anime", "weight": 0.8, "trigger_words": ["niji"], "description": "自动发现的Lora模型: niji_flux.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "flat2100", "filename": "flat2100.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/flat2100.safetensors", "category": "other", "weight": 0.8, "trigger_words": ["flat"], "description": "自动发现的Lora模型: flat2100.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "Niji_semi-realism_flux", "filename": "Niji_semi-realism_flux.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Niji_semi-realism_flux.safetensors", "category": "anime", "weight": 0.8, "trigger_words": ["semi", "niji", "realism"], "description": "自动发现的Lora模型: Niji_semi-realism_flux.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "Particle Vision", "filename": "Particle Vision.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Particle Vision.safetensors", "category": "style", "weight": 0.8, "trigger_words": ["particle", "vision"], "description": "自动发现的Lora模型: Particle Vision.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "outfit-generator", "filename": "outfit-generator.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/outfit-generator.safetensors", "category": "other", "weight": 0.8, "trigger_words": ["generator", "outfit"], "description": "自动发现的Lora模型: outfit-generator.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "goodhands_<PERSON>_<PERSON><PERSON><PERSON>", "filename": "goodhands_Beta_Gtonero.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/goodhands_Beta_Gtonero.safetensors", "category": "other", "weight": 0.8, "trigger_words": ["goodhands", "gtonero"], "description": "自动发现的Lora模型: goodhands_Beta_Gtonero.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "flux-RA-NewShikumen", "filename": "flux-RA-NewShikumen.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/flux-RA-NewShikumen.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["newshikumen"], "description": "自动发现的Lora模型: flux-RA-NewShikumen.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "Flux_RA_curtainWwall_rk16_bf16", "filename": "Flux_RA_curtainWwall_rk16_bf16.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Flux_RA_curtainWwall_rk16_bf16.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["curtainwwall"], "description": "自动发现的Lora模型: Flux_RA_curtainWwall_rk16_bf16.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "pytorch_lora_weights", "filename": "pytorch_lora_weights.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/lcm/SD1.5/pytorch_lora_weights.safetensors", "category": "other", "weight": 0.8, "trigger_words": ["pytorch", "weights"], "description": "自动发现的Lora模型: pytorch_lora_weights.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sd15"}, {"name": "ASTRA_KC_XL_RSD_Landscape_V1", "filename": "ASTRA_KC_XL_RSD_Landscape_V1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_KC_XL_RSD_Landscape_V1.safetensors", "category": "landscape", "weight": 0.8, "trigger_words": ["landscape", "rsd", "astra"], "description": "自动发现的Lora模型: ASTRA_KC_XL_RSD_Landscape_V1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl"}, {"name": "ASTRA_KC_Interior_Living_Bedroom_Minimalism_V1", "filename": "ASTRA_KC_Interior_Living_Bedroom_Minimalism_V1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_KC_Interior_Living_Bedroom_Minimalism_V1.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["astra", "living", "bedroom", "minimalism", "interior"], "description": "自动发现的Lora模型: ASTRA_KC_Interior_Living_Bedroom_Minimalism_V1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl"}, {"name": "ASTRA_XL_Artistic_Rendering_Style_V2", "filename": "ASTRA_XL_Artistic_Rendering_Style_V2.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_XL_Artistic_Rendering_Style_V2.safetensors", "category": "style", "weight": 0.8, "trigger_words": ["astra", "rendering", "style", "artistic"], "description": "自动发现的Lora模型: ASTRA_XL_Artistic_Rendering_Style_V2.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl"}, {"name": "ASTRA_XL_AerialView_Photography_V1", "filename": "ASTRA_XL_AerialView_Photography_V1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_XL_AerialView_Photography_V1.safetensors", "category": "landscape", "weight": 0.8, "trigger_words": ["photography", "astra", "aerialview"], "description": "自动发现的Lora模型: ASTRA_XL_AerialView_Photography_V1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl"}, {"name": "ASTRA_KC_XL_PlantGroups_V1", "filename": "ASTRA_KC_XL_PlantGroups_V1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_KC_XL_PlantGroups_V1.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["plantgroups", "astra"], "description": "自动发现的Lora模型: ASTRA_KC_XL_PlantGroups_V1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl"}, {"name": "ASTRA_KC_Urban_Renewal_V1.1", "filename": "ASTRA_KC_Urban_Renewal_V1.1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_KC_Urban_Renewal_V1.1.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["urban", "renewal", "astra"], "description": "自动发现的Lora模型: ASTRA_KC_Urban_Renewal_V1.1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl"}, {"name": "ASTRA_XL_OC_V3", "filename": "ASTRA_XL_OC_V3.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_XL_OC_V3.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["astra"], "description": "自动发现的Lora模型: ASTRA_XL_OC_V3.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl"}, {"name": "ASTRA_KC_XL_Residential_V1", "filename": "ASTRA_KC_XL_Residential_V1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_KC_XL_Residential_V1.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["residential", "astra"], "description": "自动发现的Lora模型: ASTRA_KC_XL_Residential_V1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl"}]}