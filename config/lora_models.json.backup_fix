{"models": [{"name": "黑格建筑效果表现FLUX_new", "filename": "黑格建筑效果表现FLUX_new.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/黑格建筑效果表现FLUX_new.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["建筑", "architecture", "architectural photography", "architectural rendering"], "description": "黑格建筑效果表现FLUX_new.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "manually_maintained": true, "is_priority": false, "model_type": "flux"}, {"name": "比鲁斯商业建筑_V0.2", "filename": "比鲁斯商业建筑_V0.2.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/比鲁斯商业建筑_V0.2.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["建筑", "architecture", "building", "architectural photography", "architectural rendering"], "description": "比鲁斯商业建筑_V0.2.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "manually_maintained": true, "is_priority": false, "model_type": "flux"}, {"name": "detail_aidmafluxproultra-FLUX-v0.1", "filename": "detail_aidmafluxproultra-FLUX-v0.1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/detail_aidmafluxproultra-FLUX-v0.1.safetensors", "category": "detail", "weight": 0.8, "trigger_words": ["细节", "detail", "细节增强", "detail enhancement", "高清细节"], "description": "detail_aidmafluxproultra-FLUX-v0.1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "manually_maintained": true, "is_priority": true, "model_type": "flux"}, {"name": "add-detail-xl", "filename": "add-detail-xl.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/add-detail-xl.safetensors", "category": "other", "weight": 0.5, "trigger_words": ["细节", "细节增强", "SDXL", "detail enhancement"], "description": "SDXL细节增强模型", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": false, "is_priority": true, "model_type": "sdxl", "manually_maintained": true, "last_manual_update": "2025-01-17", "migrated_from_yaml": true}, {"name": "Hyper-FLUX.1-dev-8steps-lora", "filename": "Hyper-FLUX.1-dev-8steps-lora.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Hyper-FLUX.1-dev-8steps-lora.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: Hyper-FLUX.1-dev-8steps-lora.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux", "manually_maintained": true}, {"name": "加州别墅-1.0", "filename": "加州别墅-1.0.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/加州别墅-1.0.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: 加州别墅-1.0.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux", "manually_maintained": true}, {"name": "ASTRA_Flux_OC_Vbeta-2", "filename": "ASTRA_Flux_OC_Vbeta-2.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/ASTRA_Flux_OC_Vbeta-2.safetensors", "category": "architecture", "weight": 0.75, "trigger_words": ["专业建筑设计", "architecture design", "建筑效果图", "architectural rendering", "architectural photography"], "description": "ASTRA建筑渲染风格", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": true, "model_type": "flux", "manually_maintained": true, "last_manual_update": "2025-01-17", "migrated_from_yaml": true}, {"name": "Flux Turbo Lora_StarAi_Flux Turbo Lora", "filename": "Flux Turbo Lora_StarAi_Flux Turbo Lora.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Flux Turbo Lora_StarAi_Flux Turbo Lora.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: Flux Turbo Lora_StarAi_Flux Turbo Lora.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux", "manually_maintained": true}, {"name": "sdxl_lightning_8step_lora", "filename": "sdxl_lightning_8step_lora.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL-Lightning/sdxl_lightning_8step_lora.safetensors", "category": "other", "weight": 0.8, "trigger_words": ["sdxl", "lightning", "8step"], "description": "自动发现的Lora模型: sdxl_lightning_8step_lora.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl", "manually_maintained": true}, {"name": "Hui-Style Architecture 徽派建筑风格", "filename": "Hui-Style Architecture 徽派建筑风格.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Hui-Style Architecture 徽派建筑风格.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["huipai", "Anhui architecture", "徽派建筑风格"], "description": "Hui-Style Architecture 徽派建筑风格.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux", "manually_maintained": true}, {"name": "ShallowDepth", "filename": "ShallowDepth.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/ShallowDepth.safetensors", "category": "style", "weight": 0.7, "trigger_words": ["浅景深", "镜头模糊", "小景深", "景深", "shallowdepth"], "description": "浅深度控制", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux", "manually_maintained": true, "last_manual_update": "2025-01-17", "migrated_from_yaml": true}, {"name": "比鲁斯别墅度假酒店Holiday cottages in Billus_XL1.0_PRO", "filename": "比鲁斯别墅度假酒店Holiday cottages in Billus_XL1.0_PRO.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/比鲁斯别墅度假酒店Holiday cottages in Billus_XL1.0_PRO.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["billus", "cottages", "比鲁斯别墅度假酒店", "holiday", "hotel", "villa"], "description": "自动发现的Lora模型: 比鲁斯别墅度假酒店Holiday cottages in Billus_XL1.0_PRO.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": true, "model_type": "sdxl", "manually_maintained": true}, {"name": "Minimalism_Flux", "filename": "Minimalism_Flux.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Minimalism_Flux.safetensors", "category": "style", "weight": 0.7, "trigger_words": ["minimalist", "极简主义", "minimalism", "简约设计"], "description": "Flux极简主义风格", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux", "manually_maintained": true, "last_manual_update": "2025-01-17", "migrated_from_yaml": true}, {"name": "Flux Flat Anime", "filename": "Flux Flat Anime.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Flux Flat Anime.safetensors", "category": "anime", "weight": 0.7, "trigger_words": ["动漫", "flat", "Flux", "anime", "niji", "二次元", "卡通", "manga", "漫画"], "description": "Flux扁平化动漫风格", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux", "manually_maintained": true, "last_manual_update": "2025-01-17", "migrated_from_yaml": true}, {"name": "比鲁斯Flux1中古室内Medieval Ancient Style _V0.1", "filename": "比鲁斯Flux1中古室内Medieval Ancient Style _V0.1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/比鲁斯Flux1中古室内Medieval Ancient Style _V0.1.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["ancient", "中古室内", "medieval", "比鲁斯"], "description": "自动发现的Lora模型: 比鲁斯Flux1中古室内Medieval Ancient Style _V0.1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "manually_maintained": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "NeoChineseModernInterior", "filename": "NeoChineseModernInterior.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/NeoChineseModernInterior.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["neochinesemoderninterior", "chinese modern interior"], "description": "自动发现的Lora模型: NeoChineseModernInterior.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "manually_maintained": true, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux"}, {"name": "比鲁斯现代酒店Modern Hotel Design_XL1.0-PRO", "filename": "比鲁斯现代酒店Modern Hotel Design_XL1.0-PRO.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/比鲁斯现代酒店Modern Hotel Design_XL1.0-PRO.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["比鲁斯现代酒店", "hotel", "hotel design", "modern hotel", "酒店设计"], "description": "自动发现的Lora模型: 比鲁斯现代酒店Modern Hotel Design_XL1.0-PRO.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": true, "model_type": "sdxl", "manually_maintained": true}, {"name": "Detail Tweaker XL", "filename": "Detail Tweaker XL.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Detail Tweaker XL.safetensors", "category": "detail", "weight": 0.8, "trigger_words": ["detail", "tweaker"], "description": "自动发现的Lora模型: Detail Tweaker XL.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": true, "model_type": "sdxl", "manually_maintained": true}, {"name": "artchitecture_Flux1", "filename": "artchitecture_Flux1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/artchitecture_Flux1.safetensors", "category": "architecture", "weight": 0.7, "trigger_words": ["建筑", "建筑艺术", "professional photography", "architecture", "architectural photography", "建筑摄影"], "description": "Flux1建筑艺术风格", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux", "manually_maintained": true, "last_manual_update": "2025-01-17", "migrated_from_yaml": true}, {"name": "Anime_niji", "filename": "Anime_niji.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Anime_niji.safetensors", "category": "anime", "weight": 0.7, "trigger_words": ["动漫", "<PERSON><PERSON>", "anime", "二次元", "卡通", "manga", "漫画"], "description": "Niji动漫风格", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux", "manually_maintained": true, "last_manual_update": "2025-01-17", "migrated_from_yaml": true}, {"name": "niji_flux", "filename": "niji_flux.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/niji_flux.safetensors", "category": "anime", "weight": 0.7, "trigger_words": ["动漫", "<PERSON><PERSON>", "anime", "二次元", "卡通", "manga", "漫画"], "description": "Niji Flux风格", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux", "manually_maintained": true, "last_manual_update": "2025-01-17", "migrated_from_yaml": true}, {"name": "flat2100", "filename": "flat2100.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/flat2100.safetensors", "category": "style", "weight": 0.7, "trigger_words": ["扁平化", "勾线平涂", "平面化", "flat", "2D"], "description": "扁平化2100风格", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux", "manually_maintained": true, "last_manual_update": "2025-01-17", "migrated_from_yaml": true}, {"name": "Niji_semi-realism_flux", "filename": "Niji_semi-realism_flux.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Niji_semi-realism_flux.safetensors", "category": "anime", "weight": 0.8, "trigger_words": ["semi realism", "anime", "niji", "semi realistic"], "description": "自动发现的Lora模型: Niji_semi-realism_flux.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "manually_maintained": true, "is_priority": false, "model_type": "flux"}, {"name": "Particle Vision", "filename": "Particle Vision.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Particle Vision.safetensors", "category": "style", "weight": 0.8, "trigger_words": ["particle", "vision"], "description": "自动发现的Lora模型: Particle Vision.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux", "manually_maintained": true}, {"name": "outfit-generator", "filename": "outfit-generator.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/outfit-generator.safetensors", "category": "other", "weight": 0.7, "trigger_words": ["服装", "生成器"], "description": "服装生成器", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux", "manually_maintained": true, "last_manual_update": "2025-01-17", "migrated_from_yaml": true}, {"name": "goodhands_<PERSON>_<PERSON><PERSON><PERSON>", "filename": "goodhands_Beta_Gtonero.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/goodhands_Beta_Gtonero.safetensors", "category": "other", "weight": 0.8, "trigger_words": ["goodhands", "gtonero"], "description": "自动发现的Lora模型: goodhands_Beta_Gtonero.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux", "manually_maintained": true}, {"name": "flux-RA-NewShikumen", "filename": "flux-RA-NewShikumen.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/flux-RA-NewShikumen.safetensors", "category": "style", "weight": 0.6, "trigger_words": ["石库门", "砖墙", "老建筑", "shikumen", "old building", "shanghai"], "description": "Flux新石库门风格", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "flux", "manually_maintained": true, "last_manual_update": "2025-01-17", "migrated_from_yaml": true}, {"name": "Flux_RA_curtainWwall_rk16_bf16", "filename": "Flux_RA_curtainWwall_rk16_bf16.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Flux_RA_curtainWwall_rk16_bf16.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["curtainwwall"], "description": "玻璃幕墙模型训练实验", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "manually_maintained": true, "is_priority": false, "model_type": "flux"}, {"name": "pytorch_lora_weights", "filename": "pytorch_lora_weights.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/lcm/SD1.5/pytorch_lora_weights.safetensors", "category": "other", "weight": 0.8, "trigger_words": ["pytorch", "weights"], "description": "自动发现的Lora模型: pytorch_lora_weights.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sd15", "manually_maintained": true}, {"name": "ASTRA_KC_XL_RSD_Landscape_V1", "filename": "ASTRA_KC_XL_RSD_Landscape_V1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_KC_XL_RSD_Landscape_V1.safetensors", "category": "landscape", "weight": 0.8, "trigger_words": ["landscape", "rsd", "astra"], "description": "自动发现的Lora模型: ASTRA_KC_XL_RSD_Landscape_V1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl", "manually_maintained": true}, {"name": "ASTRA_KC_Interior_Living_Bedroom_Minimalism_V1", "filename": "ASTRA_KC_Interior_Living_Bedroom_Minimalism_V1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_KC_Interior_Living_Bedroom_Minimalism_V1.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["astra", "living", "bedroom", "minimalism", "interior"], "description": "自动发现的Lora模型: ASTRA_KC_Interior_Living_Bedroom_Minimalism_V1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl", "manually_maintained": true}, {"name": "ASTRA_XL_Artistic_Rendering_Style_V2", "filename": "ASTRA_XL_Artistic_Rendering_Style_V2.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_XL_Artistic_Rendering_Style_V2.safetensors", "category": "style", "weight": 0.8, "trigger_words": ["astra", "rendering", "artistic rendering", "渲染风格", "艺术渲染"], "description": "自动发现的Lora模型: ASTRA_XL_Artistic_Rendering_Style_V2.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl", "manually_maintained": true}, {"name": "ASTRA_XL_AerialView_Photography_V1", "filename": "ASTRA_XL_AerialView_Photography_V1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_XL_AerialView_Photography_V1.safetensors", "category": "landscape", "weight": 0.8, "trigger_words": ["photography", "astra", "aerialview"], "description": "自动发现的Lora模型: ASTRA_XL_AerialView_Photography_V1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl", "manually_maintained": true}, {"name": "ASTRA_KC_XL_PlantGroups_V1", "filename": "ASTRA_KC_XL_PlantGroups_V1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_KC_XL_PlantGroups_V1.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["plantgroups", "astra"], "description": "自动发现的Lora模型: ASTRA_KC_XL_PlantGroups_V1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl", "manually_maintained": true}, {"name": "ASTRA_KC_Urban_Renewal_V1.1", "filename": "ASTRA_KC_Urban_Renewal_V1.1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_KC_Urban_Renewal_V1.1.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["urban", "renewal", "astra"], "description": "自动发现的Lora模型: ASTRA_KC_Urban_Renewal_V1.1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl", "manually_maintained": true}, {"name": "ASTRA_XL_OC_V3", "filename": "ASTRA_XL_OC_V3.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_XL_OC_V3.safetensors", "category": "architecture", "weight": 0.75, "trigger_words": ["建筑", "渲染", "SDXL", "architecture", "建筑设计", "专业建筑", "效果图", "建筑渲染"], "description": "ASTRA建筑渲染V3", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl", "manually_maintained": true, "last_manual_update": "2025-01-17", "migrated_from_yaml": true}, {"name": "ASTRA_KC_XL_Residential_V1", "filename": "ASTRA_KC_XL_Residential_V1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_KC_XL_Residential_V1.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["residential", "astra"], "description": "自动发现的Lora模型: ASTRA_KC_XL_Residential_V1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "model_type": "sdxl", "manually_maintained": true}, {"name": "WAN2.1_NausicaäGhibliStyle_v1_by-AI_Characters", "filename": "WAN2.1_NausicaäGhibliStyle_v1_by-AI_Characters.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/WAN2.1_NausicaäGhibliStyle_v1_by-AI_Characters.safetensors", "category": "style", "model_type": "flux", "weight": 0.8, "trigger_words": ["<PERSON><PERSON><PERSON><PERSON>", "studio ghibli", "nausicaa", "wan", "ghibli style", "miyazaki"], "description": "自动发现的Lora模型: WAN2.1_NausicaäGhibliStyle_v1_by-AI_Characters.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "manually_maintained": true}, {"name": "studio_ghibli_style", "filename": "studio_ghibli_style.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/studio_ghibli_style.safetensors", "category": "style", "model_type": "flux", "weight": 0.8, "trigger_words": ["studio", "style", "<PERSON><PERSON><PERSON><PERSON>"], "description": "自动发现的Lora模型: studio_ghibli_style.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "manually_maintained": true}, {"name": "cinematic style Flux Meets The World style v0.9 9000", "filename": "cinematic style Flux Meets The World style v0.9 9000.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/cinematic style Flux Meets The World style v0.9 9000.safetensors", "category": "style", "model_type": "flux", "weight": 0.8, "trigger_words": ["world", "style", "meets", "cinematic", "the", "flux"], "description": "自动发现的Lora模型: cinematic style Flux Meets The World style v0.9 9000.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "manually_maintained": true}, {"name": "cinematic 8mm film style v1-step00001950", "filename": "cinematic 8mm film style v1-step00001950.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/cinematic 8mm film style v1-step00001950.safetensors", "category": "style", "model_type": "flux", "weight": 0.8, "trigger_words": ["style", "step", "cinematic", "film"], "description": "自动发现的Lora模型: cinematic 8mm film style v1-step00001950.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "manually_maintained": true}, {"name": "ASTRA_Flux_RSD_PlantsGroup_Vbeta-1", "filename": "ASTRA_Flux_RSD_PlantsGroup_Vbeta-1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/ASTRA_Flux_RSD_PlantsGroup_Vbeta-1.safetensors", "category": "landscape", "model_type": "flux", "weight": 0.8, "trigger_words": ["plantsgroup", "rsd", "vbeta", "astra", "flux"], "description": "自动发现的Lora模型: ASTRA_Flux_RSD_PlantsGroup_Vbeta-1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "manually_maintained": true}, {"name": "Astra_Kon_DuskLighting_Vbeta", "filename": "Astra_Kon_DuskLighting_Vbeta.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Astra_Kon_DuskLighting_Vbeta.safetensors", "category": "kontext", "model_type": "flux", "weight": 0.8, "trigger_words": ["vbeta", "kon", "astra", "dusklighting"], "description": "自动发现的Lora模型: Astra_Kon_DuskLighting_Vbeta.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "manually_maintained": true}, {"name": "ASTRA_Flux_Natural_Style_V1-2", "filename": "ASTRA_Flux_Natural_Style_V1-2.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/ASTRA_Flux_Natural_Style_V1-2.safetensors", "category": "architecture", "model_type": "flux", "weight": 0.8, "trigger_words": ["style", "flux", "natural", "astra"], "description": "自动发现的Lora模型: ASTRA_Flux_Natural_Style_V1-2.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false, "manually_maintained": true}]}