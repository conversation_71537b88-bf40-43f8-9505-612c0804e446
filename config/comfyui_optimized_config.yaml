lora_settings:
  max_lora_count: 3
  skip_invalid_loras: true
  validate_lora_files: true
  weight_precision: 3
memory_management:
  cleanup_interval: 5
  enable_memory_cleanup: true
  force_garbage_collection: true
  max_workflow_cache_size: 100
pytorch_settings:
  map_location: cpu
  weights_only: true
workflow_validation:
  log_validation_errors: true
  skip_invalid_nodes: true
  strict_mode: false
