#!/usr/bin/env python3
"""
[二次开发] 二次开发文件索引检查工具

用于验证和生成二次开发代码的索引文档
自动扫描带有[二次开发]标识的文件并生成索引

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：自动扫描和生成二次开发代码索引文档
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：DEV-06 监控模块实现
- 依赖关系：依赖pathlib和datetime标准库
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Set, Tuple
from datetime import datetime

class DevIndexChecker:
    def __init__(self):
        self.root_path = Path(".")
        self.inventory_file = self.root_path / "docs" / "SECONDARY_DEVELOPMENT_INVENTORY.md"
        self.index_file = self.root_path / "docs" / "SECONDARY_DEVELOPMENT_INDEX.md"
        
        # 更新为实际的二次开发目录
        self.secondary_dev_dirs = [
            ".",                   # 根目录下的脚本文件
            "pkg/workers/flux",
            "pkg/workers/kontext",
            "pkg/workers/kontext_api", 
            "pkg/workers/shared",
            "pkg/core/workflow",  # 只包含unified_routing_system.py
            "pkg/core/session",   # 只包含manager.py
            "pkg/adapters",       # 只包含comfyui_adapter.py和wechat_adapter.py
        ]
        
    def scan_secondary_dev_files(self) -> Dict[str, List[Tuple[str, str, str]]]:
        """扫描所有带有[二次开发]标识的文件（递归搜索，避免重复）"""
        files_by_dir = {}
        processed_files = set()  # 用于避免重复处理同一个文件

        for dir_path in self.secondary_dev_dirs:
            full_path = self.root_path / dir_path
            if not full_path.exists():
                continue

            # 递归搜索所有Python文件
            for file_path in full_path.rglob("*.py"):
                # 排除 __pycache__ 和 __init__.py
                if not file_path.name.startswith("__"):
                    # 使用绝对路径作为唯一标识，避免重复处理
                    abs_path = file_path.resolve()
                    if abs_path in processed_files:
                        continue
                    processed_files.add(abs_path)

                    # 检查文件是否包含[二次开发]标识
                    if self._is_secondary_dev_file(file_path):
                        # 计算相对于项目根目录的路径
                        relative_dir = file_path.parent.relative_to(self.root_path)
                        relative_dir_str = str(relative_dir).replace("\\", "/")

                        if relative_dir_str not in files_by_dir:
                            files_by_dir[relative_dir_str] = []

                        files_by_dir[relative_dir_str].append((
                            file_path.name,
                            self._extract_file_description(file_path),
                            self._get_file_status(file_path)
                        ))

        # 对每个目录的文件进行排序
        for dir_path in files_by_dir:
            files_by_dir[dir_path] = sorted(files_by_dir[dir_path], key=lambda x: x[0])

        return files_by_dir
    
    def _is_secondary_dev_file(self, file_path: Path) -> bool:
        """检查文件是否包含[二次开发]标识"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read(1000)  # 只读取前1000字符
                return "[二次开发]" in content
        except Exception:
            return False
    
    def _extract_file_description(self, file_path: Path) -> str:
        """从文件头注释中提取功能描述"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read(1500)  # 增加读取长度

            # 查找功能描述
            lines = content.split('\n')
            found_dev_tag = False

            for line in lines:
                line = line.strip()

                # 找到[二次开发]标识
                if '[二次开发]' in line:
                    found_dev_tag = True
                    # 如果同一行有描述，提取它（去掉[二次开发]部分）
                    if ']' in line:
                        desc_part = line.split(']', 1)[1].strip()
                        if desc_part and not desc_part.startswith('"""'):
                            return desc_part
                    continue

                # 方法1：在找到[二次开发]标识后，查找独立的功能描述行
                if found_dev_tag and line:
                    # 跳过空行、注释结束符、开发说明等，找到功能描述
                    if (not line.startswith('"""') and
                        not line.startswith('开发说明') and
                        not line.startswith('- 此文件为') and
                        not line.startswith('- 维护者') and
                        not line.startswith('- 最后更新') and
                        not line.startswith('- 相关任务') and
                        not line.startswith('- 依赖关系') and
                        not line.startswith('[迁移说明]') and
                        not line.startswith('功能边界') and
                        line != ''):
                        return line

                # 方法2：查找"- 功能："开头的行
                if line.startswith('- 功能：'):
                    return line[4:].strip()  # 去掉"- 功能："前缀

            return "功能描述待补充"
        except Exception:
            return "功能描述待补充"
    
    def _get_file_status(self, file_path: Path) -> str:
        """获取文件状态"""
        try:
            mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
            days_ago = (datetime.now() - mtime).days
            
            if days_ago <= 7:
                return "🔥 活跃"
            elif days_ago <= 30:
                return "✅ 稳定"
            else:
                return "😴 静态"
        except Exception:
            return "❓ 未知"
    
    def generate_index_document(self) -> str:
        """生成完整的索引文档"""
        files_by_dir = self.scan_secondary_dev_files()
        
        content = f"""# 二次开发代码索引

## 📋 概述

本文档自动生成于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}，记录了所有带有 `[二次开发]` 标识的代码文件。

## 🏷️ 标识规范

### 文件头标识格式
```python
\"\"\"
[二次开发] 模块名称
功能描述

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：[具体功能描述]
- 维护者：开发团队
- 最后更新：[日期]
- 相关任务：[任务编号]
- 依赖关系：[依赖说明]
\"\"\"
```

### 目录标识格式
```python
\"\"\"
[二次开发目录] 目录名称
此目录下的所有文件均为二次开发代码，不属于langbot原生代码
\"\"\"
```

## 📁 二次开发代码索引

"""
        
        total_files = 0
        
        for dir_path in sorted(files_by_dir.keys()):
            files = files_by_dir[dir_path]
            total_files += len(files)
            
            content += f"\n### {dir_path} ({len(files)}个文件)\n\n"
            content += "| 文件名 | 功能描述 | 状态 | 最后更新 |\n"
            content += "|--------|----------|------|----------|\n"
            
            for filename, description, status in files:
                # 获取最后更新时间
                file_path = self.root_path / dir_path / filename
                try:
                    mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                    last_update = mtime.strftime('%Y-%m-%d')
                except:
                    last_update = "未知"
                
                content += f"| `{filename}` | {description} | {status} | {last_update} |\n"
        
        content += f"""
## 📊 统计信息

### 代码分布统计
- **总文件数**: {total_files}个二次开发文件
- **Flux模块**: {len(files_by_dir.get('pkg/workers/flux', []))}个文件
- **Kontext模块**: {len(files_by_dir.get('pkg/workers/kontext', []))}个文件
- **Kontext API模块**: {len(files_by_dir.get('pkg/workers/kontext_api', []))}个文件
- **共享模块**: {len(files_by_dir.get('pkg/workers/shared', []))}个文件
- **核心模块**: {len(files_by_dir.get('pkg/core/workflow', [])) + len(files_by_dir.get('pkg/core/session', []))}个文件
- **适配器模块**: {len(files_by_dir.get('pkg/adapters', []))}个文件

### 状态分布统计
"""
        
        # 统计状态分布
        status_counts = {}
        for files in files_by_dir.values():
            for _, _, status in files:
                status_counts[status] = status_counts.get(status, 0) + 1
        
        for status, count in sorted(status_counts.items()):
            content += f"- **{status}**: {count}个文件\n"
        
        content += f"""
## 🔧 维护说明

### 自动生成
- 此文档由 `check_dev_index.py` 脚本自动生成
- 基于文件头注释中的 `[二次开发]` 标识进行扫描
- 建议定期运行脚本更新索引

### 手动维护
- 添加新文件时，确保包含标准化的 `[二次开发]` 标识
- 修改文件功能时，更新文件头注释中的描述
- 删除文件时，运行脚本重新生成索引

## 📋 使用说明

### 运行检查脚本
```bash
python check_dev_index.py
```

### 生成索引文档
```bash
python check_dev_index.py --generate
```

### 检查完整性
```bash
python check_dev_index.py --check
```

---

**维护者**: 开发团队  
**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**状态**: 自动生成
"""
        
        return content
    
    def save_index_document(self, content: str):
        """保存索引文档"""
        with open(self.index_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 索引文档已保存到: {self.index_file}")
    
    def check_completeness(self) -> bool:
        """检查索引完整性"""
        print("🔍 检查二次开发文件索引完整性...")
        print("=" * 50)
        
        files_by_dir = self.scan_secondary_dev_files()
        
        if not files_by_dir:
            print("❌ 未找到任何带有[二次开发]标识的文件")
            return False
        
        total_files = sum(len(files) for files in files_by_dir.values())
        
        print(f"📊 发现 {total_files} 个二次开发文件:")
        
        for dir_path, files in sorted(files_by_dir.items()):
            print(f"\n📁 {dir_path} ({len(files)}个文件)")
            for filename, description, status in files:
                print(f"  ✅ {filename} - {description}")
        
        return True
    
    def check_file_status(self):
        """检查文件状态"""
        print("\n🔍 检查文件状态...")
        print("=" * 50)
        
        files_by_dir = self.scan_secondary_dev_files()
        
        for dir_path, files in files_by_dir.items():
            print(f"\n📁 {dir_path} ({len(files)}个文件)")
            for filename, description, status in files:
                file_path = self.root_path / dir_path / filename
                if file_path.exists():
                    size = file_path.stat().st_size
                    print(f"  {filename:<30} {status} ({size:>6}B)")

def main():
    import sys
    
    checker = DevIndexChecker()
    
    print("🚀 二次开发文件索引检查工具")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--generate":
            print("📝 生成索引文档...")
            content = checker.generate_index_document()
            checker.save_index_document(content)
            return
        elif sys.argv[1] == "--check":
            print("🔍 仅检查完整性...")
            checker.check_completeness()
            return
    
    # 默认行为：检查完整性并生成索引
    is_complete = checker.check_completeness()
    
    if is_complete:
        print("\n🎉 发现二次开发文件，正在生成索引...")
        content = checker.generate_index_document()
        checker.save_index_document(content)
    else:
        print("\n⚠️  未发现二次开发文件或索引不完整")
    
    # 检查文件状态
    checker.check_file_status()
    
    print(f"\n📋 维护建议:")
    print(f"  1. 定期运行此脚本检查索引完整性")
    print(f"  2. 添加新文件时确保包含[二次开发]标识")
    print(f"  3. 使用 --generate 参数重新生成索引")
    print(f"  4. 使用 --check 参数仅检查完整性")

if __name__ == "__main__":
    main()
