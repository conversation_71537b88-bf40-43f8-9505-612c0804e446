#!/bin/bash

# 禁用语音插件脚本
# 这个脚本可以帮助用户禁用 NewChatVoice 插件以避免依赖下载问题

echo "🔇 语音插件禁用工具"
echo "=================="

PLUGIN_DIR="plugins/NewChatVoice"
DISABLED_DIR="plugins/NewChatVoice.disabled"

if [ -d "$PLUGIN_DIR" ]; then
    echo "📁 找到语音插件目录: $PLUGIN_DIR"
    
    if [ -d "$DISABLED_DIR" ]; then
        echo "⚠️  发现已禁用的插件目录: $DISABLED_DIR"
        echo "   请先删除或重命名该目录"
        exit 1
    fi
    
    echo "🔄 正在禁用语音插件..."
    mv "$PLUGIN_DIR" "$DISABLED_DIR"
    
    if [ $? -eq 0 ]; then
        echo "✅ 语音插件已成功禁用"
        echo "📝 插件目录已重命名为: $DISABLED_DIR"
        echo ""
        echo "💡 如需重新启用，请运行:"
        echo "   mv $DISABLED_DIR $PLUGIN_DIR"
        echo ""
        echo "🚀 现在可以重新启动 langbot，将不会下载语音相关依赖"
    else
        echo "❌ 禁用插件失败"
        exit 1
    fi
else
    echo "❌ 未找到语音插件目录: $PLUGIN_DIR"
    echo ""
    if [ -d "$DISABLED_DIR" ]; then
        echo "📁 发现已禁用的插件目录: $DISABLED_DIR"
        echo "💡 如需重新启用，请运行:"
        echo "   mv $DISABLED_DIR $PLUGIN_DIR"
    fi
    exit 1
fi 