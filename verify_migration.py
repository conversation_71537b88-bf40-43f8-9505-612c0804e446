#!/usr/bin/env python3
"""
验证YAML到JSON迁移效果

作者：二次开发标识
"""

import json
import os

def verify_migration():
    """验证迁移效果"""
    config_file = "config/lora_models.json"
    
    if not os.path.exists(config_file):
        print("❌ JSON配置文件不存在")
        return
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    models = config.get('models', [])
    
    # 统计迁移情况
    migrated_models = [m for m in models if m.get('migrated_from_yaml', False)]
    manually_maintained = [m for m in models if m.get('manually_maintained', False)]
    
    print(f"📊 迁移统计:")
    print(f"   总模型数量: {len(models)}")
    print(f"   已迁移模型: {len(migrated_models)}")
    print(f"   人工维护模型: {len(manually_maintained)}")
    
    # 检查关键模型的触发词质量
    key_models = [
        "artchitecture_Flux1",
        "ASTRA_Flux_OC_Vbeta-2", 
        "Anime_niji",
        "niji_flux"
    ]
    
    print(f"\n🎯 关键模型触发词检查:")
    for model_name in key_models:
        model = next((m for m in models if m['name'] == model_name), None)
        if model:
            triggers = model.get('trigger_words', [])
            has_chinese = any('\u4e00' <= char <= '\u9fff' for trigger in triggers for char in trigger)
            has_english = any(trigger.isascii() and trigger.isalpha() for trigger in triggers)
            
            status = "✅" if len(triggers) >= 3 and has_chinese and has_english else "⚠️"
            print(f"   {status} {model_name}: {triggers}")
        else:
            print(f"   ❌ {model_name}: 未找到")
    
    # 检查分类分布
    category_stats = {}
    for model in models:
        category = model.get('category', 'unknown')
        category_stats[category] = category_stats.get(category, 0) + 1
    
    print(f"\n📋 分类分布:")
    for category, count in sorted(category_stats.items()):
        print(f"   {category}: {count} 个")
    
    # 检查需要改进的模型
    needs_improvement = []
    for model in models:
        triggers = model.get('trigger_words', [])
        if (len(triggers) < 2 or 
            model.get('category') == 'other' or
            not model.get('manually_maintained', False)):
            needs_improvement.append(model['name'])
    
    print(f"\n⚠️ 需要改进的模型 ({len(needs_improvement)} 个):")
    for name in needs_improvement[:10]:  # 只显示前10个
        print(f"   - {name}")
    if len(needs_improvement) > 10:
        print(f"   ... 还有 {len(needs_improvement) - 10} 个")

if __name__ == "__main__":
    verify_migration()
