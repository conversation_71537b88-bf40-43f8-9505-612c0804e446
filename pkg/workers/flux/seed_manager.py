"""
[二次开发] Flux 种子管理器
负责种子历史记录、复用和管理
这是 Flux 工作流的专有特性

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：Flux工作流的种子管理和复用
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：DEV-02 状态管理实现
- 依赖关系：依赖本地JSON文件存储
"""

import json
import time
import random
import os
from typing import Optional, List, Dict, Any
import logging

from .flux_workflow_models import SeedRecord, SeedInstruction, FluxParameters


class SeedManager:
    """Flux 种子管理器"""
    
    def __init__(self, max_history: int = 50, storage_file: str = "temp/flux_seed_history.json"):
        self.max_history = max_history
        self.storage_file = storage_file
        self.logger = logging.getLogger(__name__)
        
        # 当前种子状态
        self.last_seed: Optional[int] = None
        self.current_seed: Optional[int] = None
        
        # 种子历史记录
        self.seed_history: List[SeedRecord] = []
        
        # 加载历史记录
        self._load_history()
    
    def process_seed_instruction(self, params: FluxParameters) -> int:
        """
        处理种子指令并返回最终种子值
        
        Args:
            params: Flux参数，包含种子指令
            
        Returns:
            int: 最终使用的种子值
        """
        final_seed = -1
        
        try:
            if params.seed_instruction == SeedInstruction.USE_LAST:
                if self.last_seed is not None:
                    final_seed = self.last_seed
                    self.logger.info(f"使用上一次的种子: {final_seed}")
                else:
                    # 没有上一次种子，生成随机种子
                    final_seed = self._generate_random_seed()
                    self.logger.warning(f"没有上一次种子记录，使用随机种子: {final_seed}")
                    
            elif params.seed_instruction == SeedInstruction.SPECIFIC:
                if params.seed > 0:
                    final_seed = params.seed
                    self.logger.info(f"使用指定种子: {final_seed}")
                else:
                    # 指定种子无效，生成随机种子
                    final_seed = self._generate_random_seed()
                    self.logger.warning(f"指定种子无效，使用随机种子: {final_seed}")
                    
            else:  # SeedInstruction.RANDOM 或其他情况
                final_seed = self._generate_random_seed()
                self.logger.info(f"生成随机种子: {final_seed}")
                
        except Exception as e:
            self.logger.error(f"处理种子指令失败: {e}")
            final_seed = self._generate_random_seed()
            self.logger.info(f"异常处理，使用随机种子: {final_seed}")
        
        # 更新当前种子
        self.current_seed = final_seed
        
        return final_seed
    
    def record_seed_usage(self, seed: int, prompt: str, parameters: Dict[str, Any], success: bool = True):
        """
        记录种子使用情况
        
        Args:
            seed: 使用的种子
            prompt: 提示词
            parameters: 生成参数
            success: 是否成功生成
        """
        try:
            # 创建种子记录
            record = SeedRecord(
                seed=seed,
                prompt=prompt,
                parameters=parameters,
                success=success
            )
            
            # 添加到历史记录
            self.seed_history.append(record)
            
            # 如果成功，更新last_seed
            if success:
                self.last_seed = seed
                self.logger.info(f"记录成功种子: {seed}")
            else:
                self.logger.warning(f"记录失败种子: {seed}")
            
            # 清理旧记录
            self._cleanup_old_records()
            
            # 保存到文件
            self._save_history()
            
        except Exception as e:
            self.logger.error(f"记录种子使用失败: {e}")
    
    def get_recent_seeds(self, count: int = 10, success_only: bool = True) -> List[SeedRecord]:
        """
        获取最近的种子记录
        
        Args:
            count: 返回记录数量
            success_only: 只返回成功的记录
            
        Returns:
            List[SeedRecord]: 种子记录列表
        """
        try:
            # 过滤记录
            filtered_records = self.seed_history
            if success_only:
                filtered_records = [r for r in self.seed_history if r.success]
            
            # 按时间排序（最新的在前）
            sorted_records = sorted(filtered_records, key=lambda x: x.timestamp, reverse=True)
            
            # 返回指定数量
            return sorted_records[:count]
            
        except Exception as e:
            self.logger.error(f"获取最近种子失败: {e}")
            return []
    
    def find_seed_by_prompt(self, prompt: str, similarity_threshold: float = 0.7) -> List[SeedRecord]:
        """
        根据提示词查找相似的种子记录
        
        Args:
            prompt: 查找的提示词
            similarity_threshold: 相似度阈值
            
        Returns:
            List[SeedRecord]: 匹配的种子记录
        """
        try:
            matches = []
            prompt_lower = prompt.lower()
            
            for record in self.seed_history:
                if not record.success:
                    continue
                
                # 简单的文本相似度计算
                record_prompt_lower = record.prompt.lower()
                similarity = self._calculate_text_similarity(prompt_lower, record_prompt_lower)
                
                if similarity >= similarity_threshold:
                    matches.append(record)
            
            # 按相似度排序
            matches.sort(key=lambda x: self._calculate_text_similarity(prompt_lower, x.prompt.lower()), reverse=True)
            
            return matches
            
        except Exception as e:
            self.logger.error(f"根据提示词查找种子失败: {e}")
            return []
    
    def get_seed_statistics(self) -> Dict[str, Any]:
        """
        获取种子使用统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            total_records = len(self.seed_history)
            success_records = len([r for r in self.seed_history if r.success])
            
            # 最近24小时的记录
            recent_time = time.time() - 24 * 3600
            recent_records = [r for r in self.seed_history if r.timestamp >= recent_time]
            
            # 最常用的种子
            seed_counts = {}
            for record in self.seed_history:
                if record.success:
                    seed_counts[record.seed] = seed_counts.get(record.seed, 0) + 1
            
            most_used_seeds = sorted(seed_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            
            return {
                'total_records': total_records,
                'success_records': success_records,
                'success_rate': success_records / total_records if total_records > 0 else 0,
                'recent_24h_records': len(recent_records),
                'last_seed': self.last_seed,
                'current_seed': self.current_seed,
                'most_used_seeds': most_used_seeds,
                'oldest_record_time': min([r.timestamp for r in self.seed_history]) if self.seed_history else None,
                'newest_record_time': max([r.timestamp for r in self.seed_history]) if self.seed_history else None
            }
            
        except Exception as e:
            self.logger.error(f"获取种子统计失败: {e}")
            return {}
    
    def clear_history(self, keep_recent: int = 10):
        """
        清理种子历史记录
        
        Args:
            keep_recent: 保留最近的记录数量
        """
        try:
            if keep_recent > 0:
                # 保留最近的记录
                sorted_records = sorted(self.seed_history, key=lambda x: x.timestamp, reverse=True)
                self.seed_history = sorted_records[:keep_recent]
            else:
                # 清空所有记录
                self.seed_history = []
                self.last_seed = None
            
            # 保存到文件
            self._save_history()
            
            self.logger.info(f"清理种子历史，保留最近 {keep_recent} 条记录")
            
        except Exception as e:
            self.logger.error(f"清理种子历史失败: {e}")
    
    def export_history(self, file_path: str) -> bool:
        """
        导出种子历史记录
        
        Args:
            file_path: 导出文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            export_data = {
                'export_time': time.time(),
                'total_records': len(self.seed_history),
                'last_seed': self.last_seed,
                'records': [record.to_dict() for record in self.seed_history]
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"种子历史导出成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出种子历史失败: {e}")
            return False
    
    def import_history(self, file_path: str, merge: bool = True) -> bool:
        """
        导入种子历史记录
        
        Args:
            file_path: 导入文件路径
            merge: 是否合并到现有记录
            
        Returns:
            bool: 是否成功
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            # 解析记录
            imported_records = []
            for record_data in import_data.get('records', []):
                record = SeedRecord(
                    seed=record_data['seed'],
                    timestamp=record_data['timestamp'],
                    prompt=record_data['prompt'],
                    parameters=record_data['parameters'],
                    success=record_data['success']
                )
                imported_records.append(record)
            
            if merge:
                # 合并记录
                self.seed_history.extend(imported_records)
                # 去重并排序
                seen_records = {}
                unique_records = []
                for record in self.seed_history:
                    key = f"{record.seed}_{record.timestamp}"
                    if key not in seen_records:
                        seen_records[key] = True
                        unique_records.append(record)
                
                self.seed_history = sorted(unique_records, key=lambda x: x.timestamp)
            else:
                # 替换记录
                self.seed_history = imported_records
            
            # 更新last_seed
            if imported_records:
                latest_success = None
                for record in sorted(self.seed_history, key=lambda x: x.timestamp, reverse=True):
                    if record.success:
                        latest_success = record
                        break
                
                if latest_success:
                    self.last_seed = latest_success.seed
            
            # 清理和保存
            self._cleanup_old_records()
            self._save_history()
            
            self.logger.info(f"种子历史导入成功: {file_path}, 导入 {len(imported_records)} 条记录")
            return True
            
        except Exception as e:
            self.logger.error(f"导入种子历史失败: {e}")
            return False
    
    def _generate_random_seed(self) -> int:
        """生成随机种子"""
        new_seed = random.randint(1, 999999999)
        self.logger.info(f"🎲 随机种子生成: {new_seed} (上次种子: {self.last_seed})")
        
        # 检查是否与上次种子相同（极低概率但仍需检查）
        if new_seed == self.last_seed:
            self.logger.warning(f"⚠️ 检测到种子重复！重新生成...")
            new_seed = random.randint(1, 999999999)
            self.logger.info(f"🎲 重新生成种子: {new_seed}")
        
        return new_seed
    
    def _cleanup_old_records(self):
        """清理旧记录"""
        if len(self.seed_history) > self.max_history:
            # 按时间排序，保留最新的记录
            sorted_records = sorted(self.seed_history, key=lambda x: x.timestamp, reverse=True)
            self.seed_history = sorted_records[:self.max_history]
            self.logger.info(f"清理旧种子记录，保留最新 {self.max_history} 条")
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简单实现）"""
        try:
            # 简单的词汇重叠相似度
            words1 = set(text1.split())
            words2 = set(text2.split())
            
            if not words1 and not words2:
                return 1.0
            
            if not words1 or not words2:
                return 0.0
            
            intersection = words1.intersection(words2)
            union = words1.union(words2)
            
            return len(intersection) / len(union)
            
        except Exception:
            return 0.0
    
    def _load_history(self):
        """加载历史记录"""
        try:
            if os.path.exists(self.storage_file):
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 解析记录
                for record_data in data.get('records', []):
                    record = SeedRecord(
                        seed=record_data['seed'],
                        timestamp=record_data.get('timestamp', time.time()),
                        prompt=record_data.get('prompt', ''),
                        parameters=record_data.get('parameters', {}),
                        success=record_data.get('success', True)
                    )
                    self.seed_history.append(record)
                
                # 恢复last_seed
                self.last_seed = data.get('last_seed')
                
                self.logger.info(f"加载种子历史: {len(self.seed_history)} 条记录")
            else:
                self.logger.info("种子历史文件不存在，从空开始")
                
        except Exception as e:
            self.logger.error(f"加载种子历史失败: {e}")
            self.seed_history = []
            self.last_seed = None
    
    def _save_history(self):
        """保存历史记录"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.storage_file), exist_ok=True)
            
            # 准备数据
            data = {
                'last_seed': self.last_seed,
                'save_time': time.time(),
                'total_records': len(self.seed_history),
                'records': [record.to_dict() for record in self.seed_history]
            }
            
            # 保存到文件
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存种子历史失败: {e}")


# 全局单例
seed_manager: Optional[SeedManager] = None

def get_seed_manager() -> SeedManager:
    """获取种子管理器单例"""
    global seed_manager
    if seed_manager is None:
        seed_manager = SeedManager()
    return seed_manager 