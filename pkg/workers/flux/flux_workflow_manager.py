"""
[二次开发] Flux 工作流管理器
统一协调所有 Flux 专有模块，提供完整的 Flux 工作流执行能力
这是 Flux 工作流的主要入口

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：Flux工作流的统一管理和执行
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：DEV-04 业务逻辑实现
- 依赖关系：依赖langbot的工作流基类和ComfyUI客户端
"""

import json
import time
import asyncio
import aiohttp
import base64
import os
from typing import Dict, Any, Optional, List
import logging

from ...core import app
from ...provider import entities as llm_entities
from ...core.workflow.unified_routing_system import WorkflowSubType
from .flux_workflow_models import (
    FluxParameters, ExecutionResult,
    SeedInstruction
)
from .seed_manager import get_seed_manager
from .standard_nodes import get_standard_node_mapper
from .lora_integration_v2 import get_lora_integration_v2, LoraDownloadInProgressError
from .workflow_persistence_manager import get_workflow_persistence_manager
from pkg.core.workflow.manager_base import BaseWorkflowManager, WorkflowResult

class FluxWorkflowManager(BaseWorkflowManager):
    """Flux 工作流管理器"""
    
    def __init__(self, ap: Optional[app.Application] = None, pipeline_config: dict = {}):
        self.ap = ap
        self.pipeline_config = pipeline_config
        # 使用主应用的logger，确保日志能正确输出
        self.logger = logging.getLogger('langbot')
        
        # 获取ComfyUI配置
        comfyui_config = self.pipeline_config.get('ai', {}).get('comfyui-agent', {})
        self.api_url = comfyui_config.get('api-url', 'http://localhost:8188')
        self.ws_url = self.api_url.replace('http://', 'ws://').replace('https://', 'wss://')
        self.timeout = comfyui_config.get('timeout', 180)
        self.workflow_path = comfyui_config.get('workflow-path', 'workflows')
        
        # 初始化专有模块
        self.seed_manager = get_seed_manager()
        self.node_mapper = get_standard_node_mapper(self.workflow_path)
        self.lora_integration = get_lora_integration_v2()
        self.persistence_manager = get_workflow_persistence_manager()
        
        # 工作流状态
        self.current_session: Optional[Dict[str, Any]] = None
        self.current_workflow_data: Optional[Dict[str, Any]] = None
        self.last_execution_result: Optional[ExecutionResult] = None
        self.last_lora_status: str = ""  # LoRA应用状态消息
    
    def is_flux_request(self, text: str) -> bool:
        """
        判断是否是Flux图片生成请求
        """
        # 简单的关键词检测，复杂逻辑已迁移到统一路由系统
        flux_keywords = ['aigen', 'flux', '生成图片', '画图']
        return any(keyword in text.lower() for keyword in flux_keywords)

    async def preview_loras_for_prompt(self, prompt: str) -> List[str]:
        """
        预览将要为提示词选择的LoRA模型名称

        Args:
            prompt: 用户提示词

        Returns:
            List[str]: LoRA模型名称列表
        """
        try:
            # 创建临时参数对象
            temp_params = FluxParameters(prompt=prompt)

            # 选择LoRA模型
            selected_loras = await self.lora_integration.select_loras_for_prompt(temp_params)

            # 返回模型名称列表
            return [lora.name for lora in selected_loras]

        except Exception as e:
            self.logger.error(f"预览LoRA模型失败: {e}")
            return []

    async def handle_again_request(self, user_id: str, chat_id: str = "") -> WorkflowResult:
        """
        处理 --again 请求，重新执行上一次的工作流

        Args:
            user_id: 用户ID
            chat_id: 聊天ID

        Returns:
            WorkflowResult: 执行结果
        """
        try:
            self.logger.info(f"🔄 处理 --again 请求 - 用户: {user_id}")

            # 加载上一次的工作流数据
            snapshot = self.persistence_manager.load_last_successful_workflow(user_id, chat_id)
            if not snapshot:
                return WorkflowResult(
                    success=False,
                    error_message="没有找到上一次的工作流数据，请先执行一次正常的图片生成"
                )

            self.logger.info(f"✅ 找到上一次工作流 - 类型: {snapshot.workflow_type}, 提示词: {snapshot.user_prompt[:50]}...")

            self.logger.info(f"📝 原始提示词: {snapshot.user_prompt}")
            self.logger.info(f"🎨 优化提示词: {snapshot.optimized_prompt}")

            # 直接执行工作流（跳过所有准备步骤）
            # 注意：again_manager已经处理了seed更新
            result = await self._execute_comfyui_workflow(snapshot.workflow_data)

            if result:
                # 🔥 修复：正确提取image_data或text_data
                image_data = result.get('image_data') if isinstance(result, dict) else result
                text_data = result.get('text_data') if isinstance(result, dict) else None

                # 执行成功，返回结果
                return WorkflowResult(
                    success=True,
                    image_data=image_data,
                    text_data=text_data,
                    metadata={
                        'workflow_type': snapshot.workflow_type,
                        'user_prompt': snapshot.user_prompt,
                        'optimized_prompt': snapshot.optimized_prompt,
                        'lora_info': snapshot.lora_info,
                        'parameters': snapshot.parameters,
                        'is_again_request': True,
                        'original_timestamp': snapshot.timestamp
                    }
                )
            else:
                return WorkflowResult(
                    success=False,
                    error_message="重新执行工作流失败"
                )

        except Exception as e:
            self.logger.error(f"❌ 处理 --again 请求失败: {e}")
            return WorkflowResult(
                success=False,
                error_message=f"重新生成失败: {str(e)}"
            )

    async def execute_workflow(self, prompt: str, query: Any, session_images: Optional[List] = None) -> WorkflowResult:
        """
        执行Flux工作流 - 统一入口方法
        
        Args:
            prompt: 用户输入文本
            query: 查询对象
            session_images: 会话图片列表
            
        Returns:
            WorkflowResult: 执行结果
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"开始执行Flux工作流: {prompt[:30]}...")  # 截短提示词显示
            
            # 步骤1: 获取工作流文件和参数
            workflow_file, adapted_params, workflow_subtype = await self._get_workflow_and_params(prompt, query, session_images)
            if not workflow_file:
                return WorkflowResult(
                    success=False,
                    error_message="无法确定工作流文件"
                )
            
            # 步骤2: 转换为FluxParameters并处理
            params = self._convert_to_flux_parameters(adapted_params)
            
            # 步骤3: 种子处理
            final_seed = self.seed_manager.process_seed_instruction(params)
            params.seed = final_seed
            
            # 步骤4: LoRA选择和优化
            # 检查 query 对象是否有 civitai_query 属性
            civitai_query_str = getattr(query, 'civitai_query', None)

            # 🔥 调试：检查batch_size参数传递
            self.logger.info(f"🔍 [DEBUG] FluxParameters batch_size: {params.batch_size}")

            # 构建用户偏好参数
            user_preferences = self._build_user_preferences(adapted_params)

            selected_loras = await self.lora_integration.select_loras_for_prompt(
                params,
                use_civitai=bool(civitai_query_str),
                civitai_query=civitai_query_str or "",  # 🔥 确保传递字符串类型
                user_preferences=user_preferences
            )

            optimized_loras = self.lora_integration.optimize_lora_weights(
                selected_loras,
                params.quality_level.value
            )

            # 🔥 修复：从统一参数服务获取no_lora参数，而不是从原始文本检测
            use_no_lora = adapted_params.original_llm_params.get('no_lora', False)
            self.logger.info(f"🔍 [DEBUG] --lora no检测结果: {use_no_lora}")

            if use_no_lora:
                self.logger.info("🚫 检测到 --lora no 参数，将在工作流构建后bypass所有LoRA节点")

            # 步骤5: 工作流构建
            workflow_data = await self._build_workflow(workflow_file, params, optimized_loras, session_images, workflow_subtype, use_no_lora)
            if not workflow_data:
                return WorkflowResult(
                    success=False,
                    error_message="工作流构建失败"
                )

            # 🔥 调试：检查EmptyLatentImage节点的batch_size配置
            for node_id, node_data in workflow_data.items():
                if isinstance(node_data, dict) and node_data.get("class_type") == "EmptyLatentImage":
                    batch_size_in_workflow = node_data.get("inputs", {}).get("batch_size", "未设置")
                    self.logger.info(f"🔍 [DEBUG] EmptyLatentImage节点{node_id} batch_size: {batch_size_in_workflow}")
                    break

            # 如果使用了 --no-lora 参数，bypass所有LoRA节点
            if use_no_lora:
                self.logger.info("🚫 开始bypass所有LoRA节点")
                workflow_data, bypass_message = self.lora_integration.bypass_all_lora_nodes(workflow_data)
                self.logger.info(f"🚫 LoRA bypass结果: {bypass_message}")
                # 确保状态消息正确显示
                self.last_lora_status = "🚫 已禁用所有LoRA模型"
            
            # 🔥 修复：提前准备保存数据，确保--again功能可用
            user_id = getattr(query, 'sender_id', 'unknown')
            chat_id = getattr(query, 'launcher_id', '')

            # 准备图片信息
            image_info = {}
            if session_images:
                image_info = {
                    'image_count': len(session_images),
                    'has_control_images': any(img.is_control_image() for img in session_images),
                    'has_reference_images': any(img.is_reference_image() for img in session_images),
                }

            self.logger.debug(f"🔍 [DEBUG] 准备保存工作流 - 用户: {user_id}, 聊天: {chat_id}")

            # 步骤6: 执行ComfyUI工作流
            execution_result = await self._execute_comfyui_workflow(workflow_data, workflow_subtype)

            if execution_result and (execution_result.get('image_data') or execution_result.get('text_data')):
                execution_time = time.time() - start_time
                self.logger.info(f"Flux工作流执行成功，耗时: {execution_time:.2f}秒")

                # 提取结果数据
                image_data = execution_result.get('image_data')
                text_data = execution_result.get('text_data')

                # 🔥 新增：记录结果类型
                result_type = "text" if text_data else "image"
                self.logger.info(f"工作流结果类型: {result_type}")

                # 🔥 修复：使用统一Again管理器保存工作流数据
                try:
                    from ..shared.again_manager import unified_again_manager

                    success = unified_again_manager.save_successful_workflow(
                        workflow_data=workflow_data,
                        user_prompt=prompt,
                        optimized_prompt=params.prompt,
                        workflow_type=workflow_file,
                        parameters={
                            'width': params.width,
                            'height': params.height,
                            'steps': params.steps,
                            'guidance': params.guidance,
                            'seed': final_seed
                        },
                        lora_info={
                            'loras_used': [lora.name for lora in optimized_loras],
                            'lora_status': self.last_lora_status
                        },
                        image_info=image_info,
                        user_id=user_id,
                        chat_id=chat_id
                    )
                    if success:
                        self.logger.info("✅ 工作流数据已保存，支持 --again 功能")
                    else:
                        self.logger.warning("⚠️ 工作流数据保存失败")
                except Exception as save_error:
                    self.logger.error(f"❌ 保存工作流数据异常: {save_error}")
                    import traceback
                    self.logger.error(f"保存异常详情: {traceback.format_exc()}")

                return WorkflowResult(
                    success=True,
                    image_data=image_data,
                    text_data=text_data,  # 新增：支持文本输出
                    metadata={
                        'execution_time': execution_time,
                        'workflow_file': workflow_file,
                        'final_seed': final_seed,
                        'seed': final_seed,  # 添加 seed 字段供完成消息使用
                        'loras_used': [lora.name for lora in optimized_loras],  # 修正键名
                        'used_loras': [lora.name for lora in optimized_loras],  # 保留兼容性
                        'lora_status': getattr(self, 'last_lora_status', ''),  # 🔥 添加LoRA状态信息
                        'prompt': params.prompt,
                        'size': f"{params.width}x{params.height}",
                        'workflow_subtype': workflow_subtype.value if workflow_subtype else None
                    }
                )
            else:
                # 🔥 新增：即使执行失败也记录调试信息
                self.logger.error(f"❌ ComfyUI工作流执行失败，无法保存成功数据")
                self.logger.debug(f"🔍 [DEBUG] 失败的工作流参数 - 用户: {user_id}, 文件: {workflow_file}")

                return WorkflowResult(
                    success=False,
                    error_message="ComfyUI工作流执行失败"
                )
        except LoraDownloadInProgressError as e:
            self.logger.info(f"工作流已暂停，等待后台下载LoRA模型: {e.model_name}")
            return WorkflowResult(
                success=False, # 表示本次生成未完成
                error_message=f"⏳ 正在为您从Civitai下载新的LoRA模型 \"{e.model_name}\"\n\n这可能需要几分钟，请稍后重试。"
            )
        except Exception as e:
            self.logger.error(f"Flux工作流执行异常: {e}")
            return WorkflowResult(
                success=False,
                error_message=f"Flux工作流执行异常: {e}"
            )
    
    async def _get_workflow_and_params(self, user_text: str, query: Any, session_images: Optional[List]) -> tuple[Optional[str], Any, Optional[WorkflowSubType]]:
        """获取工作流文件、参数和工作流子类型"""
        # 检查是否有强制指定的工作流文件
        workflow_file = None
        if query and hasattr(query, 'session') and query.session:
            workflow_file = getattr(query.session, 'forced_workflow_file', None)
            if workflow_file:
                self.logger.info(f"使用强制指定的工作流文件: {workflow_file}")
        
        if not workflow_file:
            # 使用统一路由系统
            from ...core.workflow.unified_routing_system import get_unified_router
            unified_router = get_unified_router(self.ap)
            
            # 🔥 关键修复：确保query对象有正确的pipeline_config和ap属性
            if query and not query.pipeline_config:
                query.pipeline_config = self.pipeline_config.copy()
                self.logger.info("✅ [FIX] 已将pipeline_config设置到query对象")
            elif query:
                self.logger.info("✅ [FIX] query对象已有pipeline_config")
            
            # 🔥 关键修复：确保query对象有ap属性，供Flux优化器使用
            if query:
                from ...core.workflow.query_utils import ensure_query_has_ap
                ensure_query_has_ap(query, self.ap, self.logger)
            
            routing_result = await unified_router.route_unified(
                user_text=user_text,
                has_images=bool(session_images and len(session_images) > 0),
                image_count=len(session_images) if session_images else 0,
                query=query
            )
            
            if not routing_result:
                self.logger.error("统一路由系统返回None")
                return None, None, None
            
            workflow_file = routing_result.workflow_file
            self.logger.info(f"统一路由系统选择工作流: {workflow_file}")
            self.logger.info(f"工作流文件类型: {type(workflow_file)}")
            self.logger.info(f"工作流文件是否为空: {not workflow_file}")
            self.logger.info(f"工作流路径: {self.workflow_path}")
            self.logger.info(f"完整工作流文件路径: {os.path.join(self.workflow_path, workflow_file)}")
            self.logger.info(f"工作流文件是否存在: {os.path.exists(os.path.join(self.workflow_path, workflow_file))}")
            
            # 检查workflow_file是否有效
            if not workflow_file or not workflow_file.strip():
                self.logger.error(f"工作流文件无效: '{workflow_file}'")
                return None, None, None
        
        # 确定工作流子类型
        workflow_subtype = self._determine_workflow_subtype(workflow_file)

        # 获取参数
        if query and hasattr(query, 'analyzed_parameters') and query.analyzed_parameters:
            adapted_params = query.analyzed_parameters
            self.logger.info("使用已分析好的参数")
            # 🔥 调试：检查已分析参数的batch_size
            self.logger.info(f"🔍 [DEBUG] query.analyzed_parameters.batch_size: {getattr(adapted_params, 'batch_size', '未设置')}")
        else:
            # 使用统一参数服务
            self.logger.info("🔍 [DEBUG] 调用统一参数服务")
            adapted_params = await self._get_parameters_from_service(user_text, query, workflow_file)
            self.logger.info(f"🔍 [DEBUG] 统一参数服务返回的batch_size: {getattr(adapted_params, 'batch_size', '未设置')}")

        return workflow_file, adapted_params, workflow_subtype
    
    async def _get_parameters_from_service(self, user_text: str, query: Any, workflow_file: str) -> Any:
        """从统一参数服务获取参数"""
        from ...core.workflow.unified_parameter_service import get_unified_parameter_service
        from ...core.workflow.workflow_parameter_adapter import AdaptedParameters, WorkflowType
        
        # 确定工作流子类型
        workflow_subtype = self._determine_workflow_subtype(workflow_file)
        
        # 🔥 修复：获取原始用户输入用于参数解析
        original_user_text = user_text  # 默认使用传入的user_text
        if query and hasattr(query, 'session') and query.session and hasattr(query.session, 'messages') and query.session.messages:
            trigger_message = query.session.messages[0]
            if hasattr(trigger_message, 'content') and trigger_message.content:
                original_user_text = trigger_message.content
                self.logger.info(f"🔍 [DEBUG] 使用原始用户输入进行参数分析: {original_user_text[:100]}...")

        # 使用统一参数服务
        unified_param_service = get_unified_parameter_service(self.ap)
        param_result = await unified_param_service.analyze_user_input(
            user_text=original_user_text,  # 🔥 使用原始用户输入
            query=query,
            workflow_subtype=workflow_subtype
        )
        
        if param_result.success:
            self.logger.debug(f"统一参数服务分析成功: {param_result.final_params}")
            # 🔥 调试：检查统一参数服务返回的batch_size
            batch_size_from_service = param_result.final_params.get('batch_size', 1)
            self.logger.info(f"🔍 [DEBUG] 统一参数服务返回的batch_size详细: {batch_size_from_service} (类型: {type(batch_size_from_service)})")
            self.logger.info(f"🔍 [DEBUG] param_result.final_params完整内容: {param_result.final_params}")

            return AdaptedParameters(
                workflow_type=WorkflowType.AIGEN_TEXT_ONLY,
                prompt=param_result.final_params.get('prompt', user_text),
                negative_prompt=param_result.final_params.get('negative_prompt', ''),
                width=param_result.final_params.get('width', 1024),
                height=param_result.final_params.get('height', 1024),
                steps=param_result.final_params.get('steps', 20),
                guidance=param_result.final_params.get('guidance', 7.5),
                seed=param_result.final_params.get('seed', -1),
                batch_size=batch_size_from_service,
                original_llm_params=param_result.final_params,
                adaptation_notes=[f"统一参数服务分析，置信度: {param_result.llm_params.confidence if param_result.llm_params.success else 0.0}"]
            )
        else:
            self.logger.warning(f"统一参数服务分析失败: {param_result.error_message}")
            # 创建简单的参数对象作为回退
            return AdaptedParameters(
                workflow_type=WorkflowType.AIGEN_TEXT_ONLY,
                prompt=user_text,
                negative_prompt="",
                width=1024,
                height=1024,
                steps=20,
                guidance=7.5,
                seed=-1,
                batch_size=1,
                adaptation_notes=["统一参数服务失败，使用默认参数"]
            )

    def _build_user_preferences(self, adapted_params: Any) -> dict:
        """
        从适配参数中构建用户偏好设置

        Args:
            adapted_params: 适配后的参数对象

        Returns:
            dict: 用户偏好设置
        """
        user_preferences = {}

        # 检查是否有原始LLM参数
        if hasattr(adapted_params, 'original_llm_params') and adapted_params.original_llm_params:
            llm_params = adapted_params.original_llm_params

            # 提取LoRA偏好参数
            if 'lora_prefer' in llm_params:
                user_preferences['prefer_keywords'] = llm_params['lora_prefer']

            if 'lora_exclude' in llm_params:
                user_preferences['exclude_keywords'] = llm_params['lora_exclude']

        return user_preferences

    def _convert_to_flux_parameters(self, adapted_params: Any) -> FluxParameters:
        """转换为FluxParameters"""
        params = FluxParameters()
        params.prompt = adapted_params.prompt
        params.negative_prompt = adapted_params.negative_prompt

        if adapted_params.width is not None and adapted_params.height is not None:
            params.width = adapted_params.width
            params.height = adapted_params.height

        if adapted_params.steps is not None:
            params.steps = adapted_params.steps
        if adapted_params.guidance is not None:
            params.guidance = adapted_params.guidance
        if adapted_params.batch_size is not None:
            params.batch_size = adapted_params.batch_size

        # 🔥 调试：最终的batch_size值
        self.logger.info(f"🔍 [DEBUG] 最终FluxParameters.batch_size: {params.batch_size}")
        
        if adapted_params.seed is not None:
            if adapted_params.seed > 0:
                params.seed = adapted_params.seed
                params.seed_instruction = SeedInstruction.SPECIFIC
            else:
                params.seed_instruction = SeedInstruction.RANDOM
        
        self.logger.info(f"参数处理完成: prompt='{params.prompt[:50]}...', size={params.width}x{params.height}")
        return params
    
    async def _build_workflow(self, workflow_file: str, params: FluxParameters, optimized_loras: List, session_images: Optional[List], workflow_subtype: Optional[WorkflowSubType] = None, use_no_lora: bool = False) -> Optional[Dict[str, Any]]:
        """构建工作流"""
        # 加载工作流模板
        workflow_data = self.node_mapper.load_workflow_template(workflow_file)
        if not workflow_data:
            self.logger.error(f"无法加载工作流模板: {workflow_file}")
            return None
        
        # 🔥 新增：特殊工作流处理
        if workflow_subtype in [WorkflowSubType.AIGEN_IMAGE_TO_TEXT, WorkflowSubType.AIGEN_UPSCALE]:
            # 图片反推和高清放大工作流只应用seed参数
            self._apply_seed_only(workflow_data, params)
            self.logger.info(f"🔍 {workflow_subtype.value} 工作流只应用seed参数，跳过提示词应用")
        else:
            # 常规工作流应用所有用户参数
            workflow_data = self.node_mapper.apply_parameters_to_workflow(workflow_data, params)
            self.logger.info(f"成功应用用户提示词: {params.prompt[:100]}...")
        
        # 应用图片数据
        if session_images and len(session_images) > 0:
            self.logger.debug(f"🔍 [DEBUG] 开始应用图片数据 - 图片数量: {len(session_images)}")
            for i, img in enumerate(session_images):
                self.logger.debug(f"🔍 [DEBUG] 图片 {i+1}: 类型={img.flux_image_type.value}, 数据大小={len(img.data)} bytes, 哈希={img.get_hash()[:8]}")
            workflow_data = self._apply_images_by_type(workflow_data, session_images, workflow_subtype)
        else:
            self.logger.debug(f"⚠️ [DEBUG] 没有图片数据需要应用 - session_images={session_images}, 长度={len(session_images) if session_images else 0}")

        # 🔥 新增：特殊工作流跳过LoRA应用
        if workflow_subtype in [WorkflowSubType.AIGEN_IMAGE_TO_TEXT, WorkflowSubType.AIGEN_UPSCALE]:
            self.logger.info(f"🔍 {workflow_subtype.value} 工作流跳过LoRA应用")
            lora_status_message = f"{workflow_subtype.value} 工作流不需要LoRA"
            self.last_lora_status = ""
        elif use_no_lora:
            # 使用 --no-lora 参数，跳过LoRA应用但保留节点结构
            self.logger.info("🚫 --no-lora 参数生效，跳过LoRA应用")
            lora_status_message = "🚫 已禁用所有LoRA模型"
            self.last_lora_status = lora_status_message
        else:
            # 应用LoRA（带统一错误处理）
            workflow_data, lora_status_message = await self.lora_integration.apply_loras_to_workflow(workflow_data, optimized_loras)

            self.logger.debug(f"🔧 [DEBUG] LoRA应用结果: {lora_status_message}")

            # 保存LoRA状态消息，供后续使用
            # 如果是临时屏蔽状态，不保存调试消息
            if "🚨" in lora_status_message and "暂时屏蔽" in lora_status_message:
                self.last_lora_status = ""  # 屏蔽状态下不显示LoRA状态
            else:
                self.last_lora_status = lora_status_message
        
        # 设置当前工作流数据
        self.current_workflow_data = workflow_data

        # 检查图片节点的base64数据
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict) and node_data.get('class_type') == 'easy loadImageBase64':
                base64_data = node_data.get('inputs', {}).get('base64_data', '')
                title = node_data.get('_meta', {}).get('title', '')
                if base64_data:
                    self.logger.debug(f"✅ 节点 {node_id} ({title}) 有base64数据，长度: {len(base64_data)}")
                else:
                    self.logger.debug(f"⚠️ 节点 {node_id} ({title}) 缺少base64数据")

        return workflow_data
    
    def _determine_workflow_subtype(self, workflow_file: str) -> WorkflowSubType:
        """根据工作流文件名确定工作流子类型"""
        workflow_file_lower = workflow_file.lower()

        # 🔥 新增：检查图片反推工作流
        if "image_to_text" in workflow_file_lower or "img2txt" in workflow_file_lower:
            return WorkflowSubType.AIGEN_IMAGE_TO_TEXT
        # 🔥 新增：检查高清放大工作流
        elif "upscale" in workflow_file_lower:
            return WorkflowSubType.AIGEN_UPSCALE
        # 🔥 修复：优先检查混合工作流（包含controlnet和redux）
        elif "controlnet" in workflow_file_lower and "redux" in workflow_file_lower:
            return WorkflowSubType.AIGEN_CONTROL_REFERENCE
        elif "controlnet" in workflow_file_lower:
            return WorkflowSubType.AIGEN_CONTROL_ONLY
        elif "redux" in workflow_file_lower:
            return WorkflowSubType.AIGEN_REFERENCE_ONLY
        else:
            return WorkflowSubType.AIGEN_TEXT_ONLY

    async def _execute_comfyui_workflow(self, workflow_data: Dict[str, Any], workflow_subtype: Optional[WorkflowSubType] = None) -> Optional[Dict[str, Any]]:
        """
        执行ComfyUI工作流

        Args:
            workflow_data: 工作流数据
            workflow_subtype: 工作流子类型，用于确定返回数据类型

        Returns:
            Optional[Dict[str, Any]]: 执行结果，包含image_data或text_data
        """
        try:
            self.logger.info(f"开始执行ComfyUI工作流，API URL: {self.api_url}")
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                
                # 提交工作流
                self.logger.info("准备提交工作流到ComfyUI")
                prompt_id = await self._submit_workflow(session, workflow_data)
                if not prompt_id:
                    self.logger.error("提交工作流失败")
                    return None
                
                self.logger.info(f"工作流已提交，prompt_id: {prompt_id}")
                
                # 等待完成并获取结果
                self.logger.info("开始等待工作流完成")

                # 🔥 新增：根据工作流类型获取不同类型的结果
                if workflow_subtype == WorkflowSubType.AIGEN_IMAGE_TO_TEXT:
                    # 图片反推工作流 - 获取文本结果
                    text_result = await self._wait_for_text_completion_via_api(prompt_id)
                    if text_result:
                        self.logger.info(f"成功获取文本结果，长度: {len(text_result)} 字符")
                        return {'text_data': text_result}
                    else:
                        self.logger.error("获取文本结果失败")
                        return None
                else:
                    # 普通图像生成工作流 - 获取图片结果
                    image_data = await self._wait_for_completion_via_api(prompt_id)
                    if image_data:
                        # 检查是否是多张图片（列表）还是单张图片（bytes）
                        if isinstance(image_data, list):
                            self.logger.info(f"成功获取 {len(image_data)} 张图片")
                            return {'image_data': image_data}
                        else:
                            self.logger.info(f"成功获取图片数据，大小: {len(image_data)} bytes")
                            return {'image_data': image_data}
                    else:
                        self.logger.error("获取图片数据失败")
                        return None
                
        except Exception as e:
            self.logger.error(f"执行ComfyUI工作流失败: {e}")
            return None
    
    async def _submit_workflow(self, session: aiohttp.ClientSession, workflow: Dict[str, Any]) -> Optional[str]:
        """提交工作流到ComfyUI"""
        try:
            # 📋 记录关键工作流参数用于调试
            self.logger.info("=" * 60)
            self.logger.info("🚀 FluxWorkflowManager准备提交工作流")
            self.logger.info(f"🌐 API URL: {self.api_url}")
            self.logger.info(f"📊 工作流节点数: {len(workflow)}")
            self.logger.info("=" * 60)
            
            # 记录种子信息
            for node_id, node_data in workflow.items():
                if isinstance(node_data, dict):
                    class_type = node_data.get('class_type', '')
                    if class_type == "easy globalSeed":
                        inputs = node_data.get('inputs', {})
                        value = inputs.get('value', '未设置')
                        last_seed = inputs.get('last_seed', '未设置')
                        self.logger.info(f"  🎲 EasyGlobalSeed节点{node_id}: value={value}, last_seed={last_seed}")
                    elif "Sampler" in class_type:
                        inputs = node_data.get('inputs', {})
                        seed = inputs.get('seed', '未设置')
                        noise_seed = inputs.get('noise_seed', '未设置')
                        steps = inputs.get('steps', '未设置')
                        self.logger.info(f"  🎯 采样器节点{node_id}({class_type}): seed={seed}, noise_seed={noise_seed}, steps={steps}")
            
            prompt_data = {"prompt": workflow}

            # 🔥 添加详细的提交日志
            self.logger.info(f"📤 准备POST请求到: {self.api_url}/prompt")
            self.logger.info(f"📦 Payload大小: {len(str(prompt_data))} 字符")

            async with session.post(f"{self.api_url}/prompt", json=prompt_data) as response:
                self.logger.info(f"📨 收到响应: HTTP {response.status}")

                if response.status == 200:
                    result = await response.json()
                    self.logger.info(f"📋 响应内容: {result}")

                    prompt_id = result.get("prompt_id")
                    if prompt_id:
                        self.logger.info(f"✅ 工作流提交成功: {prompt_id}")
                        return prompt_id
                    else:
                        self.logger.error(f"❌ 工作流提交失败：响应中没有prompt_id")
                        self.logger.error(f"完整响应: {result}")
                        return None
                else:
                    error_text = await response.text()
                    self.logger.error(f"❌ 工作流提交失败: HTTP {response.status}")
                    self.logger.error(f"错误详情: {error_text}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"提交工作流异常: {e}")
            return None
    
    async def _wait_for_completion_via_api(self, prompt_id: str) -> Optional[bytes]:
        """通过API等待工作流完成"""
        try:
            # 轮询检查状态
            max_polls = 180  # 最大轮询次数
            poll_interval = 1  # 轮询间隔(秒)
            
            async with aiohttp.ClientSession() as session:
                for _ in range(max_polls):
                    # 检查队列状态
                    queue_info = await self._check_queue_status(session, prompt_id)
                    if queue_info is None:
                        self.logger.error("获取队列状态失败")
                        return None
                    
                    # 检查是否完成
                    if queue_info.get('completed', False):
                        self.logger.info("工作流执行完成")
                        
                        # 获取输出图片
                        image_data = await self._get_output_image(session, prompt_id)
                        return image_data
                    
                    # 检查是否出错
                    if queue_info.get('failed', False):
                        error_msg = queue_info.get('error', '未知错误')
                        self.logger.error(f"工作流执行失败: {error_msg}")
                        return None
                    
                    # 等待下次轮询
                    await asyncio.sleep(poll_interval)
                
                self.logger.error("工作流执行超时")
                return None
                
        except Exception as e:
            self.logger.error(f"等待工作流完成失败: {e}")
            return None
    
    async def _check_queue_status(self, session: aiohttp.ClientSession, prompt_id: str) -> Optional[Dict[str, Any]]:
        """检查队列状态"""
        try:
            # 🔥 修复：先检查队列状态，再检查历史记录
            # 1. 检查当前队列
            async with session.get(f"{self.api_url}/queue") as response:
                if response.status == 200:
                    queue_data = await response.json()

                    # 检查是否在执行队列中
                    queue_running = queue_data.get('queue_running', [])
                    queue_pending = queue_data.get('queue_pending', [])

                    # 检查是否在运行中
                    for item in queue_running:
                        if item[1] == prompt_id:
                            # self.logger.info(f"工作流正在执行中: {prompt_id}")  # 隐藏频繁的执行状态信息
                            return {'completed': False, 'failed': False, 'status': 'running'}

                    # 检查是否在等待中
                    for item in queue_pending:
                        if item[1] == prompt_id:
                            self.logger.info(f"工作流在队列中等待: {prompt_id}")
                            return {'completed': False, 'failed': False, 'status': 'pending'}

            # 2. 检查历史记录（已完成或失败）
            async with session.get(f"{self.api_url}/history/{prompt_id}") as response:
                if response.status == 200:
                    history_data = await response.json()

                    if prompt_id in history_data:
                        prompt_data = history_data[prompt_id]

                        # 检查是否有错误
                        if 'error' in prompt_data:
                            error_info = prompt_data['error']
                            error_msg = error_info.get('message', '未知错误') if isinstance(error_info, dict) else str(error_info)
                            self.logger.error(f"工作流执行失败: {error_msg}")
                            return {
                                'completed': False,
                                'failed': True,
                                'error': error_msg,
                                'status': 'failed'
                            }

                        # 检查是否有输出（成功完成）
                        outputs = prompt_data.get('outputs', {})
                        if outputs:
                            self.logger.info(f"工作流执行完成: {prompt_id}")
                            return {
                                'completed': True,
                                'failed': False,
                                'outputs': outputs,
                                'status': 'completed'
                            }
                        else:
                            # 在历史中但没有输出，可能还在处理
                            return {'completed': False, 'failed': False, 'status': 'processing'}
                    else:
                        # 不在历史中，可能还在队列中或刚提交
                        return {'completed': False, 'failed': False, 'status': 'unknown'}
                else:
                    self.logger.warning(f"检查历史记录失败: {response.status}")
                    return None

        except Exception as e:
            self.logger.error(f"检查队列状态异常: {e}")
            return None
    
    async def _get_output_image(self, session: aiohttp.ClientSession, prompt_id: str) -> Optional[bytes]:
        """获取输出图片"""
        try:
            # 获取历史记录找到输出
            async with session.get(f"{self.api_url}/history/{prompt_id}") as response:
                if response.status != 200:
                    return None
                
                history_data = await response.json()
                prompt_data = history_data.get(prompt_id, {})
                outputs = prompt_data.get('outputs', {})
                
                self.logger.info(f"工作流输出节点: {list(outputs.keys())}")
                
                # 🔥 智能识别最终输出节点
                final_node_id = self._identify_final_output_node(outputs)
                if final_node_id:
                    output_data = outputs[final_node_id]
                    if 'images' in output_data:
                        images = output_data['images']
                        if images:
                            # 获取所有图片
                            image_data_list = []
                            for i, image_info in enumerate(images):
                                filename = image_info['filename']
                                subfolder = image_info.get('subfolder', '')
                                image_type = image_info.get('type', '')

                                self.logger.info(f"找到最终结果图片 {i+1}/{len(images)}: 节点{final_node_id}, 文件: {filename}")

                                # 下载图片
                                image_data = await self._download_image(session, filename, subfolder, image_type)
                                if image_data:
                                    image_data_list.append(image_data)

                            # 如果只有一张图片，返回单张；多张图片返回列表
                            if len(image_data_list) == 1:
                                return image_data_list[0]
                            elif len(image_data_list) > 1:
                                return image_data_list
                            else:
                                self.logger.error("没有成功下载任何图片")
                                return None
                
                # 如果没有找到最终结果节点，查找其他预览节点
                for node_id, output_data in outputs.items():
                    if 'images' in output_data:
                        images = output_data['images']
                        if images:
                            # 获取所有图片
                            image_data_list = []
                            for i, image_info in enumerate(images):
                                filename = image_info['filename']
                                subfolder = image_info.get('subfolder', '')
                                image_type = image_info.get('type', '')

                                self.logger.info(f"找到预览图片 {i+1}/{len(images)}: 节点{node_id}, 文件: {filename}")

                                # 下载图片
                                image_data = await self._download_image(session, filename, subfolder, image_type)
                                if image_data:
                                    image_data_list.append(image_data)

                            # 如果只有一张图片，返回单张；多张图片返回列表
                            if len(image_data_list) == 1:
                                return image_data_list[0]
                            elif len(image_data_list) > 1:
                                return image_data_list
                            else:
                                self.logger.error("没有成功下载任何图片")
                                return None
                
                self.logger.warning("没有找到输出图片")
                return None
                
        except Exception as e:
            self.logger.error(f"获取输出图片失败: {e}")
            return None
    
    def _identify_final_output_node(self, outputs: Dict[str, Any]) -> Optional[str]:
        """
        智能识别最终输出节点

        Args:
            outputs: 工作流输出节点数据

        Returns:
            Optional[str]: 最终输出节点ID，如果没有找到返回None
        """
        try:
            # 查找所有有图片输出的节点
            image_output_nodes = []

            for node_id, output_data in outputs.items():
                if 'images' in output_data and output_data['images']:
                    image_output_nodes.append(node_id)

            if not image_output_nodes:
                self.logger.warning("没有找到任何图片输出节点")
                return None

            # 🔥 关键修复：排除输入节点，避免把输入当成输出
            if self.current_workflow_data:
                filtered_output_nodes = []
                for node_id in image_output_nodes:
                    if node_id in self.current_workflow_data:
                        node_data = self.current_workflow_data[node_id]
                        title = node_data.get('_meta', {}).get('title', '')
                        class_type = node_data.get('class_type', '')

                        # 🔥 修复：只排除明确标记为输入的节点，不排除所有easy loadImageBase64
                        if 'input' in title.lower():
                            self.logger.info(f"🚫 排除输入节点: {node_id} (标题: {title}, 类型: {class_type})")
                            continue

                        filtered_output_nodes.append(node_id)

                if filtered_output_nodes:
                    image_output_nodes = filtered_output_nodes
                    self.logger.info(f"🔍 过滤后的输出节点: {image_output_nodes}")
                else:
                    self.logger.warning("⚠️ 过滤后没有有效的输出节点")
                    # 🔥 如果过滤后没有节点，使用原始列表作为备选
                    self.logger.info("🔄 使用原始输出节点列表作为备选")
                    # 保持原始的image_output_nodes，不再过滤

            # 🔥 新增：第一优先级：查找ColorMatch节点（混合工作流的真正最终输出）
            if self.current_workflow_data:
                for node_id in image_output_nodes:
                    if node_id in self.current_workflow_data:
                        node_data = self.current_workflow_data[node_id]
                        class_type = node_data.get('class_type', '')
                        title = node_data.get('_meta', {}).get('title', '')

                        if class_type == 'ColorMatch':
                            self.logger.info(f"🎨 找到ColorMatch最终输出节点: {node_id} (标题: {title})")
                            return node_id

            # 🎯 第二优先级：查找标题为"final_image_output"的节点（VAEDecode节点）
            if self.current_workflow_data:
                for node_id in image_output_nodes:
                    if node_id in self.current_workflow_data:
                        node_data = self.current_workflow_data[node_id]
                        title = node_data.get('_meta', {}).get('title', '')

                        if title == 'final_image_output':
                            self.logger.info(f"✅ 找到final_image_output节点: {node_id}")
                            return node_id

            # 🔥 第三优先级：查找其他VAEDecode节点
            if self.current_workflow_data:
                vae_decode_nodes = []
                for node_id in image_output_nodes:
                    if node_id in self.current_workflow_data:
                        node_data = self.current_workflow_data[node_id]
                        class_type = node_data.get('class_type', '')

                        if class_type == 'VAEDecode':
                            vae_decode_nodes.append(node_id)

                if vae_decode_nodes:
                    # 选择ID最大的VAEDecode节点
                    try:
                        numeric_nodes = [(int(node_id), node_id) for node_id in vae_decode_nodes if node_id.isdigit()]
                        if numeric_nodes:
                            numeric_nodes.sort(key=lambda x: x[0], reverse=True)
                            final_node_id = numeric_nodes[0][1]
                            self.logger.info(f"✅ 选择VAEDecode节点: {final_node_id}")
                            return final_node_id
                    except (ValueError, TypeError):
                        pass

                    final_node_id = vae_decode_nodes[0]
                    self.logger.info(f"✅ 选择第一个VAEDecode节点: {final_node_id}")
                    return final_node_id

            # 🔥 第四优先级：查找标题为"final_image"的PreviewImage节点
            if self.current_workflow_data:
                for node_id in image_output_nodes:
                    if node_id in self.current_workflow_data:
                        node_data = self.current_workflow_data[node_id]
                        title = node_data.get('_meta', {}).get('title', '')
                        
                        if title == 'final_image':
                            self.logger.info(f"⚠️  选择final_image PreviewImage节点: {node_id}")
                            return node_id
            
            # 🔥 第五优先级：选择SaveImage或其他最终输出节点
            if len(image_output_nodes) == 1:
                node_id = image_output_nodes[0]
                # 检查节点类型
                if self.current_workflow_data and node_id in self.current_workflow_data:
                    class_type = self.current_workflow_data[node_id].get('class_type', '')
                    title = self.current_workflow_data[node_id].get('_meta', {}).get('title', '')
                    self.logger.info(f"✅ 选择唯一输出节点: {node_id} ({class_type}, 标题: {title})")
                else:
                    self.logger.info(f"✅ 选择唯一输出节点: {node_id}")
                return node_id

            # 多个输出节点时，选择最合适的
            try:
                numeric_nodes = [(int(node_id), node_id) for node_id in image_output_nodes if node_id.isdigit()]
                if numeric_nodes:
                    numeric_nodes.sort(key=lambda x: x[0], reverse=True)
                    final_node_id = numeric_nodes[0][1]

                    # 检查选中节点的类型
                    if self.current_workflow_data and final_node_id in self.current_workflow_data:
                        class_type = self.current_workflow_data[final_node_id].get('class_type', '')
                        title = self.current_workflow_data[final_node_id].get('_meta', {}).get('title', '')
                        self.logger.info(f"✅ 选择最终输出节点: {final_node_id} ({class_type}, 标题: {title})")
                    else:
                        self.logger.info(f"✅ 选择最终输出节点: {final_node_id}")
                    return final_node_id
            except (ValueError, TypeError):
                pass
            
            # 如果节点ID不是数字，按字符串排序选择最后一个
            image_output_nodes.sort(reverse=True)
            final_node_id = image_output_nodes[0]

            # 检查选中节点的类型
            if self.current_workflow_data and final_node_id in self.current_workflow_data:
                class_type = self.current_workflow_data[final_node_id].get('class_type', '')
                title = self.current_workflow_data[final_node_id].get('_meta', {}).get('title', '')
                self.logger.info(f"✅ 选择输出节点: {final_node_id} ({class_type}, 标题: {title})")
            else:
                self.logger.info(f"✅ 选择输出节点: {final_node_id}")
            return final_node_id
            
        except Exception as e:
            self.logger.error(f"识别最终输出节点失败: {e}")
            return None
    
    async def _download_image(self, session: aiohttp.ClientSession, filename: str, subfolder: str = "", image_type: str = "") -> Optional[bytes]:
        """下载图片"""
        try:
            # 构建下载URL
            url_params = [f"filename={filename}"]
            if subfolder:
                url_params.append(f"subfolder={subfolder}")
            if image_type:
                url_params.append(f"type={image_type}")

            url = f"{self.api_url}/view?" + "&".join(url_params)

            async with session.get(url) as response:
                if response.status == 200:
                    image_data = await response.read()
                    self.logger.info(f"下载图片成功: {filename}, 大小: {len(image_data)} bytes")

                    # 🔥 新增：如果是SaveImage生成的文件，下载后自动清理
                    await self._cleanup_saveimage_file_if_needed(session, filename, subfolder, image_type)

                    return image_data
                else:
                    self.logger.error(f"下载图片失败: {response.status}")
                    return None

        except Exception as e:
            self.logger.error(f"下载图片异常: {e}")
            return None

    async def _cleanup_saveimage_file_if_needed(self, session: aiohttp.ClientSession, filename: str, subfolder: str = "", image_type: str = ""):
        """清理SaveImage生成的文件"""
        try:
            # 🔥 修复：简化清理逻辑，直接清理所有langbot临时文件
            if filename.startswith("langbot_temp"):
                self.logger.info(f"🗑️ 准备清理临时文件: {filename}")

                # 🔥 修复：直接删除文件系统中的文件（ComfyUI没有删除API）
                await self._delete_file_directly(filename, subfolder, image_type)
            else:
                self.logger.debug(f"跳过清理非临时文件: {filename}")

        except Exception as e:
            self.logger.warning(f"清理SaveImage文件失败: {e}")

    async def _delete_file_directly(self, filename: str, subfolder: str = "", image_type: str = ""):
        """直接删除文件系统中的文件"""
        try:
            import os
            import asyncio

            # 构建文件路径
            base_dir = "/home/<USER>/Workspace/ComfyUI"
            if image_type == "input":
                file_path = os.path.join(base_dir, "input", subfolder, filename)
            else:  # output 或其他
                file_path = os.path.join(base_dir, "output", subfolder, filename)

            self.logger.info(f"🗑️ 尝试删除文件: {file_path}")

            # 检查文件是否存在
            if os.path.exists(file_path):
                # 异步删除文件
                def delete_file():
                    os.remove(file_path)
                    return True

                # 在线程池中执行删除操作
                loop = asyncio.get_event_loop()
                success = await loop.run_in_executor(None, delete_file)

                if success:
                    self.logger.info(f"✅ 已清理临时文件: {filename}")
                else:
                    self.logger.warning(f"⚠️ 文件删除失败: {filename}")
            else:
                self.logger.warning(f"⚠️ 文件不存在，无法删除: {file_path}")

        except Exception as e:
            self.logger.warning(f"删除文件异常: {e}")
            import traceback
            self.logger.debug(f"删除异常详情: {traceback.format_exc()}")
    
    async def create_image_message(self, image_data: bytes) -> Optional[llm_entities.Message]:
        """创建图片消息"""
        try:
            # 将图片数据转换为base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 构建消息内容
            content_parts = []
            
            # 添加LoRA状态信息
            if hasattr(self, 'last_lora_status') and self.last_lora_status:
                content_parts.append(f"**LoRA状态**: {self.last_lora_status}")
            
            # 添加图片
            content_parts.append(f"![Generated Image](data:image/png;base64,{image_base64})")
            
            # 组合最终内容
            final_content = "\n\n".join(content_parts)
            
            # 创建消息
            message = llm_entities.Message(
                role='assistant',
                content=final_content
            )
            
            self.logger.debug(f"🔧 [DEBUG] 创建图片消息，包含LoRA状态: {self.last_lora_status if hasattr(self, 'last_lora_status') else '无'}")
            
            return message
            
        except Exception as e:
            self.logger.error(f"创建图片消息失败: {e}")
            return None
    
    def get_flux_statistics(self) -> Dict[str, Any]:
        """获取Flux工作流统计信息"""
        try:
            seed_stats = self.seed_manager.get_seed_statistics()
            lora_stats = self.lora_integration.get_lora_statistics()
            
            # 组合统计信息
            stats = {
                'workflow_type': 'Flux',
                'seed_statistics': seed_stats,
                'lora_statistics': lora_stats,
                'supported_parameters': self.node_mapper.get_supported_parameters(),
                'last_execution': None
            }
            
            # 添加最后执行信息
            if self.last_execution_result:
                stats['last_execution'] = self.last_execution_result.get_generation_info()
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取Flux统计失败: {e}")
            return {}
    
    def export_workflow_config(self, file_path: str) -> bool:
        """导出工作流配置"""
        try:
            config = {
                'workflow_type': 'flux',
                'api_url': self.api_url,
                'timeout': self.timeout,
                'workflow_path': self.workflow_path,
                'export_time': time.time(),
                'node_configurations': {
                    node_id: self.node_mapper.get_node_info(node_id)
                    for node_id in ["6", "55", "50", "61", "60"]
                },
                'seed_settings': {
                    'max_history': self.seed_manager.max_history,
                    'storage_file': self.seed_manager.storage_file
                }
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"导出Flux工作流配置: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出工作流配置失败: {e}")
            return False
    
    def validate_configuration(self) -> Dict[str, Any]:
        """验证Flux配置"""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'checks': {}
        }
        
        try:
            # 检查API连接
            validation_result['checks']['api_url'] = {
                'value': self.api_url,
                'status': 'unknown'  # 需要实际请求才能确定
            }
            
            # 检查工作流文件
            workflow_files = [
                os.path.join(self.workflow_path, self.node_mapper.default_workflow_file),
                os.path.join(self.workflow_path, self.node_mapper.fallback_workflow_file)
            ]
            
            workflow_exists = any(os.path.exists(f) for f in workflow_files)
            validation_result['checks']['workflow_files'] = {
                'files': workflow_files,
                'exists': workflow_exists
            }
            
            if not workflow_exists:
                validation_result['warnings'].append("没有找到工作流文件，将使用内置模板")
            
            # 检查种子管理器
            validation_result['checks']['seed_manager'] = {
                'storage_file': self.seed_manager.storage_file,
                'history_count': len(self.seed_manager.seed_history)
            }
            
            # 检查LoRA集成
            validation_result['checks']['lora_integration'] = {
                'manager_available': self.lora_integration.lora_manager is not None
            }
            
        except Exception as e:
            validation_result['valid'] = False
            validation_result['errors'].append(f"配置验证异常: {e}")
        
        return validation_result

    def _apply_images_by_type(self, workflow_data: Dict[str, Any], session_images: List, workflow_subtype: Optional[WorkflowSubType] = None) -> Dict[str, Any]:
        """
        根据图片类型将图片分配到不同节点
        Args:
            workflow_data: 工作流数据
            session_images: 会话图片列表（SessionImage对象）
            workflow_subtype: 工作流子类型，用于自动分类图片
        Returns:
            修改后的工作流数据
        """
        try:
            import base64

            # 🔍 [DEBUG] 详细分析会话图片
            self.logger.debug(f"🔍 [DEBUG] 开始分析 {len(session_images)} 张会话图片")
            for i, img in enumerate(session_images):
                self.logger.debug(f"🔍 [DEBUG] 图片 {i+1}: 类型={img.flux_image_type.value}, 数据大小={len(img.data)} bytes, 哈希={img.get_hash()[:8]}")

            # 🔥 新增：图片反推工作流特殊处理
            if workflow_subtype == WorkflowSubType.AIGEN_IMAGE_TO_TEXT:
                self.logger.info(f"🖼️ 检测到图片反推工作流，所有图片将直接应用到图片反推节点")

                # 图片反推工作流中，所有图片都直接应用到图片反推节点
                # 不需要区分控制图、参考图等类型
                input_images = session_images
                control_images = []
                reference_images = []
                mixed_images = []
                untyped_images = []

                # 应用图片到图片反推节点（节点61）
                if input_images and len(input_images) > 0:
                    # 获取第一张图片
                    img = input_images[0]
                    # 转换为base64 - 确保长度是4的倍数
                    try:
                        img_base64 = base64.b64encode(img.data).decode('utf-8')

                        # 应用到图片反推节点
                        if "61" in workflow_data:  # 图片反推工作流中的图片输入节点
                            workflow_data["61"]["inputs"]["base64_data"] = img_base64
                            self.logger.info(f"✅ 成功应用图片到图片反推节点 (节点61)")
                        else:
                            self.logger.warning(f"⚠️ 未找到图片反推节点 (节点61)，无法应用图片")
                    except Exception as e:
                        self.logger.error(f"❌ 图片反推工作流应用图片失败: {e}")
                        # 尝试使用替代方法
                        try:
                            # 查找任何easy loadImageBase64类型的节点
                            for node_id, node_data in workflow_data.items():
                                if isinstance(node_data, dict) and node_data.get('class_type') == 'easy loadImageBase64':
                                    img_base64 = base64.b64encode(img.data).decode('utf-8')
                                    workflow_data[node_id]["inputs"]["base64_data"] = img_base64
                                    self.logger.info(f"✅ 成功应用图片到替代节点 (节点{node_id})")
                                    break
                        except Exception as e2:
                            self.logger.error(f"❌ 图片反推工作流应用图片替代方法也失败: {e2}")

                self.logger.info(f"📊 图片反推工作流: 已应用 {len(input_images)} 张图片")

                # 直接返回修改后的工作流数据
                return workflow_data

            # 🔥 新增：高清放大工作流特殊处理
            elif workflow_subtype == WorkflowSubType.AIGEN_UPSCALE:
                self.logger.info(f"🔍 检测到高清放大工作流，按顺序应用图片到指定节点")

                # 高清放大工作流中，按顺序应用图片到指定节点
                # 不需要区分控制图、参考图等类型
                input_images = session_images
                control_images = []
                reference_images = []
                mixed_images = []
                untyped_images = []

                # 应用图片到高清放大节点
                if input_images and len(input_images) > 0:
                    try:
                        # 第一张图片：目标图（要放大的图片）-> load_image_input_01
                        img1 = input_images[0]
                        img1_base64 = base64.b64encode(img1.data).decode('utf-8')

                        # 第二张图片：控制图（控制结构的图片）-> load_image_input_02
                        # 如果只有一张图片，两个节点使用同一张图片
                        if len(input_images) >= 2:
                            img2 = input_images[1]
                            img2_base64 = base64.b64encode(img2.data).decode('utf-8')
                            self.logger.info(f"🔍 使用2张图片：目标图 + 控制图")
                        else:
                            img2_base64 = img1_base64  # 使用同一张图片
                            self.logger.info(f"🔍 只有1张图片，两个节点使用同一张图片")

                        # 查找并应用到指定节点
                        target_node_found = False
                        control_node_found = False

                        # 🔥 打印所有节点的标题，帮助调试
                        self.logger.info("🔍 工作流节点标题列表:")
                        for node_id, node_data in workflow_data.items():
                            if isinstance(node_data, dict) and '_meta' in node_data and 'title' in node_data['_meta']:
                                self.logger.info(f"  - 节点 {node_id}: {node_data['_meta']['title']}")

                        # 查找并应用到指定节点
                        for node_id, node_data in workflow_data.items():
                            if isinstance(node_data, dict) and '_meta' in node_data:
                                node_title = node_data.get('_meta', {}).get('title', '')

                                # 目标图节点
                                if node_title == 'load_image_input_01':
                                    workflow_data[node_id]["inputs"]["base64_data"] = img1_base64
                                    self.logger.info(f"✅ 成功应用目标图到节点 {node_id} (load_image_input_01)")
                                    target_node_found = True

                                # 控制图节点
                                elif node_title == 'load_image_input_02':
                                    workflow_data[node_id]["inputs"]["base64_data"] = img2_base64
                                    self.logger.info(f"✅ 成功应用控制图到节点 {node_id} (load_image_input_02)")
                                    control_node_found = True

                        # 检查是否找到了所需的节点
                        if not target_node_found or not control_node_found:
                            # 尝试使用替代方法 - 查找任何easy loadImageBase64类型的节点
                            self.logger.warning(f"⚠️ 未找到部分或全部指定节点，尝试使用替代方法")

                            # 查找所有easy loadImageBase64类型的节点
                            image_nodes = []
                            for node_id, node_data in workflow_data.items():
                                if isinstance(node_data, dict) and node_data.get('class_type') == 'easy loadImageBase64':
                                    image_nodes.append(node_id)

                            if len(image_nodes) >= 2:
                                # 有至少两个图片节点，分别应用两张图片
                                workflow_data[image_nodes[0]]["inputs"]["base64_data"] = img1_base64
                                workflow_data[image_nodes[1]]["inputs"]["base64_data"] = img2_base64
                                self.logger.info(f"✅ 成功应用图片到替代节点 {image_nodes[0]} 和 {image_nodes[1]}")
                                target_node_found = True
                                control_node_found = True
                            elif len(image_nodes) == 1:
                                # 只有一个图片节点，应用第一张图片
                                workflow_data[image_nodes[0]]["inputs"]["base64_data"] = img1_base64
                                self.logger.info(f"✅ 成功应用图片到替代节点 {image_nodes[0]}")
                                target_node_found = True

                            # 最终检查
                            if not target_node_found:
                                self.logger.warning(f"⚠️ 未找到目标图节点 (load_image_input_01)，且无法使用替代方法")
                            if not control_node_found:
                                self.logger.warning(f"⚠️ 未找到控制图节点 (load_image_input_02)，且无法使用替代方法")

                        applied_count = len(input_images) if len(input_images) <= 2 else 2
                        self.logger.info(f"📊 高清放大工作流: 已应用 {applied_count} 张图片到指定节点")

                    except Exception as e:
                        self.logger.error(f"❌ 高清放大工作流处理图片时发生异常: {e}")
                else:
                    self.logger.warning(f"⚠️ 高清放大工作流没有图片输入")

                # 直接返回修改后的工作流数据
                return workflow_data
            else:
                # 常规工作流 - 获取不同类型的图片
                control_images = [img for img in session_images if img.is_control_image()]
                reference_images = [img for img in session_images if img.is_reference_image()]
                mixed_images = [img for img in session_images if img.is_mixed_image()]
                untyped_images = [img for img in session_images if img.flux_image_type.value == 'unknown']

                self.logger.info(f"📊 图片类型分布: 控制图{len(control_images)}张, 参考图{len(reference_images)}张, 混合图{len(mixed_images)}张, 未分类{len(untyped_images)}张")

            # 🔍 [DEBUG] 详细显示每种类型的图片
            if control_images:
                for i, img in enumerate(control_images):
                    self.logger.debug(f"🔍 [DEBUG] 控制图 {i+1}: 哈希={img.get_hash()[:8]}, 大小={len(img.data)} bytes")
            if reference_images:
                for i, img in enumerate(reference_images):
                    self.logger.debug(f"🔍 [DEBUG] 参考图 {i+1}: 哈希={img.get_hash()[:8]}, 大小={len(img.data)} bytes")
            if untyped_images:
                for i, img in enumerate(untyped_images):
                    self.logger.debug(f"🔍 [DEBUG] 未分类图 {i+1}: 哈希={img.get_hash()[:8]}, 大小={len(img.data)} bytes")
            # 查找特定功能的图片输入节点
            controlnet_node_id = None
            redux_node_id = None

            self.logger.debug(f"🔍 [DEBUG] 开始查找图片输入节点，工作流包含 {len(workflow_data)} 个节点")

            for node_id, node_data in workflow_data.items():
                if isinstance(node_data, dict) and 'class_type' in node_data and '_meta' in node_data:
                    title = node_data['_meta'].get('title', '')
                    class_type = node_data.get('class_type', '')

                    # 记录所有图片相关节点
                    if 'image' in title.lower() or class_type == 'easy loadImageBase64':
                        self.logger.debug(f"🔍 [DEBUG] 发现图片节点: {node_id}, 标题='{title}', 类型='{class_type}'")

                    if title == 'controlnet_image_input':
                        controlnet_node_id = node_id
                        self.logger.info(f"✅ 找到ControlNet图片输入节点: {node_id}")
                    elif title == 'redux_image_input':
                        redux_node_id = node_id
                        self.logger.info(f"✅ 找到Redux图片输入节点: {node_id}")

            self.logger.info(f"📋 节点查找结果: ControlNet节点={controlnet_node_id}, Redux节点={redux_node_id}")
            # 分配控制图到ControlNet节点
            if control_images and controlnet_node_id:
                control_image = control_images[0]  # 使用第一张控制图
                self.logger.debug(f"🔍 [DEBUG] 控制图数据大小: {len(control_image.data)} bytes, 哈希: {control_image.get_hash()[:8]}")
                image_base64 = base64.b64encode(control_image.data).decode('utf-8')
                self.logger.debug(f"🔍 [DEBUG] Base64数据长度: {len(image_base64)}, 前50字符: {image_base64[:50]}...")
                workflow_data[controlnet_node_id]["inputs"]["base64_data"] = image_base64
                self.logger.info(f"✅ 成功应用控制图到ControlNet节点 {controlnet_node_id}")

                # 验证数据是否正确设置
                if workflow_data[controlnet_node_id]["inputs"]["base64_data"]:
                    self.logger.info(f"✅ 验证: ControlNet节点 {controlnet_node_id} 的base64_data已设置，长度: {len(workflow_data[controlnet_node_id]['inputs']['base64_data'])}")
                else:
                    self.logger.error(f"❌ 验证失败: ControlNet节点 {controlnet_node_id} 的base64_data为空")
            elif control_images and not controlnet_node_id:
                self.logger.warning("⚠️ 有控制图但未找到ControlNet节点")
            elif not control_images and controlnet_node_id:
                self.logger.info(f"ℹ️ 找到ControlNet节点 {controlnet_node_id} 但没有控制图")
            # 分配参考图到Redux节点
            if reference_images and redux_node_id:
                reference_image = reference_images[0]  # 使用第一张参考图
                self.logger.debug(f"🔍 [DEBUG] 参考图数据大小: {len(reference_image.data)} bytes, 哈希: {reference_image.get_hash()[:8]}")
                image_base64 = base64.b64encode(reference_image.data).decode('utf-8')
                self.logger.debug(f"🔍 [DEBUG] Base64数据长度: {len(image_base64)}, 前50字符: {image_base64[:50]}...")
                workflow_data[redux_node_id]["inputs"]["base64_data"] = image_base64
                self.logger.info(f"✅ 成功应用参考图到Redux节点 {redux_node_id}")

                # 验证数据是否正确设置
                if workflow_data[redux_node_id]["inputs"]["base64_data"]:
                    self.logger.info(f"✅ 验证: Redux节点 {redux_node_id} 的base64_data已设置，长度: {len(workflow_data[redux_node_id]['inputs']['base64_data'])}")
                else:
                    self.logger.error(f"❌ 验证失败: Redux节点 {redux_node_id} 的base64_data为空")
            elif reference_images and not redux_node_id:
                self.logger.warning("⚠️ 有参考图但未找到Redux节点")
            elif not reference_images and redux_node_id:
                self.logger.info(f"ℹ️ 找到Redux节点 {redux_node_id} 但没有参考图")
            # 处理混合图片 - 如果有两个节点都可用，同时分配
            if mixed_images:
                mixed_image = mixed_images[0]
                image_base64 = base64.b64encode(mixed_image.data).decode('utf-8')
                # 如果没有专门的控制图，混合图可以作为控制图
                if not control_images and controlnet_node_id:
                    workflow_data[controlnet_node_id]["inputs"]["base64_data"] = image_base64
                    self.logger.info(f"成功应用混合图作为控制图到ControlNet节点 {controlnet_node_id}")
                # 如果没有专门的参考图，混合图可以作为参考图
                if not reference_images and redux_node_id:
                    workflow_data[redux_node_id]["inputs"]["base64_data"] = image_base64
                    self.logger.info(f"成功应用混合图作为参考图到Redux节点 {redux_node_id}")
            
            # 🔥 处理未明确类型的图片 - 根据工作流子类型自动分类
            self.logger.debug(f"🔍 [DEBUG] 检查未分类图片...")

            # 详细分析每张图片的状态
            for i, img in enumerate(session_images):
                self.logger.debug(f"🔍 [DEBUG] 图片 {i+1} 状态检查:")
                self.logger.debug(f"   - 类型: {img.flux_image_type.value}")
                self.logger.debug(f"   - is_control_image(): {img.is_control_image()}")
                self.logger.debug(f"   - is_reference_image(): {img.is_reference_image()}")
                self.logger.debug(f"   - is_mixed_image(): {img.is_mixed_image()}")

            untyped_images = [img for img in session_images if not (img.is_control_image() or img.is_reference_image() or img.is_mixed_image())]
            self.logger.debug(f"🔍 [DEBUG] 未分类图片数量: {len(untyped_images)}")
            self.logger.debug(f"🔍 [DEBUG] 工作流子类型: {workflow_subtype.value if workflow_subtype else 'None'}")

            if untyped_images:
                self.logger.info(f"🔄 发现 {len(untyped_images)} 张未分类图片，根据工作流类型自动分类")

                # 根据工作流子类型自动分类图片
                if workflow_subtype:
                    self._auto_classify_images_by_workflow(untyped_images, workflow_subtype)
                else:
                    self.logger.debug("⚠️ [DEBUG] 工作流子类型为空，无法自动分类")
            else:
                self.logger.debug("ℹ️ [DEBUG] 没有未分类图片，跳过自动分类")

            # 🔥 无论是否有自动分类，都重新获取分类后的图片
            control_images = [img for img in session_images if img.is_control_image()]
            reference_images = [img for img in session_images if img.is_reference_image()]
            mixed_images = [img for img in session_images if img.is_mixed_image()]
            self.logger.info(f"最终图片分布: 控制图{len(control_images)}张, 参考图{len(reference_images)}张, 混合图{len(mixed_images)}张")

            # 🔍 [DEBUG] 显示最终的详细信息
            for i, img in enumerate(session_images):
                self.logger.debug(f"🔍 [DEBUG] 最终图片 {i+1}: 类型={img.flux_image_type.value}, 哈希={img.get_hash()[:8]}")

            # 🔥 重新执行图片分配逻辑（这是关键修复）
            if untyped_images and workflow_subtype:
                self.logger.info("🔄 重新分配自动分类后的图片到工作流节点")

                # 分配新分类的控制图到ControlNet节点
                if control_images and controlnet_node_id:
                    control_image = control_images[0]
                    self.logger.debug(f"🔍 [DEBUG] 重新分配控制图数据大小: {len(control_image.data)} bytes")
                    image_base64 = base64.b64encode(control_image.data).decode('utf-8')
                    workflow_data[controlnet_node_id]["inputs"]["base64_data"] = image_base64
                    self.logger.info(f"✅ 重新分配控制图到ControlNet节点 {controlnet_node_id}")

                # 分配新分类的参考图到Redux节点
                if reference_images and redux_node_id:
                    reference_image = reference_images[0]
                    self.logger.debug(f"🔍 [DEBUG] 重新分配参考图数据大小: {len(reference_image.data)} bytes")
                    image_base64 = base64.b64encode(reference_image.data).decode('utf-8')
                    workflow_data[redux_node_id]["inputs"]["base64_data"] = image_base64
                    self.logger.info(f"✅ 重新分配参考图到Redux节点 {redux_node_id}")
            
            # 统计分配结果
            assigned_count = 0
            if control_images and controlnet_node_id:
                assigned_count += 1
            if reference_images and redux_node_id:
                assigned_count += 1
            if mixed_images and (controlnet_node_id or redux_node_id):
                assigned_count += 1
            if untyped_images and controlnet_node_id and not control_images and not mixed_images:
                assigned_count += 1
            if assigned_count == 0:
                self.logger.warning("未分配任何图片到工作流节点")
            else:
                self.logger.info(f"总共分配了 {assigned_count} 张图片到工作流节点")
            # 🔍 [DEBUG] 最终验证工作流中的图片数据
            self._debug_workflow_image_data(workflow_data)

            return workflow_data
        except Exception as e:
            self.logger.error(f"根据类型分配图片失败: {e}")
            return workflow_data

    def _auto_classify_images_by_workflow(self, untyped_images: List, workflow_subtype: WorkflowSubType):
        """
        根据工作流子类型自动分类图片

        Args:
            untyped_images: 未分类的图片列表
            workflow_subtype: 工作流子类型
        """
        from ...core.session.models import FluxImageType

        self.logger.debug(f"🔄 [DEBUG] 开始自动分类 {len(untyped_images)} 张图片")
        self.logger.debug(f"🔄 [DEBUG] 工作流子类型: {workflow_subtype.value if workflow_subtype else 'None'}")

        # 显示分类前的图片状态
        for i, img in enumerate(untyped_images):
            self.logger.debug(f"🔄 [DEBUG] 分类前图片 {i+1}: 类型={img.flux_image_type.value}, 哈希={img.get_hash()[:8]}")

        try:
            if workflow_subtype == WorkflowSubType.AIGEN_CONTROL_ONLY:
                # 控制图工作流：所有图片都是控制图
                for img in untyped_images:
                    img.flux_image_type = FluxImageType.CONTROL
                self.logger.info(f"控制图工作流：将 {len(untyped_images)} 张图片分类为控制图")

            elif workflow_subtype == WorkflowSubType.AIGEN_REFERENCE_ONLY:
                # 参考图工作流：所有图片都是参考图
                for img in untyped_images:
                    img.flux_image_type = FluxImageType.REFERENCE
                self.logger.info(f"参考图工作流：将 {len(untyped_images)} 张图片分类为参考图")

            elif workflow_subtype == WorkflowSubType.AIGEN_CONTROL_REFERENCE:
                # 混合工作流：按顺序分类（第一张控制图，第二张参考图）
                self.logger.debug(f"🎯 [DEBUG] 执行混合工作流分类逻辑")
                for i, img in enumerate(untyped_images):
                    old_type = img.flux_image_type.value
                    if i == 0:
                        img.flux_image_type = FluxImageType.CONTROL
                        self.logger.debug(f"🎯 [DEBUG] 第1张图片: {old_type} → CONTROL, 哈希={img.get_hash()[:8]}")
                    elif i == 1:
                        img.flux_image_type = FluxImageType.REFERENCE
                        self.logger.debug(f"🎯 [DEBUG] 第2张图片: {old_type} → REFERENCE, 哈希={img.get_hash()[:8]}")
                    else:
                        # 超过2张图片的情况，后续图片标记为混合类型
                        img.flux_image_type = FluxImageType.MIXED
                        self.logger.debug(f"🎯 [DEBUG] 第{i+1}张图片: {old_type} → MIXED, 哈希={img.get_hash()[:8]}")

            else:
                # 其他工作流类型：默认作为参考图
                for img in untyped_images:
                    img.flux_image_type = FluxImageType.REFERENCE
                self.logger.info(f"默认工作流：将 {len(untyped_images)} 张图片分类为参考图")

        except Exception as e:
            self.logger.error(f"自动分类图片失败: {e}")
            # 出错时默认分类为参考图
            for img in untyped_images:
                img.flux_image_type = FluxImageType.REFERENCE

        # 🔍 [DEBUG] 显示分类后的最终状态
        self.logger.debug(f"✅ [DEBUG] 分类完成，最终状态:")
        for i, img in enumerate(untyped_images):
            self.logger.debug(f"✅ [DEBUG] 图片 {i+1}: 类型={img.flux_image_type.value}, 哈希={img.get_hash()[:8]}")

    def _debug_workflow_image_data(self, workflow_data: Dict[str, Any]):
        """调试工作流中的图片数据状态"""
        try:
            self.logger.debug("🔍 [DEBUG] 最终工作流图片数据状态:")

            # 检查所有图片输入节点
            image_input_nodes = []
            for node_id, node_data in workflow_data.items():
                if isinstance(node_data, dict) and 'class_type' in node_data and '_meta' in node_data:
                    title = node_data['_meta'].get('title', '')
                    class_type = node_data.get('class_type', '')

                    if 'image_input' in title.lower() or class_type == 'easy loadImageBase64':
                        image_input_nodes.append((node_id, title, class_type))

            for node_id, title, class_type in image_input_nodes:
                base64_data = workflow_data[node_id].get('inputs', {}).get('base64_data', '')
                has_data = bool(base64_data and base64_data.strip())
                data_length = len(base64_data) if base64_data else 0

                status = "✅ 有数据" if has_data else "❌ 无数据"
                self.logger.debug(f"🔍 [DEBUG] 节点 {node_id} ({title}) {status}，长度: {data_length}")

                if has_data and data_length < 100:
                    self.logger.debug(f"⚠️ [DEBUG] 节点 {node_id} 数据长度异常短: {data_length}")

        except Exception as e:
            self.logger.error(f"调试工作流图片数据失败: {e}")

    async def get_generation_info(self, task_id: str) -> WorkflowResult:
        """获取生成信息 - 实现基类抽象方法"""
        return WorkflowResult(
            success=True, 
            image_data=None,
            metadata={"task_id": task_id}, 
            error_message=None
        )

    async def _wait_for_text_completion_via_api(self, prompt_id: str) -> Optional[str]:
        """通过API等待工作流完成并获取文本结果"""
        try:
            # 轮询检查状态
            max_polls = 180  # 最大轮询次数
            poll_interval = 1  # 轮询间隔(秒)

            async with aiohttp.ClientSession() as session:
                for _ in range(max_polls):
                    # 检查队列状态
                    queue_info = await self._check_queue_status(session, prompt_id)
                    if queue_info is None:
                        self.logger.error("获取队列状态失败")
                        return None

                    # 检查是否完成
                    if queue_info.get('completed', False):
                        self.logger.info("工作流执行完成")

                        # 获取输出文本
                        text_data = await self._get_output_text(session, prompt_id)
                        return text_data

                    # 检查是否出错
                    if queue_info.get('failed', False):
                        error_msg = queue_info.get('error', '未知错误')
                        self.logger.error(f"工作流执行失败: {error_msg}")
                        return None

                    # 等待下次轮询
                    await asyncio.sleep(poll_interval)

                self.logger.error("工作流执行超时")
                return None

        except Exception as e:
            self.logger.error(f"等待工作流完成失败: {e}")
            return None

    async def _get_output_text(self, session: aiohttp.ClientSession, prompt_id: str) -> Optional[str]:
        """获取输出文本"""
        try:
            # 获取历史记录找到输出
            async with session.get(f"{self.api_url}/history/{prompt_id}") as response:
                if response.status != 200:
                    return None

                history_data = await response.json()
                prompt_data = history_data.get(prompt_id, {})
                outputs = prompt_data.get('outputs', {})

                self.logger.info(f"工作流输出节点: {list(outputs.keys())}")

                # 查找文本输出 - 针对图片反推工作流
                # 优先查找ShowText节点（节点64）
                if "64" in outputs and "text" in outputs["64"]:
                    text_result = outputs["64"]["text"]
                    if text_result and len(text_result) > 0:
                        self.logger.info(f"找到ShowText节点文本输出: {text_result[0][:100]}...")
                        return text_result[0] if isinstance(text_result, list) else text_result

                # 备用：查找任何包含text字段的节点
                for node_id, node_output in outputs.items():
                    if "text" in node_output:
                        text_result = node_output["text"]
                        if text_result:
                            self.logger.info(f"找到备用文本输出 (节点{node_id}): {str(text_result)[:100]}...")
                            return text_result[0] if isinstance(text_result, list) else text_result

                self.logger.warning("没有找到输出文本")
                return None

        except Exception as e:
            self.logger.error(f"获取输出文本失败: {e}")
            return None

    def _apply_seed_only(self, workflow_data: Dict[str, Any], params: FluxParameters) -> None:
        """
        只应用seed参数到工作流，用于高清放大等特殊工作流

        Args:
            workflow_data: 工作流数据
            params: Flux参数对象
        """
        try:
            if not hasattr(params, 'seed') or params.seed <= 0:
                self.logger.info("没有有效的seed参数，跳过seed应用")
                return

            seed_updated = False

            # 第一优先级：查找EasyGlobalSeed节点
            for node_id, node_data in workflow_data.items():
                if isinstance(node_data, dict) and node_data.get('class_type') == 'EasyGlobalSeed':
                    if 'inputs' in node_data and 'seed' in node_data['inputs']:
                        node_data['inputs']['seed'] = params.seed
                        self.logger.info(f"更新EasyGlobalSeed节点{node_id}的seed: {params.seed}")
                        seed_updated = True
                        break

            # 第二优先级：查找其他种子节点
            if not seed_updated:
                sampler_seed_mapping = {
                    "FluxSamplerParams+": "seed",
                    "FluxSamplerParams": "seed",
                    "KSamplerAdvanced //Inspire": "noise_seed",
                    "KSampler": "seed",
                    "SamplerCustomAdvanced": "noise_seed"
                }

                for node_id, node_data in workflow_data.items():
                    if isinstance(node_data, dict):
                        class_type = node_data.get('class_type', '')
                        if class_type in sampler_seed_mapping:
                            seed_field = sampler_seed_mapping[class_type]
                            if 'inputs' in node_data and seed_field in node_data['inputs']:
                                node_data['inputs'][seed_field] = params.seed
                                self.logger.info(f"更新{class_type}节点{node_id}的{seed_field}: {params.seed}")
                                seed_updated = True
                                break

            if not seed_updated:
                self.logger.warning("未找到合适的种子更新节点")

        except Exception as e:
            self.logger.error(f"应用seed参数失败: {e}")



    async def close(self) -> None:
        # 预留资源释放接口
        pass


# 全局单例
flux_workflow_manager: Optional[FluxWorkflowManager] = None

def get_flux_workflow_manager(ap: Optional[app.Application] = None, pipeline_config: dict = {}) -> FluxWorkflowManager:
    """获取Flux工作流管理器单例"""
    global flux_workflow_manager
    if flux_workflow_manager is None:
        flux_workflow_manager = FluxWorkflowManager(ap, pipeline_config)
    return flux_workflow_manager