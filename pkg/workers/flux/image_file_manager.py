"""
[二次开发] Flux图片文件管理器
负责保存用户图片到本地并管理文件路径

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：Flux工作流的图片文件管理
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：DEV-03 通信模块实现
- 依赖关系：依赖本地文件系统
"""

import os
import uuid
import logging
from typing import Optional, List
from pathlib import Path


class ImageFileManager:
    """图片文件管理器"""
    
    def __init__(self, upload_dir: str = "/home/<USER>/Workspace/ComfyUI/input"):
        """
        初始化图片文件管理器
        
        Args:
            upload_dir: 图片上传目录，默认指向ComfyUI的input目录
        """
        self.upload_dir = Path(upload_dir)
        self.logger = logging.getLogger(__name__)
        
        # 确保上传目录存在
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        self.logger.info(f"图片上传目录: {self.upload_dir}")
        
        # 记录已保存的文件路径，用于后续清理
        self.saved_files: List[str] = []
    
    def save_image_to_local(self, image_data: bytes, filename: Optional[str] = None) -> str:
        """
        保存图片数据到本地文件
        
        Args:
            image_data: 图片二进制数据
            filename: 可选的文件名，如果不提供则自动生成
            
        Returns:
            str: 保存的文件绝对路径
        """
        try:
            # 生成文件名
            if filename is None:
                # 根据图片数据判断格式
                if image_data.startswith(b'\xff\xd8\xff'):
                    extension = '.jpg'
                elif image_data.startswith(b'\x89PNG\r\n\x1a\n'):
                    extension = '.png'
                elif image_data.startswith(b'GIF87a') or image_data.startswith(b'GIF89a'):
                    extension = '.gif'
                elif image_data.startswith(b'RIFF') and b'WEBP' in image_data[:12]:
                    extension = '.webp'
                else:
                    extension = '.png'  # 默认使用PNG
                
                filename = f"{uuid.uuid4().hex}{extension}"
            
            # 确保文件名有扩展名
            if not any(filename.lower().endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']):
                filename += '.png'
            
            # 构建完整路径
            file_path = self.upload_dir / filename
            
            # 保存文件
            with open(file_path, 'wb') as f:
                f.write(image_data)
            
            # 记录文件路径
            self.saved_files.append(str(file_path))
            
            self.logger.info(f"图片保存成功: {file_path} (大小: {len(image_data)} bytes)")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"保存图片失败: {e}")
            raise
    
    def save_session_images(self, session_images: List) -> List[str]:
        """
        保存会话图片列表到本地
        
        Args:
            session_images: SessionImage对象列表
            
        Returns:
            List[str]: 保存的文件路径列表
        """
        file_paths = []
        
        for i, session_image in enumerate(session_images):
            try:
                # 生成带序号的文件名
                filename = f"session_{i}_{uuid.uuid4().hex[:8]}.png"
                file_path = self.save_image_to_local(session_image.data, filename)
                file_paths.append(file_path)
                
            except Exception as e:
                self.logger.error(f"保存会话图片 {i} 失败: {e}")
                # 继续处理其他图片
                continue
        
        return file_paths
    
    def get_file_info(self, file_path: str) -> Optional[dict]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[dict]: 文件信息字典
        """
        try:
            path = Path(file_path)
            if path.exists():
                stat = path.stat()
                return {
                    'path': str(path),
                    'size': stat.st_size,
                    'modified': stat.st_mtime,
                    'exists': True
                }
            else:
                return {'path': str(path), 'exists': False}
                
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {e}")
            return None
    
    def list_saved_files(self) -> List[str]:
        """
        列出所有已保存的文件
        
        Returns:
            List[str]: 文件路径列表
        """
        return self.saved_files.copy()
    
    def cleanup_files(self, file_paths: Optional[List[str]] = None) -> int:
        """
        清理文件（暂时不实现，预留接口）
        
        Args:
            file_paths: 要清理的文件路径列表，如果为None则清理所有记录的文件
            
        Returns:
            int: 成功清理的文件数量
        """
        # 暂时不实现清理功能，只记录日志
        if file_paths is None:
            file_paths = self.saved_files
        
        self.logger.info(f"请求清理 {len(file_paths)} 个文件（暂未实现）")
        return 0


# 全局单例
image_file_manager: Optional[ImageFileManager] = None

def get_image_file_manager(upload_dir: str = "/home/<USER>/Workspace/ComfyUI/input") -> ImageFileManager:
    """获取图片文件管理器单例"""
    global image_file_manager
    if image_file_manager is None:
        image_file_manager = ImageFileManager(upload_dir)
    return image_file_manager 