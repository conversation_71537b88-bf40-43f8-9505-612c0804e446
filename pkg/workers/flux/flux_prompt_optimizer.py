"""
[二次开发] Flux 提示词优化模块
负责LLM润色、提示词增强等（专用于aigen/ControlNet/LoRA等Flux管线）

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：Flux工作流的提示词优化和LLM翻译
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：REQ-01 需求范围定义
- 依赖关系：依赖langbot的LLM模型管理器和查询对象
"""
import re
import logging
from typing import Dict, Optional, Any

class FluxPromptOptimizer:
    """
    LLM提示词优化与增强（Flux专用）
    """
    def __init__(self, lora_dict: Optional[Dict[str, str]] = None):
        self.lora_dict = lora_dict or {}
        self.logger = logging.getLogger(__name__)

    def optimize_prompt(self, prompt: str) -> str:
        """
        对输入的prompt进行优化（如润色、去噪、增强等）
        """
        if not prompt or not prompt.strip():
            return prompt

        # 基础清理
        cleaned_prompt = self._clean_prompt(prompt)

        # 简单的英文优化（如果是中文，提示用户使用英文）
        if self._is_chinese(cleaned_prompt):
            # 如果是中文，提示用户LLM翻译不可用
            self.logger.warning("检测到中文提示词，但LLM翻译不可用")
            return self._prompt_for_english(cleaned_prompt)

        # 英文提示词优化
        optimized = self._enhance_english_prompt(cleaned_prompt)
        return optimized

    def _clean_prompt(self, prompt: str) -> str:
        """清理提示词：保留中文标点，仅清理英文特殊符号"""
        cleaned = prompt.strip()
        # 检查是否包含中文
        if self._is_chinese(cleaned):
            # 仅移除英文特殊符号，保留中文标点
            # 中文标点范围：\u3000-\u303F，\uFF00-\uFFEF
            cleaned = re.sub(r'[A-Za-z0-9@#\$%\^&\*_+=<>\|~`]', '', cleaned)
            # 不移除中文逗号、句号、顿号等
        else:
            # 英文场景下移除特殊字符（保留基本标点）
            cleaned = re.sub(r'[^\w\s\-.,!?()[\]{}:;"\']', '', cleaned)
        return cleaned

    def _is_chinese(self, text: str) -> bool:
        """检测是否包含中文"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        return bool(chinese_pattern.search(text))

    def _enhance_english_prompt(self, prompt: str) -> str:
        """增强英文提示词（Flux风格）"""
        # Flux偏好高质量、细节、艺术风格
        enhanced = prompt
        quality_keywords = ['high quality', 'detailed', 'masterpiece', 'best quality', '8k', 'ultra detailed']
        if not any(keyword in enhanced.lower() for keyword in quality_keywords):
            enhanced = f"masterpiece, best quality, ultra detailed, {enhanced}"
        # 确保结尾有标点
        enhanced = enhanced.strip()
        if not enhanced.endswith('.'):
            enhanced += '.'
        return enhanced

    async def optimize_prompt_with_llm(self, prompt: str, query: Any, no_trans: bool = False) -> str:
        """
        使用LLM优化提示词（完整版本，Flux专用系统提示词和示例）

        Args:
            prompt: 用户提示词
            query: 查询对象
            no_trans: 是否禁用润色，仅简单翻译
        """
        try:
            self.logger.info(f"🔧 [DEBUG] Flux优化器开始处理，输入: {prompt}")
            self.logger.info(f"🔧 [DEBUG] 检测语言，是否为中文: {self._is_chinese(prompt)}")
            self.logger.info(f"🔧 [DEBUG] 简单翻译模式: {no_trans}")

            if self._is_chinese(prompt):
                self.logger.info(f"🔧 [DEBUG] 调用中文翻译优化...")
                result = await self._translate_and_optimize(prompt, query, no_trans)
                self.logger.info(f"🔧 [DEBUG] 中文翻译优化结果: {result}")
                return result
            else:
                if no_trans:
                    self.logger.info(f"🔧 [DEBUG] 英文输入且no_trans模式，直接返回")
                    return prompt  # 英文输入且no_trans模式，直接返回
                else:
                    self.logger.info(f"🔧 [DEBUG] 调用英文优化...")
                    result = await self._optimize_english_with_llm(prompt, query)
                    self.logger.info(f"🔧 [DEBUG] 英文优化结果: {result}")
                    return result
        except Exception as e:
            self.logger.error(f"❌ LLM提示词优化失败: {e}")
            import traceback
            self.logger.error(f"❌ 详细错误信息: {traceback.format_exc()}")
            fallback_result = self.optimize_prompt(prompt)
            self.logger.info(f"🔧 [DEBUG] 降级到基础优化，结果: {fallback_result}")
            return fallback_result

    async def _translate_and_optimize(self, chinese_prompt: str, query: Any, no_trans: bool = False) -> str:
        """
        翻译中文并优化（Flux专用系统提示词，采用自然语言+专业修饰词最佳实践）

        Args:
            chinese_prompt: 中文提示词
            query: 查询对象
            no_trans: 是否禁用润色，仅简单翻译
        """
        try:
            if no_trans:
                # 简单翻译模式：直接翻译，不润色
                system_prompt = '''You are a precise Chinese-to-English translator for image generation. Translate the user's description directly and accurately.

**CRITICAL REQUIREMENTS:**
1. Translate literally and precisely
2. Keep specific details exact (时间、颜色、风格等)
3. Do NOT add quality keywords or enhancements
4. Return ONLY the translation, no explanations
5. No titles, prefixes, or markdown formatting

**Examples:**
1. Input: 一个美丽的女孩在花园里
   Output: A beautiful girl in the garden.

2. Input: 赛博朋克风格的城市夜景
   Output: Cyberpunk style city night scene.

3. Input: 抽象的彩色几何图形
   Output: Abstract colorful geometric shapes.'''
            else:
                # 润色模式：翻译并增强（原有逻辑）
                system_prompt = '''You are an expert AI prompt optimizer. Your goal is to translate a user's Chinese description into a high-quality, detailed English prompt for an image generation AI.

**CORE PRINCIPLE: STYLE FIRST**
1.  **Identify User's Intent**: First, determine if the user is asking for a **photorealistic image** or a specific **artistic style** (e.g., anime, oil painting, sketch, etc.).
2.  **Default to Photorealism**: If no specific artistic style is mentioned, **assume the user wants a photorealistic image, especially for subjects like architecture or real-world scenes.** Optimize for realism, detail, and photographic qualities.
3.  **Respect Artistic Style**: If a specific style is mentioned (e.g., "动漫风格", "油画", "素描"), all optimizations **must enhance that specific style**. Do NOT add photorealistic details like 'photographed with a wide-angle lens' to a painting or sketch prompt.

**TASK: Convert the user's Chinese description into a high-quality English prompt.**

**CRITICAL OUTPUT REQUIREMENTS:**
1.  ONLY return the English prompt text - nothing else.
2.  NO explanations, NO Chinese text, NO additional comments.
3.  NO titles like "Optimized Prompt:", "Output:", or markdown formatting.
4.  Use natural language sentences, not just keywords.
5.  Limit to 200 words maximum.
6.  CRITICAL: If you include any Chinese text or explanations, your response will be rejected.

**RESPONSE FORMAT:**
Start your response immediately with the English prompt - no prefixes, no explanations.

---
**EXAMPLES (Photorealistic Focus - Default)**

1.  Input: 现代风格的办公楼，玻璃幕墙，夜晚灯光明亮，前景有绿植
    Output: A modern office building with glass curtain walls, illuminated brightly at night. Lush greenery decorates the entrance, the atmosphere is vibrant and energetic, photographed with a wide-angle lens, soft reflections on the glass, minimalist architectural style.

2.  Input: 一个小女孩在公园里放风筝，阳光明媚，草地翠绿
    Output: A little girl flying a kite in a sunlit park, lush green grass beneath her feet, joyful expression on her face, gentle breeze, soft natural lighting, cheerful and lively atmosphere, candid photography style.

3.  Input: 未来城市的鸟瞰图，高楼林立，空中有飞行汽车
    Output: A futuristic cityscape viewed from above, towering skyscrapers, flying cars gliding through the sky, neon lights reflecting off glass buildings, dynamic composition, cinematic perspective, inspired by sci-fi concept art.

---
**EXAMPLES (Specific Artistic Style Focus)**

4.  Input: 一个女孩和她的龙，动漫风格，吉卜力工作室
    Output: A young girl and her friendly dragon, in the enchanting anime style of Studio Ghibli, soft pastel color palette, beautiful hand-drawn background, heartwarming and whimsical atmosphere, cel shading.

5.  Input: 梵高风格的星空下的向日葵田
    Output: A field of sunflowers under a swirling, starry night sky, in the expressive, impasto oil painting style of Vincent van Gogh, thick, visible brushstrokes, dramatic use of color and light, emotional and intense mood.

6.  Input: 一个老人的铅笔素描肖像，细节丰富
    Output: A highly detailed pencil sketch portrait of an elderly man, capturing the texture of his wrinkled skin and wise eyes, cross-hatching for shadows, on textured paper, realistic yet artistic, black and white.

7.  Input: 中国水墨画风格的山水
    Output: A majestic mountain landscape in the style of traditional Chinese ink wash painting (Shanshui), minimalist composition, calligraphic brushstrokes, varying ink tones, a sense of tranquility and empty space.
'''
            user_prompt = f"Convert this Chinese description to an optimized English prompt: {chinese_prompt}"
            optimized = await self._call_llm_for_optimization(system_prompt, user_prompt, query)
            if optimized:
                # 清理响应，确保只返回英文提示词
                cleaned_prompt = self._clean_llm_response(optimized)
                return cleaned_prompt
            else:
                return self._prompt_for_english(chinese_prompt)
        except Exception as e:
            self.logger.error(f"翻译优化失败: {e}")
            return self._prompt_for_english(chinese_prompt)

    async def _optimize_english_with_llm(self, english_prompt: str, query: Any) -> str:
        """
        优化英文提示词（Flux专用系统提示词，采用自然语言+专业修饰词最佳实践）
        """
        try:
            system_prompt = '''You are a professional AI prompt optimizer specializing in Stable Diffusion, ControlNet, and LoRA workflows.

TASK: Optimize the user's English prompt using fluent, natural language sentences.

Your optimized prompt should include:
- Clear description of the subject (person, animal, building, object, etc.)
- Actions or states
- Environment and background (city, nature, interior, etc.)
- Atmosphere and emotion (e.g., warm, mysterious, futuristic)
- Artistic style or medium (oil painting, photography, digital illustration, etc.)
- Details about lighting, color, composition, camera angle, etc.
- Optionally reference famous artists, architects, or photographers

**CRITICAL OUTPUT REQUIREMENTS:**
1. ONLY return the optimized English prompt text - nothing else
2. NO explanations, NO Chinese text, NO additional comments
3. NO titles like "Optimized Prompt:", "Output:", or markdown formatting
4. NO numbered lists or bullet points in your response
5. Use natural language sentences, not just keywords
6. Limit to 200 words maximum
7. IMPORTANT: You must respond in English only. Do not use any Chinese characters in your response.
8. CRITICAL: If you include any Chinese text or explanations, your response will be rejected.

**RESPONSE FORMAT:**
Your response must be ONLY the optimized English prompt text. Do not include any other text before or after the prompt.
Start your response immediately with the English prompt - no prefixes, no explanations.

**Examples:**
1. A modern office building with glass curtain walls, illuminated brightly at night. Lush greenery decorates the entrance, the atmosphere is vibrant and energetic, photographed with a wide-angle lens, soft reflections on the glass, minimalist architectural style.
2. A little girl flying a kite in a sunlit park, lush green grass beneath her feet, joyful expression on her face, gentle breeze, soft natural lighting, cheerful and lively atmosphere, candid photography style.
3. A futuristic cityscape viewed from above, towering skyscrapers, flying cars gliding through the sky, neon lights reflecting off glass buildings, dynamic composition, cinematic perspective, inspired by sci-fi concept art.
4. An orange cat lying on a windowsill, gazing outside as rain falls, cozy indoor setting, soft diffused light, peaceful and warm mood, photorealistic style.
5. Abstract colorful geometric shapes, dynamic composition, vibrant hues, sense of movement, modern art style, high contrast, digital illustration.'''
            user_prompt = f"Optimize this English prompt: {english_prompt}"
            optimized = await self._call_llm_for_optimization(system_prompt, user_prompt, query)
            if optimized:
                # 清理响应，确保只返回英文提示词
                cleaned_prompt = self._clean_llm_response(optimized)
                return cleaned_prompt
            else:
                return self._enhance_english_prompt(english_prompt)
        except Exception as e:
            self.logger.error(f"英文优化失败: {e}")
            return self._enhance_english_prompt(english_prompt)

    async def _call_llm_for_optimization(self, system_prompt: str, user_prompt: str, query: Any) -> Optional[str]:
        """调用LLM进行优化"""
        try:
            # 详细的配置检查和日志
            if not query:
                self.logger.warning("❌ query对象为空，无法调用LLM")
                return None

            if not hasattr(query, 'pipeline_config'):
                self.logger.warning("❌ query对象缺少pipeline_config属性，无法调用LLM")
                return None

            if not query.pipeline_config:
                self.logger.warning("❌ query.pipeline_config为空，无法调用LLM")
                self.logger.info(f"🔍 query对象属性: {[attr for attr in dir(query) if not attr.startswith('_')]}")
                return None

            self.logger.info(f"✅ pipeline_config存在，类型: {type(query.pipeline_config)}")
            self.logger.info(f"🔍 pipeline_config内容: {query.pipeline_config}")

            # 支持多种runner类型的模型配置
            ai_config = query.pipeline_config.get('ai', {})
            runner_type = ai_config.get('runner', {}).get('runner', 'local-agent')
            self.logger.info(f"🔍 检测到runner类型: {runner_type}")

            model_uuid = ai_config.get(runner_type, {}).get('model', '')
            if not model_uuid:
                self.logger.warning(f"❌ 未在{runner_type}配置中找到LLM模型，无法进行提示词优化")
                self.logger.info(f"🔍 ai配置: {ai_config}")
                self.logger.info(f"🔍 {runner_type}配置: {ai_config.get(runner_type, {})}")
                return None

            self.logger.info(f"✅ 找到模型UUID: {model_uuid}")
            from ...core.workflow.query_utils import safe_get_query_ap
            ap = safe_get_query_ap(query, self.logger)
            if not ap:
                self.logger.warning("❌ 无法获取应用实例，无法调用LLM")
                return None

            self.logger.info(f"✅ 应用实例存在，model_mgr: {ap.model_mgr is not None}")
            runtime_llm_model = None
            for model in ap.model_mgr.llm_models:
                if model.model_entity.uuid == model_uuid:
                    runtime_llm_model = model
                    break
            if not runtime_llm_model:
                self.logger.warning(f"未找到模型 {model_uuid}，无法进行提示词优化")
                return None
            from ...provider import entities as llm_entities
            messages = [
                llm_entities.Message(role='system', content=system_prompt),
                llm_entities.Message(role='user', content=user_prompt)
            ]
            self.logger.info(f"🔧 [DEBUG] 准备调用LLM，系统提示词长度: {len(system_prompt)}")
            self.logger.info(f"🔧 [DEBUG] 用户提示词: {user_prompt}")

            # 设置LLM参数以提高输出一致性
            extra_args = {
                'temperature': 0.6,  # 降低随机性，提高一致性
                'max_tokens': 300,   # 限制输出长度
                'top_p': 0.9,        # 核采样参数
            }

            result = await runtime_llm_model.requester.invoke_llm(
                query,
                runtime_llm_model,
                messages,
                [],
                extra_args=extra_args,
            )

            self.logger.info(f"🔧 [DEBUG] LLM调用完成，result类型: {type(result)}")
            self.logger.info(f"🔧 [DEBUG] result属性: {[attr for attr in dir(result) if not attr.startswith('_')]}")

            response_text = self._extract_response_text(result)
            self.logger.info(f"🔧 [DEBUG] 提取的响应文本长度: {len(response_text) if response_text else 0}")
            self.logger.info(f"🔧 [DEBUG] 原始LLM响应: {response_text}")

            if response_text:
                self.logger.info("LLM提示词优化成功")
                return response_text.strip()
            else:
                self.logger.warning("LLM返回空响应")
                return None
        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}")
            return None

    def _extract_response_text(self, result) -> str:
        """提取LLM响应文本"""
        response_text = ""
        if hasattr(result, 'content') and result.content:
            if isinstance(result.content, list):
                for element in result.content:
                    if hasattr(element, 'text') and element.text:
                        response_text += element.text
            elif isinstance(result.content, str):
                response_text = result.content
            else:
                response_text = str(result.content)
        return response_text.strip()

    def _clean_llm_response(self, response: str) -> str:
        """清理LLM响应，提取纯净的英文提示词"""
        self.logger.info(f"🔧 [DEBUG] 开始清理LLM响应，原始长度: {len(response) if response else 0}")

        if not response:
            return response

        # 移除常见的解释性前缀和后缀
        response = response.strip()
        self.logger.info(f"🔧 [DEBUG] 去除空白后的响应: {response}")

        # 如果包含中文，尝试提取英文部分
        if self._is_chinese(response):
            self.logger.warning(f"🔧 [DEBUG] 检测到中文响应，尝试提取英文部分...")

            # 查找引号内的英文内容
            import re
            english_matches = re.findall(r'"([^"]*)"', response)
            self.logger.info(f"🔧 [DEBUG] 找到引号内容: {english_matches}")

            for match in english_matches:
                if not self._is_chinese(match) and len(match) > 20:
                    self.logger.info(f"🔧 [DEBUG] 找到有效英文内容: {match}")
                    return match.strip()

            # 查找Output:后的内容
            output_match = re.search(r'Output:\s*(.+?)(?:\n|$)', response, re.IGNORECASE)
            if output_match:
                output_text = output_match.group(1).strip()
                self.logger.info(f"🔧 [DEBUG] 找到Output后的内容: {output_text}")
                if not self._is_chinese(output_text):
                    return output_text

            # 如果都没找到，返回提示信息
            self.logger.warning(f"🔧 [DEBUG] 无法从中文响应中提取英文内容")
            return "⚠️ LLM returned Chinese text with explanations. Please try again or use English input."

        # 移除常见的标题和解释性文本
        import re

        # 移除常见的标题格式
        response = re.sub(r'\*\*[^*]+\*\*:?\s*', '', response)  # 移除 **Optimized Prompt:** 等
        response = re.sub(r'Optimized Prompt:?\s*', '', response, flags=re.IGNORECASE)
        response = re.sub(r'Output:?\s*', '', response, flags=re.IGNORECASE)
        response = re.sub(r'Result:?\s*', '', response, flags=re.IGNORECASE)

        lines = response.split('\n')
        clean_lines = []

        for line in lines:
            line = line.strip()
            # 跳过解释性行
            if (line.startswith('优化说明') or
                line.startswith('说明') or
                line.startswith('解释') or
                line.startswith('1.') or
                line.startswith('2.') or
                line.startswith('3.') or
                line.startswith('4.') or
                line.startswith('5.') or
                line.startswith('6.') or
                line.startswith('7.') or
                line.startswith('8.') or
                line.startswith('Explanation') or
                line.startswith('Note:') or
                line.startswith('Here') or
                line.startswith('This') or
                line == '' or
                len(line) < 10):  # 跳过太短的行
                continue
            clean_lines.append(line)

        if clean_lines:
            # 取第一行作为主要提示词，并清理引号
            result = clean_lines[0].strip('"').strip("'").strip()
            return result

        # 如果没有找到有效内容，尝试直接清理原始响应
        cleaned = response.strip().strip('"').strip("'").strip()
        if len(cleaned) > 10:
            return cleaned

        return "⚠️ Unable to extract valid prompt from LLM response."

    def _prompt_for_english(self, chinese_prompt: str) -> str:
        """提示用户使用英文提示词"""
        return f"⚠️ LLM提示词优化暂时不可用，请直接使用英文提示词。\n\n💡 建议：您可以在前端配置页面检查并保存模型配置信息。\n\n原始输入：{chinese_prompt}"

    def apply_lora(self, prompt: str, lora_name: str) -> str:
        """
        根据lora_name将LoRA模型指令插入到prompt中
        """
        lora_tag = self.lora_dict.get(lora_name, f'<lora:{lora_name}>')
        return f"{lora_tag} {prompt}"

    def analyze_prompt(self, prompt: str) -> Dict[str, str]:
        """
        分析prompt，提取关键信息（如风格、主题等）
        """
        analysis = {
            "length": str(len(prompt)),
            "language": "chinese" if self._is_chinese(prompt) else "english",
            "has_quality_keywords": str(any(kw in prompt.lower() for kw in ['high quality', 'detailed', 'masterpiece', 'ultra detailed'])),
            "word_count": str(len(prompt.split()))
        }
        return analysis 