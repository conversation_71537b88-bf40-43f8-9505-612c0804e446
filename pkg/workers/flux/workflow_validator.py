
import json
import logging
from typing import Dict, Any, List, Optional

class WorkflowValidator:
    """工作流验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.required_nodes = ["UNETLoader", "FluxSamplerParams+"]
        self.optional_nodes = ["LoraLoader", "Power Lora Loader (rgthree)"]
    
    def validate_workflow(self, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证工作流"""
        result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "fixed_nodes": []
        }
        
        try:
            # 检查必需节点
            for node_type in self.required_nodes:
                if not self._has_node_type(workflow_data, node_type):
                    result["errors"].append(f"缺少必需节点: {node_type}")
                    result["valid"] = False
            
            # 验证LoRA节点
            lora_errors = self._validate_lora_nodes(workflow_data)
            result["errors"].extend(lora_errors)
            
            # 检查连接完整性
            connection_errors = self._validate_connections(workflow_data)
            result["errors"].extend(connection_errors)
            
            if result["errors"]:
                result["valid"] = False
            
            return result
            
        except Exception as e:
            result["valid"] = False
            result["errors"].append(f"验证过程出错: {e}")
            return result
    
    def _has_node_type(self, workflow_data: Dict[str, Any], node_type: str) -> bool:
        """检查是否包含指定类型的节点"""
        for node_data in workflow_data.values():
            if isinstance(node_data, dict) and node_data.get("class_type") == node_type:
                return True
        return False
    
    def _validate_lora_nodes(self, workflow_data: Dict[str, Any]) -> List[str]:
        """验证LoRA节点"""
        errors = []
        
        for node_id, node_data in workflow_data.items():
            if not isinstance(node_data, dict):
                continue
                
            class_type = node_data.get("class_type", "")
            if "LoraLoader" in class_type or "Power Lora Loader" in class_type:
                inputs = node_data.get("inputs", {})
                
                # 检查必需输入
                required_inputs = ["model", "clip", "lora_name"]
                for input_name in required_inputs:
                    if input_name not in inputs:
                        errors.append(f"节点{node_id}缺少必需输入: {input_name}")
                
                # 检查权重范围
                for weight_type in ["strength_model", "strength_clip"]:
                    if weight_type in inputs:
                        weight = inputs[weight_type]
                        if not isinstance(weight, (int, float)) or not (0.1 <= weight <= 1.0):
                            errors.append(f"节点{node_id}权重无效: {weight_type}={weight}")
        
        return errors
    
    def _validate_connections(self, workflow_data: Dict[str, Any]) -> List[str]:
        """验证节点连接"""
        errors = []
        
        for node_id, node_data in workflow_data.items():
            if not isinstance(node_data, dict):
                continue
                
            inputs = node_data.get("inputs", {})
            for input_name, input_value in inputs.items():
                if isinstance(input_value, list) and len(input_value) >= 2:
                    target_node = input_value[0]
                    if target_node not in workflow_data:
                        errors.append(f"节点{node_id}连接到不存在的节点: {target_node}")
        
        return errors

# 全局验证器实例
workflow_validator = WorkflowValidator()
