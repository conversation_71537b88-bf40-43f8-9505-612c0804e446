"""
Kontext 工作流数据模型
定义 Kontext 专有的数据结构和枚举
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, Any, List, Optional, Tuple
import time


class AspectRatio(Enum):
    """Kontext 支持的宽高比枚举"""
    SQUARE_1_1 = "1:1"           # 正方形 1:1
    LANDSCAPE_3_2 = "3:2"        # 横版 3:2
    PORTRAIT_2_3 = "2:3"         # 竖版 2:3
    LANDSCAPE_16_9 = "16:9"      # 横版 16:9
    PORTRAIT_9_16 = "9:16"       # 竖版 9:16


class ImageInputMode(Enum):
    """图片输入模式枚举"""
    SINGLE = "single"            # 单图输入
    DUAL = "dual"                # 双图输入
    TRIPLE = "triple"            # 三图输入
    AUTO = "auto"                # 自动选择


class ExecutionMode(Enum):
    """执行模式枚举"""
    LOCAL = "local"              # 本地ComfyUI
    API = "api"                  # 远程API
    AUTO = "auto"                # 自动选择


@dataclass
class KontextParameters:
    """Kontext 工作流参数"""
    # 基础参数
    prompt: str = ""
    negative_prompt: str = ""
    aspect_ratio: AspectRatio = AspectRatio.LANDSCAPE_3_2

    # 生成参数
    steps: int = 60
    guidance: float = 4.0
    seed: int = -1
    batch_size: int = 1
    prompt_upsampling: bool = False

    # 图片参数
    image_count: int = 1
    input_mode: ImageInputMode = ImageInputMode.SINGLE
    
    # 执行参数
    execution_mode: ExecutionMode = ExecutionMode.LOCAL
    timeout: int = 180
    
    # LoRA参数
    lora_models: List[str] = field(default_factory=list)  # LoRA模型名称列表
    lora_strengths: List[float] = field(default_factory=list)  # LoRA强度列表

    # 元数据
    detected_keywords: List[str] = field(default_factory=list)
    image_analysis: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    
    def get_aspect_ratio_value(self) -> str:
        """获取宽高比字符串值"""
        return self.aspect_ratio.value
    
    def get_resolution_from_ratio(self, base_size: int = 1216) -> Tuple[int, int]:
        """根据宽高比计算分辨率 - 调整到1216以内避免重复元素"""
        ratio_str = self.aspect_ratio.value
        # 🔥 优化：调整到1216以内，避免重复元素填充问题
        # 基于实际测试，过高分辨率会导致Flux生成重复内容
        if ratio_str == "1:1":
            return (1216, 1216)  # 1.48 MP，平衡质量与内容完整性
        elif ratio_str == "3:2":
            return (1216, 832)   # 1.01 MP，经典3:2比例
        elif ratio_str == "2:3":
            return (832, 1216)   # 1.01 MP，经典2:3比例
        elif ratio_str == "16:9":
            return (1344, 756)   # 1.02 MP，宽屏比例，接近1MB
        elif ratio_str == "9:16":
            return (756, 1344)   # 1.02 MP，竖屏比例，接近1MB
        else:
            return (1216, 1216)  # 默认正方形


@dataclass
class KontextWorkflowConfig:
    """Kontext 工作流配置"""
    workflow_file: str
    image_input_count: int
    description: str
    supported_ratios: List[AspectRatio] = field(default_factory=list)
    default_params: Dict[str, Any] = field(default_factory=dict)
    node_mappings: Dict[str, str] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.supported_ratios:
            self.supported_ratios = [
                AspectRatio.SQUARE_1_1,
                AspectRatio.LANDSCAPE_3_2,
                AspectRatio.PORTRAIT_2_3,
                AspectRatio.LANDSCAPE_16_9,
                AspectRatio.PORTRAIT_9_16
            ]
        
        if not self.default_params:
            self.default_params = {
                "steps": 60,
                "guidance": 4.0,
                "prompt_upsampling": False
            }
    
    def is_compatible_with_images(self, image_count: int) -> bool:
        """检查是否与图片数量兼容"""
        return image_count == self.image_input_count
    
    def get_optimal_ratio(self, user_preference: Optional[str] = None) -> AspectRatio:
        """获取最佳宽高比"""
        if user_preference:
            try:
                return AspectRatio(user_preference)
            except ValueError:
                pass
        
        # 默认返回3:2
        return AspectRatio.LANDSCAPE_3_2


@dataclass
class KontextSession:
    """Kontext 会话信息"""
    session_id: str
    user_id: str
    chat_id: str
    
    # 会话状态
    current_workflow: Optional[str] = None
    current_images: List[bytes] = field(default_factory=list)
    current_prompt: str = ""
    current_ratio: AspectRatio = AspectRatio.LANDSCAPE_3_2
    
    # 执行历史
    execution_history: List[Dict[str, Any]] = field(default_factory=list)
    last_execution_time: Optional[float] = None
    
    # 会话元数据
    created_time: float = field(default_factory=time.time)
    last_activity: float = field(default_factory=time.time)
    
    def add_execution_record(self, execution_info: Dict[str, Any]):
        """添加执行记录"""
        self.execution_history.append({
            "timestamp": time.time(),
            "workflow": self.current_workflow,
            "prompt": self.current_prompt,
            "ratio": self.current_ratio.value,
            "image_count": len(self.current_images),
            **execution_info
        })
        self.last_execution_time = time.time()
        self.last_activity = time.time()
    
    def update_activity(self):
        """更新活动时间"""
        self.last_activity = time.time()
    
    def get_session_info(self) -> Dict[str, Any]:
        """获取会话信息"""
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "chat_id": self.chat_id,
            "current_workflow": self.current_workflow,
            "current_prompt": self.current_prompt,
            "current_ratio": self.current_ratio.value,
            "image_count": len(self.current_images),
            "execution_count": len(self.execution_history),
            "created_time": self.created_time,
            "last_activity": self.last_activity,
            "last_execution_time": self.last_execution_time
        }


@dataclass
class ImageStitchConfig:
    """图片拼接配置"""
    direction: str = "right"           # 拼接方向: right, left, up, down
    match_image_size: bool = True      # 是否匹配图片尺寸
    spacing_width: int = 20            # 间距宽度
    spacing_color: str = "white"       # 间距颜色
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "direction": self.direction,
            "match_image_size": self.match_image_size,
            "spacing_width": self.spacing_width,
            "spacing_color": self.spacing_color
        }


@dataclass
class KontextNodeConfig:
    """Kontext 节点配置"""
    node_id: str
    class_type: str
    title: str
    inputs: Dict[str, Any] = field(default_factory=dict)
    outputs: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    
    def is_input_node(self) -> bool:
        """是否为输入节点"""
        return self.class_type in ["LoadImage", "easy loadImageBase64"]
    
    def is_kontext_node(self) -> bool:
        """是否为Kontext处理节点"""
        return self.class_type == "FluxKontextProImageNode"
    
    def is_output_node(self) -> bool:
        """是否为输出节点"""
        return self.class_type == "PreviewImage"


@dataclass
class KontextExecutionResult:
    """Kontext 执行结果"""
    success: bool = False
    image_data: Optional[bytes] = None
    workflow_file: str = ""
    execution_time: float = 0.0
    total_time: float = 0.0
    
    # 参数信息
    used_parameters: Optional[KontextParameters] = None
    workflow_config: Optional[KontextWorkflowConfig] = None
    
    # 错误信息
    error_message: str = ""
    error_details: Dict[str, Any] = field(default_factory=dict)
    
    # 执行统计
    node_count: int = 0
    image_count: int = 0
    prompt_length: int = 0
    
    def get_generation_info(self) -> Dict[str, Any]:
        """获取生成信息"""
        return {
            "success": self.success,
            "workflow_file": self.workflow_file,
            "execution_time": self.execution_time,
            "total_time": self.total_time,
            "image_size": len(self.image_data) if self.image_data else 0,
            "parameters": {
                "prompt": self.used_parameters.prompt if self.used_parameters else "",
                "aspect_ratio": self.used_parameters.aspect_ratio.value if self.used_parameters else "",
                "steps": self.used_parameters.steps if self.used_parameters else 0,
                "guidance": self.used_parameters.guidance if self.used_parameters else 0.0,
                "image_count": self.used_parameters.image_count if self.used_parameters else 0
            } if self.used_parameters else {},
            "error": self.error_message if not self.success else None
        } 