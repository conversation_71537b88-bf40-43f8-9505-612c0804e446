"""
Kontext工作流的LoRA集成模块
专门为Kontext工作流选择和应用LoRA模型
"""

import logging
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from ..shared.shared_lora_manager import SharedLoraManager, LoraModel
from .kontext_workflow_models import KontextParameters


@dataclass
class KontextLoRAConfig:
    """Kontext LoRA配置"""
    name: str
    filename: str
    weight: float
    category: str
    trigger_words: List[str]


class KontextLoRAIntegration:
    """Kontext工作流的LoRA集成管理器"""
    
    def __init__(self, lora_manager: SharedLoraManager):
        self.lora_manager = lora_manager
        self.logger = logging.getLogger(__name__)
    
    async def select_loras_for_prompt(self, params: KontextParameters, max_loras: int = 3,
                                    user_preferences: dict = None) -> List[KontextLoRAConfig]:
        """
        根据提示词为Kontext工作流选择合适的LoRA模型

        Args:
            params: Kontext参数
            max_loras: 最大LoRA数量
            user_preferences: 用户偏好设置

        Returns:
            List[KontextLoRAConfig]: 选择的LoRA配置列表
        """
        try:
            selected_loras = []
            
            # 第一步：本地模型匹配 - 只匹配kontext类型的模型
            search_query = params.prompt
            selected_models = self.lora_manager.get_models_by_trigger(
                self.lora_manager._tokenize_input(search_query),
                model_type="kontext",
                user_preferences=user_preferences
            )
            
            self.logger.info(f"Kontext工作流匹配到 {len(selected_models)} 个候选LoRA模型")
            
            # 第二步：智能选择和排序
            if selected_models:
                # 按分数排序并选择最佳模型
                scored_models = self._score_and_rank_models(selected_models, search_query)
                
                # 限制数量
                selected_count = min(len(scored_models), max_loras)
                
                for i in range(selected_count):
                    model = scored_models[i]
                    lora_config = KontextLoRAConfig(
                        name=model.name,
                        filename=model.filename,
                        weight=model.weight,
                        category=model.category.value,
                        trigger_words=model.trigger_words
                    )
                    selected_loras.append(lora_config)
            
            # 第三步：如果没有找到kontext专用模型，尝试通用flux模型
            if not selected_loras:
                self.logger.info("未找到kontext专用LoRA，尝试通用flux模型")
                flux_models = self.lora_manager.get_models_by_trigger(
                    self.lora_manager._tokenize_input(search_query),
                    model_type="flux",
                    user_preferences=user_preferences
                )
                
                if flux_models:
                    # 选择最佳的flux模型作为备选
                    best_flux_model = flux_models[0]
                    lora_config = KontextLoRAConfig(
                        name=best_flux_model.name,
                        filename=best_flux_model.filename,
                        weight=best_flux_model.weight,
                        category=best_flux_model.category.value,
                        trigger_words=best_flux_model.trigger_words
                    )
                    selected_loras.append(lora_config)
                    self.logger.info(f"使用备选flux模型: {best_flux_model.name}")
            
            # 第四步：如果仍然没有找到，添加默认LoRA
            if not selected_loras:
                default_lora = self._get_default_kontext_lora()
                if default_lora:
                    selected_loras.append(default_lora)
            
            self.logger.info(f"为Kontext工作流选择了 {len(selected_loras)} 个LoRA模型")
            for lora in selected_loras:
                self.logger.info(f"  - {lora.name} (权重: {lora.weight}, 分类: {lora.category})")
            
            return selected_loras
            
        except Exception as e:
            self.logger.error(f"Kontext LoRA选择失败: {e}")
            # 返回默认LoRA
            default_lora = self._get_default_kontext_lora()
            return [default_lora] if default_lora else []
    
    def _score_and_rank_models(self, models: List[LoraModel], search_query: str) -> List[LoraModel]:
        """为模型评分并排序"""
        scored_models = []
        query_words = search_query.lower().split()
        
        for model in models:
            score = 0
            
            # 1. 触发词匹配加分
            for trigger_word in model.trigger_words:
                trigger_lower = trigger_word.lower()
                for query_word in query_words:
                    if query_word in trigger_lower or trigger_lower in query_word:
                        score += 10
            
            # 2. 名称匹配加分
            model_name_lower = model.name.lower()
            for query_word in query_words:
                if query_word in model_name_lower:
                    score += 5
            
            # 3. 分类匹配加分
            if model.category.value == "kontext":
                score += 20  # kontext专用模型优先
            elif model.category.value in ["landscape", "architecture"]:
                score += 10  # 相关分类加分
            
            # 4. 优先级模型加分
            if hasattr(model, 'is_priority') and model.is_priority:
                score += 15
            
            # 5. 评分加分
            if hasattr(model, 'rating') and model.rating:
                score += model.rating * 2
            
            scored_models.append((model, score))
        
        # 按分数排序（降序）
        scored_models.sort(key=lambda x: x[1], reverse=True)
        
        # 返回排序后的模型列表
        return [model for model, score in scored_models]
    
    def _get_default_kontext_lora(self) -> Optional[KontextLoRAConfig]:
        """获取默认的Kontext LoRA"""
        try:
            # 优先查找kontext类型的模型
            kontext_models = [
                model for model in self.lora_manager.lora_models.values()
                if model.model_type == "kontext" and model.is_active
            ]
            
            if kontext_models:
                default_model = kontext_models[0]
                return KontextLoRAConfig(
                    name=default_model.name,
                    filename=default_model.filename,
                    weight=default_model.weight,
                    category=default_model.category.value,
                    trigger_words=default_model.trigger_words
                )
            
            # 如果没有kontext专用模型，查找通用模型
            general_models = [
                model for model in self.lora_manager.lora_models.values()
                if model.model_type == "flux" and model.is_active and 
                any(keyword in model.name.lower() for keyword in ['detail', 'enhance', 'quality'])
            ]
            
            if general_models:
                default_model = general_models[0]
                return KontextLoRAConfig(
                    name=default_model.name,
                    filename=default_model.filename,
                    weight=default_model.weight,
                    category=default_model.category.value,
                    trigger_words=default_model.trigger_words
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取默认Kontext LoRA失败: {e}")
            return None
    
    def prepare_lora_params(self, selected_loras: List[KontextLoRAConfig]) -> Dict[str, Any]:
        """
        准备LoRA参数用于工作流
        
        Args:
            selected_loras: 选择的LoRA配置列表
            
        Returns:
            Dict[str, Any]: 工作流参数
        """
        lora_models = []
        lora_strengths = []
        
        for lora in selected_loras:
            lora_models.append(lora.filename)
            lora_strengths.append(lora.weight)
        
        # 确保至少有5个槽位（Power LoRA Loader支持5个）
        while len(lora_models) < 5:
            lora_models.append("")
            lora_strengths.append(1.0)
        
        return {
            'lora_models': lora_models,
            'lora_strengths': lora_strengths
        }
