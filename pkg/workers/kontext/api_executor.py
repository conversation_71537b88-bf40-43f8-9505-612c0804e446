"""
Kontext API模式工作流执行器
通过HTTP API调用远程/云端ComfyUI服务
"""

import json
import asyncio
import aiohttp
import base64
import os
import tempfile
from typing import Dict, Any, List, Optional
from .kontext_base_executor import BaseKontextExecutor
from pkg.core.workflow.manager_base import WorkflowResult


class ApiKontextExecutor(BaseKontextExecutor):
    def __init__(self, api_url: str = "http://127.0.0.1:8188", timeout: int = 180, api_key: Optional[str] = None):
        self.api_url = api_url
        self.timeout = timeout
        self.api_key = api_key
        self.session = None
        self.current_workflow_data = None  # 🔥 新增：保存工作流数据供--again功能使用
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取或创建aiohttp会话"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            # 🔥 简化：不在HTTP头部添加认证信息，而是通过extra_data传递给ComfyUI API节点
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def execute_workflow(self, prompt: str, query: Any, *args, **kwargs) -> WorkflowResult:
        try:
            # 1. 加载工作流文件
            workflow_data = await self._load_workflow_file(prompt)  # prompt作为workflow_file
            if not workflow_data:
                return WorkflowResult(success=False, error_message=f'工作流文件不存在: {prompt}')
            
            # 2. 更新工作流参数
            updated_workflow = self._update_workflow_params(workflow_data, query)
            
            # 3. 上传图片（如果有）
            if 'images' in query and query['images']:
                updated_workflow = await self._upload_images_to_workflow(updated_workflow, query['images'])

            # 🔥 修复：保存最终的工作流数据供--again功能使用
            self.current_workflow_data = updated_workflow

            # 4. 提交工作流到API
            session = await self._get_session()
            prompt_id = await self._submit_workflow(session, updated_workflow)
            if not prompt_id:
                return WorkflowResult(success=False, error_message='提交工作流失败')
            
            # 5. 等待执行完成
            image_data = await self._wait_for_completion(prompt_id)
            if not image_data:
                return WorkflowResult(success=False, error_message='工作流执行超时或失败')

            # 🔥 新增：保存工作流数据到temp目录，支持--again功能
            try:
                # 从query中提取用户信息
                user_id = getattr(query, 'user_id', 'unknown')
                chat_id = getattr(query, 'chat_id', 'unknown')
                self._save_workflow_data(updated_workflow, user_id, chat_id)
            except Exception as save_error:
                print(f"⚠️ 保存工作流数据失败，但不影响主流程: {save_error}")

            return WorkflowResult(success=True, image_data=image_data, metadata={
                'prompt_id': prompt_id,
                'workflow_file': prompt
            })
            
        except Exception as e:
            return WorkflowResult(success=False, error_message=str(e))
    
    async def _load_workflow_file(self, workflow_file: str) -> Optional[Dict[str, Any]]:
        """加载工作流文件"""
        try:
            # 检查是否是完整路径
            if os.path.exists(workflow_file):
                file_path = workflow_file
            else:
                # 假设是相对于workflows目录的路径
                file_path = os.path.join('workflows', workflow_file)
            
            if not os.path.exists(file_path):
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            print(f"加载工作流文件失败: {e}")
            return None
    
    def _update_workflow_params(self, workflow: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """更新工作流参数"""
        updated_workflow = workflow.copy()
        
        # 更新FluxKontextProImageNode参数（API模式主要使用这个节点）
        for node_id, node_data in updated_workflow.items():
            if node_data.get("class_type") == "FluxKontextProImageNode":
                if 'prompt' in params:
                    node_data["inputs"]["prompt"] = params['prompt']
                if 'aspect_ratio' in params:
                    node_data["inputs"]["aspect_ratio"] = params['aspect_ratio']
                if 'guidance' in params:
                    node_data["inputs"]["guidance"] = params['guidance']
                if 'steps' in params:
                    node_data["inputs"]["steps"] = params['steps']
                if 'seed' in params:
                    node_data["inputs"]["seed"] = params['seed']
                if 'prompt_upsampling' in params:
                    node_data["inputs"]["prompt_upsampling"] = params['prompt_upsampling']
                break
        
        # 更新CLIPTextEncode节点（如果有）
        if 'prompt' in params:
            for node_id, node_data in updated_workflow.items():
                if node_data.get("class_type") == "CLIPTextEncode":
                    # 检查是否是正面提示词节点
                    if "positive" in node_data.get("_meta", {}).get("title", "").lower():
                        node_data["inputs"]["text"] = params['prompt']
                    # 检查是否是负面提示词节点
                    elif "negative" in node_data.get("_meta", {}).get("title", "").lower():
                        node_data["inputs"]["text"] = params.get('negative_prompt', '')
        
        # 🔥 保存工作流数据供节点优先级判断使用
        self.current_workflow_data = updated_workflow

        return updated_workflow
    
    async def _upload_images_to_workflow(self, workflow: Dict[str, Any], images: List[bytes]) -> Dict[str, Any]:
        """将图片上传到工作流"""
        try:
            session = await self._get_session()
            
            # 查找图片输入节点
            image_input_nodes = []
            for node_id, node_data in workflow.items():
                if node_data.get("class_type") in ["LoadImage", "easy loadImageBase64"]:
                    image_input_nodes.append((node_id, node_data))
            
            # 按标题排序，确保正确的映射顺序
            image_input_nodes.sort(key=lambda x: x[1].get("_meta", {}).get("title", ""))
            
            # 上传图片并更新工作流
            for i, (node_id, node_data) in enumerate(image_input_nodes):
                if i < len(images):
                    # 对于API模式，主要使用base64方式
                    if node_data.get("class_type") == "easy loadImageBase64":
                        # 将图片转换为base64
                        image_base64 = base64.b64encode(images[i]).decode('utf-8')
                        node_data["inputs"]["base64_data"] = image_base64
                    elif node_data.get("class_type") == "LoadImage":
                        # 上传图片到API
                        image_name = await self._upload_image(session, images[i], f"kontext_input_{i+1}")
                        if image_name:
                            node_data["inputs"]["image"] = image_name
            
            return workflow
            
        except Exception as e:
            print(f"上传图片失败: {e}")
            return workflow
    
    async def _upload_image(self, session: aiohttp.ClientSession, image_data: bytes, filename: str) -> Optional[str]:
        """上传单张图片到API"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
                temp_file.write(image_data)
                temp_file_path = temp_file.name
            
            try:
                # 上传文件
                with open(temp_file_path, 'rb') as f:
                    data = aiohttp.FormData()
                    data.add_field('image', f, filename=filename)
                    
                    async with session.post(f"{self.api_url}/upload/image", data=data) as response:
                        if response.status == 200:
                            result = await response.json()
                            return result.get('name', filename)
                        else:
                            print(f"上传图片失败: {response.status}")
                            return None
            finally:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            print(f"上传图片出错: {e}")
            return None
    
    async def _submit_workflow(self, session: aiohttp.ClientSession, workflow: Dict[str, Any]) -> Optional[str]:
        """提交工作流到API"""
        try:
            # 🔥 关键修复：使用正确的字段名 api_key_comfy_org
            prompt_data = {"prompt": workflow}

            # 如果有API Key，添加到extra_data中（使用正确的字段名）
            if self.api_key:
                prompt_data["extra_data"] = {"api_key_comfy_org": self.api_key}
                print(f"🔑 已添加ComfyUI官方API Key到extra_data中（字段名：api_key_comfy_org）")

            async with session.post(
                f"{self.api_url}/prompt",
                json=prompt_data,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('prompt_id')
                else:
                    error_text = await response.text()
                    print(f"提交工作流失败: {response.status} - {error_text}")
                    return None
        except Exception as e:
            print(f"提交工作流出错: {e}")
            return None
    
    async def _wait_for_completion(self, prompt_id: str) -> Optional[bytes]:
        """等待工作流完成并获取图片数据 - 复用Flux成功的轮询机制"""
        try:
            # 轮询检查状态 - 复用Flux的成功实现
            max_polls = 180  # 最大轮询次数
            poll_interval = 1  # 轮询间隔(秒)
            
            session = await self._get_session()
            
            for poll_count in range(max_polls):
                # 检查队列状态 - 复用Flux的双重检查机制
                queue_info = await self._check_queue_status_flux_style(session, prompt_id)
                if queue_info is None:
                    print(f"❌ 获取队列状态失败")
                    return None
                
                # 每10次轮询输出一次状态
                if poll_count % 10 == 0:
                    print(f"🔍 [DEBUG] 轮询第{poll_count}次，状态: {queue_info.get('status', 'unknown')}")
                
                # 检查是否完成
                if queue_info.get('completed', False):
                    print(f"✅ 工作流执行完成")
                    
                    # 获取输出图片
                    image_data = await self._get_output_image_flux_style(session, prompt_id)
                    return image_data
                
                # 检查是否出错
                if queue_info.get('failed', False):
                    error_msg = queue_info.get('error', '未知错误')
                    print(f"❌ 工作流执行失败: {error_msg}")
                    return None
                
                # 等待下次轮询
                await asyncio.sleep(poll_interval)
            
            print(f"❌ 工作流执行超时，已轮询{max_polls}次")
            return None
            
        except Exception as e:
            print(f"❌ 等待工作流完成失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return None

    async def _check_queue_status_flux_style(self, session: aiohttp.ClientSession, prompt_id: str) -> Optional[Dict[str, Any]]:
        """检查队列状态 - 复用Flux的成功实现"""
        try:
            # 1. 检查当前队列
            async with session.get(f"{self.api_url}/queue", timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    queue_data = await response.json()

                    # 检查是否在执行队列中
                    queue_running = queue_data.get('queue_running', [])
                    queue_pending = queue_data.get('queue_pending', [])

                    # 检查是否在运行中
                    for item in queue_running:
                        if item[1] == prompt_id:
                            return {'completed': False, 'failed': False, 'status': 'running'}

                    # 检查是否在等待中
                    for item in queue_pending:
                        if item[1] == prompt_id:
                            print(f"🔍 [DEBUG] 工作流在队列中等待: {prompt_id}")
                            return {'completed': False, 'failed': False, 'status': 'pending'}

            # 2. 检查历史记录（已完成或失败）
            async with session.get(f"{self.api_url}/history/{prompt_id}", timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    history_data = await response.json()

                    if prompt_id in history_data:
                        prompt_data = history_data[prompt_id]

                        # 检查是否有错误
                        if 'error' in prompt_data:
                            error_info = prompt_data['error']
                            error_msg = error_info.get('message', '未知错误') if isinstance(error_info, dict) else str(error_info)
                            print(f"❌ 工作流执行失败: {error_msg}")
                            return {
                                'completed': False,
                                'failed': True,
                                'error': error_msg,
                                'status': 'failed'
                            }

                        # 检查是否有输出（成功完成）
                        outputs = prompt_data.get('outputs', {})
                        if outputs:
                            print(f"✅ 工作流执行完成: {prompt_id}")
                            return {
                                'completed': True,
                                'failed': False,
                                'outputs': outputs,
                                'status': 'completed'
                            }
                        else:
                            # 在历史中但没有输出，可能还在处理
                            return {'completed': False, 'failed': False, 'status': 'processing'}
                    else:
                        # 不在历史中，可能还在队列中或刚提交
                        return {'completed': False, 'failed': False, 'status': 'unknown'}
                else:
                    print(f"⚠️ 检查历史记录失败: {response.status}")
                    return None

        except Exception as e:
            print(f"❌ 检查队列状态异常: {e}")
            return None

    async def _get_output_image_flux_style(self, session: aiohttp.ClientSession, prompt_id: str) -> Optional[bytes]:
        """获取输出图片 - 复用Flux的成功实现"""
        try:
            print(f"🔍 [DEBUG] 开始获取输出图片，prompt_id: {prompt_id}")

            # 获取历史记录找到输出
            history_url = f"{self.api_url}/history/{prompt_id}"
            print(f"🔍 [DEBUG] 请求历史记录URL: {history_url}")

            async with session.get(history_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                print(f"🔍 [DEBUG] 历史记录响应状态: {response.status}")
                if response.status != 200:
                    print(f"❌ 获取历史记录失败: {response.status}")
                    return None

                history_data = await response.json()
                print(f"🔍 [DEBUG] 历史记录数据键: {list(history_data.keys())}")

                prompt_data = history_data.get(prompt_id, {})
                if not prompt_data:
                    print(f"❌ 历史记录中未找到prompt_id: {prompt_id}")
                    return None

                outputs = prompt_data.get('outputs', {})
                print(f"🔍 [DEBUG] 工作流输出节点: {list(outputs.keys())}")

                # 智能识别最终输出节点（复用Flux的逻辑）
                final_node_id = self._identify_final_output_node_flux_style(outputs)
                if final_node_id and final_node_id in outputs:
                    print(f"🔍 [DEBUG] 尝试从智能识别的节点{final_node_id}获取图片")
                    image_data = await self._download_image_flux_style(session, outputs[final_node_id], f"智能节点{final_node_id}")
                    if image_data:
                        print(f"✅ 从节点{final_node_id}获取图片成功，大小: {len(image_data)} bytes")
                        return image_data
                    else:
                        print(f"❌ 节点{final_node_id}图片下载失败")

                # 如果没有找到最终结果节点，查找其他预览节点
                for node_id, output_data in outputs.items():
                    if 'images' in output_data:
                        images = output_data['images']
                        if images:
                            # 获取第一张图片
                            image_info = images[0]
                            filename = image_info['filename']
                            subfolder = image_info.get('subfolder', '')
                            image_type = image_info.get('type', '')
                            
                            print(f"🔍 [DEBUG] 找到预览图片: 节点{node_id}, 文件: {filename}")
                            
                            # 下载图片
                            image_data = await self._download_image_flux_style(session, output_data, f"节点{node_id}")
                            if image_data:
                                print(f"✅ 从节点{node_id}获取图片成功，大小: {len(image_data)} bytes")
                                return image_data
                
                print(f"❌ 没有找到输出图片")
                return None
                
        except Exception as e:
            print(f"❌ 获取输出图片失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return None

    def _identify_final_output_node_flux_style(self, outputs: Dict[str, Any]) -> Optional[str]:
        """智能识别最终输出节点 - 复用Flux的逻辑"""
        try:
            # 查找所有有图片输出的节点
            image_output_nodes = []

            for node_id, output_data in outputs.items():
                if 'images' in output_data and output_data['images']:
                    image_output_nodes.append(node_id)

            if not image_output_nodes:
                print(f"⚠️ 没有找到任何图片输出节点")
                return None

            # 排除输入节点，避免把输入当成输出
            if self.current_workflow_data:
                filtered_output_nodes = []
                for node_id in image_output_nodes:
                    if node_id in self.current_workflow_data:
                        node_data = self.current_workflow_data[node_id]
                        title = node_data.get('_meta', {}).get('title', '')
                        class_type = node_data.get('class_type', '')

                        # 只排除明确标记为输入的节点
                        if 'input' in title.lower():
                            print(f"🚫 排除输入节点: {node_id} (标题: {title}, 类型: {class_type})")
                            continue

                        filtered_output_nodes.append(node_id)

                if filtered_output_nodes:
                    image_output_nodes = filtered_output_nodes
                    print(f"🔍 过滤后的输出节点: {image_output_nodes}")

            # 第一优先级：查找PreviewImage节点（最终输出）
            if self.current_workflow_data:
                for node_id in image_output_nodes:
                    if node_id in self.current_workflow_data:
                        node_data = self.current_workflow_data[node_id]
                        class_type = node_data.get('class_type', '')
                        title = node_data.get('_meta', {}).get('title', '')

                        if class_type == 'PreviewImage':
                            print(f"✅ 找到PreviewImage最终输出节点: {node_id} (标题: {title})")
                            return node_id

            # 第二优先级：查找FluxKontextProImageNode（生成节点）
            if self.current_workflow_data:
                for node_id in image_output_nodes:
                    if node_id in self.current_workflow_data:
                        node_data = self.current_workflow_data[node_id]
                        class_type = node_data.get('class_type', '')
                        title = node_data.get('_meta', {}).get('title', '')

                        if class_type == 'FluxKontextProImageNode':
                            print(f"🎨 找到FluxKontextProImageNode生成节点: {node_id} (标题: {title})")
                            return node_id

            # 如果无法确定，返回第一个有图片输出的节点
            if image_output_nodes:
                first_node = image_output_nodes[0]
                print(f"🔄 使用第一个输出节点: {first_node}")
                return first_node

            return None

        except Exception as e:
            print(f"❌ 识别最终输出节点失败: {e}")
            return None

    async def _download_image_flux_style(self, session: aiohttp.ClientSession, node_output: Dict[str, Any], source_desc: str = "未知节点") -> Optional[bytes]:
        """下载图片 - 复用Flux的实现"""
        try:
            if 'images' not in node_output or not node_output['images']:
                print(f"❌ {source_desc}没有图片输出")
                return None

            # 获取第一张图片
            image_info = node_output['images'][0]
            filename = image_info['filename']
            subfolder = image_info.get('subfolder', '')
            image_type = image_info.get('type', 'output')

            print(f"🔍 [DEBUG] 准备下载图片: {filename} (类型: {image_type})")

            # 构建下载URL
            url_params = [f"filename={filename}"]
            if subfolder:
                url_params.append(f"subfolder={subfolder}")
            if image_type:
                url_params.append(f"type={image_type}")

            url = f"{self.api_url}/view?" + "&".join(url_params)
            print(f"🔍 [DEBUG] 构建的下载URL: {url}")

            async with session.get(url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                print(f"🔍 [DEBUG] 图片下载响应状态: {response.status}")
                print(f"🔍 [DEBUG] 响应头: {dict(response.headers)}")

                if response.status == 200:
                    image_data = await response.read()
                    data_size = len(image_data)
                    print(f"✅ 下载图片成功: {filename}, 大小: {data_size} bytes")

                    # 验证图片数据
                    if data_size == 0:
                        print(f"❌ 警告: 下载的图片数据为空")
                        return None

                    return image_data
                else:
                    print(f"❌ 下载图片失败: HTTP {response.status}")
                    response_text = await response.text()
                    print(f"❌ 错误响应内容: {response_text[:200]}")
                    return None

        except Exception as e:
            print(f"❌ 下载图片异常: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return None

    def _get_node_priority(self, node_id: str, workflow_nodes: Dict[str, Any]) -> str:
        """
        动态判断节点优先级，避免硬编码节点号
        返回: 'high', 'medium', 'low'
        """
        try:
            # 如果有工作流数据，基于节点类型和标题判断
            if node_id in workflow_nodes:
                node_data = workflow_nodes[node_id]
                class_type = node_data.get('class_type', '')
                meta = node_data.get('_meta', {})
                title = meta.get('title', '').lower()

                # 高优先级：明确标记为最终输出的PreviewImage节点
                if (class_type == 'PreviewImage' and
                    ('final' in title or 'output' in title)):
                    return 'high'

                # 中等优先级：FluxKontextProImageNode（生成节点）
                if class_type == 'FluxKontextProImageNode':
                    return 'medium'

                # 中等优先级：其他PreviewImage节点
                if class_type == 'PreviewImage':
                    return 'medium'

                # 🔥 修复：输入节点直接跳过，不参与图片获取
                if ('input' in title or 'load' in class_type.lower() or
                    class_type in ['easy loadImageBase64', 'LoadImage']):
                    return 'skip'  # 新增skip级别，表示跳过

            # 如果无法确定，基于节点ID的模式判断（作为备选）
            # 通常较大的节点ID是后添加的输出节点
            try:
                node_num = int(node_id)
                if node_num >= 90:  # 通常输出节点ID较大
                    return 'medium'
                elif node_num >= 80:
                    return 'medium'
                else:
                    return 'low'
            except ValueError:
                # 非数字节点ID，默认中等优先级
                return 'medium'

        except Exception as e:
            print(f"判断节点优先级失败: {e}")
            return 'medium'  # 默认中等优先级

    def _save_workflow_data(self, workflow_data: Dict[str, Any], user_id: str, chat_id: str):
        """保存工作流数据到temp目录，支持--again功能"""
        try:
            # 创建temp目录
            temp_dir = "temp"
            os.makedirs(temp_dir, exist_ok=True)

            # 生成文件名
            filename = f"last_workflow_{user_id}_{chat_id}.json"
            filepath = os.path.join(temp_dir, filename)

            # 保存工作流数据
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(workflow_data, f, ensure_ascii=False, indent=2)

            print(f"✅ 工作流数据已保存: {filepath}")

        except Exception as e:
            print(f"❌ 保存工作流数据失败: {e}")

    def get_last_workflow_data(self) -> Optional[Dict[str, Any]]:
        """获取最后保存的工作流数据（用于--again功能）"""
        return getattr(self, 'current_workflow_data', None)