"""
[二次开发] Local Kontext 工作流管理器

封装本地 Kontext 工作流的主要业务逻辑，聚合执行器、参数处理、多图处理等专有功能。
结构与 FluxWorkflowManager 保持一致。

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：本地Kontext工作流的统一管理和执行
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：DEV-04 业务逻辑实现
- 依赖关系：依赖langbot的工作流基类和ComfyUI客户端
"""

import logging
from typing import Dict, Any, List, Optional
from .local_executor import LocalKontextExecutor
from .kontext_workflow_models import KontextParameters, KontextExecutionResult, KontextWorkflowConfig
from .multi_image_handler import MultiImageHandler
from .custom_nodes import CustomNodeHandler
from .kontext_lora_integration import KontextLoRAIntegration
from ..shared.shared_lora_manager import SharedLoraManager
from pkg.core.workflow.manager_base import BaseWorkflowManager, WorkflowResult

class LocalKontextWorkflowManager(BaseWorkflowManager):
    """
    本地 Kontext 工作流管理器
    """
    def __init__(self, api_url: str = "http://localhost:8188", timeout: int = 180):
        self.logger = logging.getLogger(__name__)
        self.executor = LocalKontextExecutor(api_url=api_url, timeout=timeout)

        # 初始化LoRA管理器和集成
        self.lora_manager = SharedLoraManager()
        self.lora_integration = KontextLoRAIntegration(self.lora_manager)
        self.multi_image_handler = MultiImageHandler()
        self.custom_node_handler = CustomNodeHandler()
        
        # 初始化工作流配置
        self.kontext_workflows = {
            1: KontextWorkflowConfig(
                workflow_file="kontext_local_1image.json",
                image_input_count=1,
                description="单图Kontext编辑"
            ),
            2: KontextWorkflowConfig(
                workflow_file="kontext_local_2images.json",
                image_input_count=2,
                description="双图Kontext编辑"
            ),
            3: KontextWorkflowConfig(
                workflow_file="kontext_local_3images.json",
                image_input_count=3,
                description="三图Kontext编辑"
            )
        }

    async def generate_image(self, user_text: str, params: Dict[str, Any], images: Optional[List[bytes]] = None) -> WorkflowResult:
        result = await self.executor.execute(user_text, params, images)
        image_bytes = None
        if hasattr(result, 'image_data') and isinstance(result.image_data, bytes):
            image_bytes = result.image_data
        elif isinstance(result, dict) and isinstance(result.get('image_data'), bytes):
            image_bytes = result.get('image_data')
        images_list = [image_bytes] if image_bytes else []
        return WorkflowResult(
            success=result.success if hasattr(result, 'success') else result.get('success', False),
            images=images_list,
            metadata=result.get_generation_info() if hasattr(result, 'get_generation_info') else {},
            workflow_type="kontext",
            error_message=getattr(result, 'error_message', result.get('error', None) if isinstance(result, dict) else None)
        )

    async def submit_workflow(self, workflow_data: Dict[str, Any], images: Optional[List[bytes]] = None) -> WorkflowResult:
        result = await self.executor.execute_workflow(workflow_data, images)
        image_bytes = None
        if hasattr(result, 'image_data') and isinstance(result.image_data, bytes):
            image_bytes = result.image_data
        elif isinstance(result, dict) and isinstance(result.get('image_data'), bytes):
            image_bytes = result.get('image_data')
        images_list = [image_bytes] if image_bytes else []
        return WorkflowResult(
            success=result.success if hasattr(result, 'success') else result.get('success', False),
            images=images_list,
            metadata=result.get_generation_info() if hasattr(result, 'get_generation_info') else {},
            workflow_type="kontext",
            error_message=getattr(result, 'error_message', result.get('error', None) if isinstance(result, dict) else None)
        )

    async def execute_workflow(self, prompt: str, query: Any) -> WorkflowResult:
        """执行Kontext工作流，包含LoRA选择"""
        try:
            self.logger.info(f"开始执行Kontext工作流: {prompt[:30]}...")

            # 步骤1: 创建Kontext参数
            params = KontextParameters(prompt=prompt)

            # 步骤2: 检查用户是否指定了LoRA参数
            user_preferences = self._build_user_preferences_from_query(query)
            has_lora_params = self._has_lora_parameters(query, user_preferences)

            # 步骤3: 只有用户明确指定LoRA时才进行选择
            selected_loras = []
            if has_lora_params:
                self.logger.info("检测到用户LoRA参数，进行智能选择")
                selected_loras = await self.lora_integration.select_loras_for_prompt(
                    params,
                    user_preferences=user_preferences
                )
            else:
                self.logger.info("用户未指定LoRA参数，跳过LoRA选择")

            # 步骤4: 准备LoRA参数
            lora_params = self.lora_integration.prepare_lora_params(selected_loras)

            # 步骤5: 合并参数
            workflow_params = {
                'prompt': prompt,
                **lora_params
            }

            self.logger.info(f"Kontext工作流参数准备完成，包含 {len(selected_loras)} 个LoRA模型")

            # 步骤5: 执行工作流（这里需要根据实际的执行逻辑来实现）
            # 暂时返回成功结果，实际执行逻辑需要在具体的执行器中实现
            return WorkflowResult(
                success=True,
                images=[],
                metadata={
                    'selected_loras': [lora.name for lora in selected_loras],
                    'lora_count': len(selected_loras)
                }
            )

        except Exception as e:
            self.logger.error(f"Kontext工作流执行失败: {e}")
            return WorkflowResult(success=False, error_message=str(e))

    async def get_generation_info(self, task_id: str) -> WorkflowResult:
        # 伪实现，实际逻辑请补充
        return WorkflowResult(success=True, metadata={"task_id": task_id})

    async def close(self) -> None:
        # 预留资源释放接口
        pass

    def select_workflow(self, image_count: int) -> KontextWorkflowConfig:
        """根据图片数量选择合适的工作流"""
        
        if image_count <= 0:
            raise ValueError("Kontext工作流至少需要一张图片")
        
        # 限制最大图片数量为3
        effective_count = min(image_count, 3)
        
        config = self.kontext_workflows[effective_count]
        self.logger.info(f"选择Kontext工作流: {config.description} (输入图片: {effective_count}张)")
        
        # 如果用户提供了超过3张图片，记录警告
        if image_count > 3:
            self.logger.warning(f"用户提供了{image_count}张图片，但Kontext工作流最多支持3张图片，将使用前3张图片")
        
        return config

    def _build_user_preferences_from_query(self, query: Any) -> dict:
        """
        从query对象中构建用户偏好参数

        Args:
            query: 查询对象

        Returns:
            dict: 用户偏好设置
        """
        user_preferences = {}

        # 检查query对象是否有用户文本，从中解析参数
        if hasattr(query, 'user_text') and query.user_text:
            try:
                from ...core.intent.parameter_parser import parameter_parser
                parsed_params = parameter_parser.parse_user_input(query.user_text)

                # 提取LoRA偏好参数
                if parsed_params.lora_prefer_keywords:
                    user_preferences['prefer_keywords'] = parsed_params.lora_prefer_keywords

                if parsed_params.lora_exclude_keywords:
                    user_preferences['exclude_keywords'] = parsed_params.lora_exclude_keywords

                self.logger.info(f"从query中提取用户偏好: {user_preferences}")

            except Exception as e:
                self.logger.warning(f"解析用户偏好失败: {e}")

        return user_preferences

    def _has_lora_parameters(self, query: Any, user_preferences: dict) -> bool:
        """
        检查用户是否指定了任何LoRA相关参数

        Args:
            query: 查询对象
            user_preferences: 用户偏好设置

        Returns:
            bool: 是否有LoRA参数
        """
        # 检查新的用户偏好参数
        if user_preferences.get('prefer_keywords') or user_preferences.get('exclude_keywords'):
            return True

        # 检查传统的Kontext LoRA参数
        if hasattr(query, 'user_text') and query.user_text:
            try:
                from ...core.intent.parameter_parser import parameter_parser
                parsed_params = parameter_parser.parse_user_input(query.user_text)

                # 检查是否有任何LoRA相关参数
                if (parsed_params.lora_names or
                    parsed_params.lora_prefer_keywords or
                    parsed_params.lora_exclude_keywords):
                    return True

            except Exception as e:
                self.logger.warning(f"检查LoRA参数失败: {e}")

        return False

    # 可根据需要扩展更多管理功能，如参数分析、节点处理等