
"""
[二次开发] Civitai API 客户端

开发说明：
- 此文件为二次开发新增功能，用于实现与Civitai API的交互。
- 功能：搜索模型、获取模型信息、下载模型文件。
- 维护者：开发团队
- 最后更新：2025-07-17
- 依赖关系：依赖 aiohttp, pyyaml
"""

import asyncio
import aiohttp
import yaml
import re  # 🔥 添加缺失的re模块导入
import logging  # 🔥 添加logging模块
from pathlib import Path
from typing import Optional, Dict, Any, List

class CivitaiApiClient:
    """
    一个用于与Civitai API交互的异步客户端。
    """
    def __init__(self, api_key: Optional[str] = None, config_path: str = "config/civitai_config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()

        self.base_url = self.config.get("base_url", "https://civitai.com/api/v1")
        self.api_key = api_key or self.config.get("api_key")
        self.proxy = self.config.get("proxy")
        self.timeout = self.config.get("timeout", 30)

        # 🔥 添加logger
        self.logger = logging.getLogger('langbot')

        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    def _load_config(self) -> Dict[str, Any]:
        """从配置文件加载完整配置"""
        try:
            config_file = Path(__file__).parent.parent.parent.parent / self.config_path
            if not config_file.exists():
                print(f"警告：Civitai配置文件不存在于 {config_file}")
                return {}

            with open(config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            print(f"错误：加载Civitai配置失败: {e}")
            return {}

    def _load_api_key(self, config_path: str) -> Optional[str]:
        """从配置文件加载API密钥。"""
        try:
            config_file = Path(__file__).parent.parent.parent.parent / config_path
            if not config_file.exists():
                print(f"警告：Civitai配置文件不存在于 {config_file}")
                return None
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            return config.get("api_key")
        except Exception as e:
            print(f"错误：加载Civitai API密钥失败: {e}")
            return None

    async def search_lora_models(self, query: str, limit: int = 10, tags: Optional[List[str]] = None, period: str = "AllTime") -> Optional[List[Dict[str, Any]]]:
        """
        根据查询词和筛选条件搜索LoRA模型，并按综合评分排序。
        
        Args:
            query: 搜索关键词。
            limit: 返回结果的数量。
            tags: 要求模型必须包含的标签列表。
            period: 时间周期 (e.g., "AllTime", "Year", "Month", "Week", "Day").
            
        Returns:
            一个经过综合评分排序的、包含模型信息的字典列表，或在失败时返回None。
        """
        if not self.api_key:
            print("错误：Civitai API密钥未配置。")
            return None

        search_url = f"{self.base_url}/models"
        params = {
            "query": query,
            "types": "LORA",
            "sort": "Relevance",  # 🔧 修复：使用相关性排序而不是下载量
            "limit": limit * 2, # 获取多一些结果以便客户端排序
            "period": period
        }
        if tags:
            params["tag"] = ",".join(tags)
        
        # 🔥 修复：配置代理和超时
        connector = aiohttp.TCPConnector(ssl=False) if self.proxy else None
        timeout = aiohttp.ClientTimeout(total=self.timeout)

        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            try:
                async with session.get(search_url, headers=self.headers, params=params, proxy=self.proxy) as response:
                    response.raise_for_status()
                    data = await response.json()
                    items = data.get("items", [])
                    
                    # 在客户端进行综合评分排序（相关性+质量）
                    sorted_items = self._sort_by_relevance_and_quality(items, query)

                    return sorted_items[:limit]

            except aiohttp.ClientError as e:
                print(f"错误：搜索Civitai模型失败: {e}")
                return None

    def _sort_by_relevance_and_quality(self, items: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """
        根据相关性和质量的综合评分对模型列表进行排序。
        """
        def calculate_score(item):
            stats = item.get("stats", {})
            downloads = stats.get("downloadCount", 0)
            likes = stats.get("favoriteCount", 0)

            # 基础质量分数
            quality_score = (downloads * 0.3) + (likes * 0.2)

            # 🔧 相关性评分：检查查询词与模型名称、标签的匹配度
            relevance_score = self._calculate_relevance_score(item, query)

            # 🔧 风格匹配惩罚：如果查询realistic但模型是illustration风格，降低分数
            style_penalty = self._calculate_style_penalty(item, query)

            # 综合评分：相关性权重最高
            total_score = (relevance_score * 0.5) + (quality_score * 0.3) - (style_penalty * 0.2)

            return total_score

        return sorted(items, key=calculate_score, reverse=True)

    def _calculate_relevance_score(self, item: Dict[str, Any], query: str) -> float:
        """计算相关性评分"""
        score = 0.0
        query_lower = query.lower()
        query_words = query_lower.split()

        # 检查模型名称匹配
        name = item.get("name", "").lower()
        for word in query_words:
            if word in name:
                score += 10.0

        # 检查标签匹配
        tags = [tag.lower() for tag in item.get("tags", [])]
        for word in query_words:
            for tag in tags:
                if word in tag:
                    score += 5.0

        return score

    def _calculate_style_penalty(self, item: Dict[str, Any], query: str) -> float:
        """计算风格不匹配的惩罚分数"""
        penalty = 0.0
        query_lower = query.lower()

        # 如果查询包含realistic相关词汇，但模型是illustration风格，给予惩罚
        realistic_keywords = ['realistic', 'photo', 'photographic', 'real']
        illustration_keywords = ['illustration', 'il', 'drawing', 'painted', 'artwork']

        has_realistic_query = any(keyword in query_lower for keyword in realistic_keywords)

        if has_realistic_query:
            name_lower = item.get("name", "").lower()
            tags_lower = [tag.lower() for tag in item.get("tags", [])]

            # 检查模型名称和标签是否包含illustration相关词汇
            for keyword in illustration_keywords:
                if keyword in name_lower:
                    penalty += 50.0  # 高惩罚
                for tag in tags_lower:
                    if keyword in tag:
                        penalty += 30.0

        return penalty

    async def download_lora_model(self, model_id: int, model_version_id: int, save_path: Path) -> Optional[Path]:
        """
        下载指定的LoRA模型文件。

        Args:
            model_id: 模型的ID。
            model_version_id: 模型版本的ID。
            save_path: 模型保存的目录路径。

        Returns:
            成功下载后的文件路径，或在失败时返回None。
        """
        if not self.api_key:
            print("错误：Civitai API密钥未配置。")
            return None

        download_url = f"https://civitai.com/api/download/models/{model_version_id}"
        
        # 🔥 修复：配置代理和超时（下载使用更长超时，支持大文件）
        connector = aiohttp.TCPConnector(ssl=False) if self.proxy else None
        download_timeout = max(self.timeout * 10, 1200)  # 下载超时至少20分钟，支持300-600MB大文件
        timeout = aiohttp.ClientTimeout(total=download_timeout)

        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            try:
                async with session.get(download_url, headers=self.headers, allow_redirects=True, proxy=self.proxy) as response:
                    response.raise_for_status()
                    
                    # 从Content-Disposition头获取文件名
                    content_disposition = response.headers.get("Content-Disposition")
                    if content_disposition:
                        filename_match = re.search(r'filename="([^"]+)"', content_disposition)
                        if filename_match:
                            filename = filename_match.group(1)
                        else:
                            filename = f"lora_model_{model_version_id}.safetensors"
                    else:
                        filename = f"lora_model_{model_version_id}.safetensors"

                    file_path = save_path / filename

                    self.logger.info(f"🔍 [DEBUG] 准备下载到: {file_path}")
                    self.logger.info(f"🔍 [DEBUG] 保存目录: {save_path}")
                    self.logger.info(f"🔍 [DEBUG] 文件名: {filename}")

                    # 确保目录存在
                    save_path.mkdir(parents=True, exist_ok=True)
                    self.logger.info(f"🔍 [DEBUG] 目录创建完成，开始下载...")

                    total_size = 0
                    content_length = response.headers.get('Content-Length')
                    expected_size = int(content_length) if content_length else None
                    last_progress = -1

                    with open(file_path, 'wb') as f:
                        while True:
                            chunk = await response.content.read(8192)  # 增大chunk size提高效率
                            if not chunk:
                                break
                            f.write(chunk)
                            total_size += len(chunk)

                            # 每10%报告一次进度，避免刷屏
                            if expected_size:
                                progress = int((total_size / expected_size) * 100)
                                if progress >= last_progress + 10 and progress <= 100:
                                    self.logger.info(f"📥 下载进度: {progress}% ({total_size // (1024 * 1024)}MB/{expected_size // (1024 * 1024)}MB)")
                                    last_progress = progress

                    # 🔧 严格验证文件是否成功写入
                    if file_path.exists() and file_path.stat().st_size > 0:
                        actual_size = file_path.stat().st_size
                        self.logger.info(f"✅ 成功下载模型到: {file_path}")
                        self.logger.info(f"📊 文件大小: {actual_size / (1024 * 1024):.2f}MB")
                        self.logger.info(f"🔍 [DEBUG] 文件验证: 存在且大小正常")
                        return str(file_path)
                    else:
                        self.logger.error(f"❌ 文件写入失败: {file_path}")
                        self.logger.error(f"🔍 [DEBUG] 文件存在: {file_path.exists()}")
                        if file_path.exists():
                            self.logger.error(f"🔍 [DEBUG] 文件大小: {file_path.stat().st_size} bytes")
                        return None
            except aiohttp.ClientError as e:
                self.logger.error(f"❌ 错误：下载Civitai模型失败: {e}")
                return None
            except Exception as e:
                self.logger.error(f"❌ 下载过程中发生未知错误: {e}")
                import traceback
                self.logger.error(traceback.format_exc())
                return None

# 可以在这里添加一个简单的测试函数
async def _test_client():
    print("正在测试Civitai API客户端...")
    client = CivitaiApiClient()
    if not client.api_key:
        print("测试中止：API Key未找到。")
        return

    # 测试搜索
    query = "cyberpunk"
    print(f"\n--- 正在搜索 '{query}' ---")
    search_results = await client.search_lora_models(query, limit=1)
    
    if search_results:
        print(f"搜索成功，找到 {len(search_results)} 个模型。")
        model = search_results[0]
        model_id = model.get("id")
        model_name = model.get("name")
        
        print(f"  - 模型ID: {model_id}")
        print(f"  - 模型名称: {model_name}")
        
        # 假设我们下载第一个版本
        model_version = model.get("modelVersions")[0]
        model_version_id = model_version.get("id")
        
        print(f"  - 模型版本ID: {model_version_id}")

        # 测试下载
        print(f"\n--- 正在尝试下载模型版本 {model_version_id} ---")
        # 创建一个临时的保存路径
        temp_save_path = Path(__file__).parent.parent.parent.parent / "temp" / "lora_downloads"
        downloaded_file = await client.download_lora_model(model_id, model_version_id, temp_save_path)
        
        if downloaded_file:
            print(f"下载测试成功！文件保存在: {downloaded_file}")
        else:
            print("下载测试失败。")
    else:
        print("搜索测试失败。")

if __name__ == '__main__':
    # 为了能够直接运行测试，需要处理asyncio事件循环
    # 在实际应用中，这个文件会被其他异步代码导入和使用
    # asyncio.run(_test_client())
    pass
