"""
[二次开发] 共享 ComfyUI 客户端

提供跨工作流的 ComfyUI 连接和基础操作

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：所有工作流共享的ComfyUI API客户端
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：DEV-03 通信模块实现
- 依赖关系：依赖aiohttp异步HTTP客户端
"""

import aiohttp
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass


@dataclass
class ComfyUIConnection:
    """ComfyUI 连接配置"""
    host: str
    port: int
    protocol: str = "http"
    api_key: Optional[str] = None
    timeout: int = 30


class SharedComfyUIClient:
    """共享 ComfyUI 客户端"""
    
    def __init__(self, connection: ComfyUIConnection):
        self.connection = connection
        self.logger = logging.getLogger(__name__)
        self._session: Optional[aiohttp.ClientSession] = None
        self._base_url = f"{connection.protocol}://{connection.host}:{connection.port}"
        
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取或创建HTTP会话"""
        if self._session is None or self._session.closed:
            headers = {"Content-Type": "application/json"}
            if self.connection.api_key:
                headers["Authorization"] = f"Bearer {self.connection.api_key}"
            
            timeout = aiohttp.ClientTimeout(total=self.connection.timeout)
            self._session = aiohttp.ClientSession(headers=headers, timeout=timeout)
        return self._session
    
    async def check_connection(self) -> bool:
        """检查连接状态"""
        try:
            session = await self._get_session()
            url = f"{self._base_url}/system_stats"
            
            async with session.get(url) as response:
                return response.status == 200
        except Exception as e:
            self.logger.error(f"连接检查失败: {e}")
            return False
    
    async def get_system_stats(self) -> Dict[str, Any]:
        """获取系统状态"""
        session = await self._get_session()
        url = f"{self._base_url}/system_stats"
        
        async with session.get(url) as response:
            if response.status == 200:
                return await response.json()
            else:
                raise Exception(f"获取系统状态失败: {response.status}")
    
    async def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        session = await self._get_session()
        url = f"{self._base_url}/queue"
        
        async with session.get(url) as response:
            if response.status == 200:
                return await response.json()
            else:
                raise Exception(f"获取队列状态失败: {response.status}")
    
    async def get_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取历史记录"""
        session = await self._get_session()
        url = f"{self._base_url}/history"
        
        params = {"limit": limit} if limit else {}
        async with session.get(url, params=params) as response:
            if response.status == 200:
                return await response.json()
            else:
                raise Exception(f"获取历史记录失败: {response.status}")
    
    async def upload_image(self, image_data: bytes, filename: str) -> str:
        """上传图片"""
        session = await self._get_session()
        url = f"{self._base_url}/upload/image"
        
        data = aiohttp.FormData()
        data.add_field('image', image_data, filename=filename, content_type='image/png')
        
        async with session.post(url, data=data) as response:
            if response.status == 200:
                result = await response.json()
                return result.get("name", filename)
            else:
                raise Exception(f"上传图片失败: {response.status}")
    
    async def download_image(self, filename: str) -> bytes:
        """下载图片"""
        session = await self._get_session()
        url = f"{self._base_url}/view/{filename}"
        
        async with session.get(url) as response:
            if response.status == 200:
                return await response.read()
            else:
                raise Exception(f"下载图片失败: {response.status}")
    
    async def clear_queue(self) -> bool:
        """清空队列"""
        session = await self._get_session()
        url = f"{self._base_url}/queue"
        
        async with session.delete(url) as response:
            return response.status == 200
    
    async def close(self):
        """关闭客户端"""
        if self._session and not self._session.closed:
            await self._session.close() 