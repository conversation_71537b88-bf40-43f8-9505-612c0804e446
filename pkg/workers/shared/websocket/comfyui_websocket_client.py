"""
[二次开发] ComfyUI WebSocket 客户端
用于接收通过SaveImageWebsocket节点传输的图片数据

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：ComfyUI WebSocket客户端，接收图片数据
- 维护者：开发团队
- 最后更新：2025-01-09
- 相关任务：ComfyUI图片传输功能

[迁移说明] 此文件已从 pkg/provider/runners/ 迁移到 pkg/workers/shared/websocket/
原因：优化架构层级，将工具类与运行器分离
迁移时间：2025-01-09
"""



import asyncio
import websockets
import json
import base64
import uuid
from typing import Optional, Dict, Any, Callable
import logging


class ComfyUIWebSocketClient:
    """ComfyUI WebSocket 客户端"""
    
    def __init__(self, api_url: str, logger: logging.Logger):
        self.api_url = api_url
        self.ws_url = api_url.replace('http://', 'ws://').replace('https://', 'wss://')
        self.logger = logger
        self.client_id = str(uuid.uuid4())
        self.websocket = None
        self.connected = False
        self.image_callbacks: Dict[str, Callable] = {}  # prompt_id -> callback
        
    async def connect(self) -> bool:
        """连接到ComfyUI WebSocket"""
        try:
            ws_url = f"{self.ws_url}/ws?clientId={self.client_id}"
            self.logger.info(f"连接到ComfyUI WebSocket: {ws_url}")
            
            self.websocket = await websockets.connect(ws_url)
            self.connected = True
            self.logger.info("WebSocket连接成功")
            
            # 启动消息监听任务
            asyncio.create_task(self._listen_messages())
            
            return True
            
        except Exception as e:
            self.logger.error(f"WebSocket连接失败: {str(e)}")
            self.connected = False
            return False
    
    async def disconnect(self):
        """断开WebSocket连接"""
        if self.websocket and self.connected:
            try:
                await self.websocket.close()
                self.connected = False
                self.logger.info("WebSocket连接已断开")
            except Exception as e:
                self.logger.error(f"断开WebSocket连接失败: {str(e)}")
    
    async def _listen_messages(self):
        """监听WebSocket消息"""
        try:
            while self.connected and self.websocket:
                message = await self.websocket.recv()
                if isinstance(message, bytes):
                    message = message.decode('utf-8')
                await self._handle_message(message)
                
        except websockets.exceptions.ConnectionClosed:
            self.logger.info("WebSocket连接被关闭")
            self.connected = False
        except Exception as e:
            self.logger.error(f"WebSocket消息监听出错: {str(e)}")
            self.connected = False
    
    async def _handle_message(self, message: str):
        """处理WebSocket消息"""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'executing':
                # 工作流执行状态
                node_id = data.get('data', {}).get('node')
                prompt_id = data.get('data', {}).get('prompt_id')
                self.logger.debug(f"执行节点 {node_id}, prompt_id: {prompt_id}")
                
            elif message_type == 'executed':
                # 节点执行完成
                node_id = data.get('data', {}).get('node')
                prompt_id = data.get('data', {}).get('prompt_id')
                output = data.get('data', {}).get('output', {})
                
                self.logger.debug(f"节点 {node_id} 执行完成, prompt_id: {prompt_id}")
                
                # 检查是否是SaveImageWebsocket节点的输出
                if 'images' in output:
                    await self._handle_image_output(prompt_id, node_id, output)
                    
            elif message_type == 'progress':
                # 进度更新
                progress_data = data.get('data', {})
                value = progress_data.get('value', 0)
                max_value = progress_data.get('max', 100)
                prompt_id = progress_data.get('prompt_id')
                
                progress_percent = (value / max_value * 100) if max_value > 0 else 0
                self.logger.debug(f"工作流进度: {progress_percent:.1f}%, prompt_id: {prompt_id}")
                
        except json.JSONDecodeError:
            self.logger.warning(f"无法解析WebSocket消息: {message}")
        except Exception as e:
            self.logger.error(f"处理WebSocket消息出错: {str(e)}")
    
    async def _handle_image_output(self, prompt_id: str, node_id: str, output: Dict[str, Any]):
        """处理图片输出"""
        try:
            images = output.get('images', [])
            if not images:
                return

            # 获取所有图片数据
            image_data_list = []
            for i, image_info in enumerate(images):
                if 'image_data' in image_info:
                    image_data = base64.b64decode(image_info['image_data'])
                    image_data_list.append(image_data)
                    self.logger.info(f"通过WebSocket接收到第{i+1}张图片数据，大小: {len(image_data)} bytes")

            if image_data_list:
                self.logger.info(f"总共接收到 {len(image_data_list)} 张图片")

                # 调用回调函数，传递所有图片
                if prompt_id in self.image_callbacks:
                    callback = self.image_callbacks[prompt_id]
                    # 如果只有一张图片，保持向后兼容，传递单张图片
                    if len(image_data_list) == 1:
                        await callback(image_data_list[0])
                    else:
                        # 多张图片，传递列表
                        await callback(image_data_list)
                    # 移除回调
                    del self.image_callbacks[prompt_id]
                else:
                    self.logger.warning(f"未找到prompt_id {prompt_id} 的回调函数")

        except Exception as e:
            self.logger.error(f"处理图片输出出错: {str(e)}")
    
    def register_image_callback(self, prompt_id: str, callback: Callable):
        """注册图片接收回调"""
        self.image_callbacks[prompt_id] = callback
        self.logger.debug(f"注册图片回调: {prompt_id}")
    
    def unregister_image_callback(self, prompt_id: str):
        """取消注册图片回调"""
        if prompt_id in self.image_callbacks:
            del self.image_callbacks[prompt_id]
            self.logger.debug(f"取消注册图片回调: {prompt_id}")
    
    async def wait_for_image(self, prompt_id: str, timeout: float = 180) -> Optional[bytes]:
        """等待接收图片数据"""
        image_data = None
        received_event = asyncio.Event()
        
        async def image_callback(data: bytes):
            nonlocal image_data
            image_data = data
            received_event.set()
        
        # 注册回调
        self.register_image_callback(prompt_id, image_callback)
        
        try:
            # 等待图片数据
            await asyncio.wait_for(received_event.wait(), timeout=timeout)
            return image_data
            
        except asyncio.TimeoutError:
            self.logger.error(f"等待图片数据超时: {prompt_id}")
            return None
        finally:
            # 清理回调
            self.unregister_image_callback(prompt_id)


# 全局WebSocket客户端实例
_websocket_client: Optional[ComfyUIWebSocketClient] = None


async def get_websocket_client(api_url: str, logger: logging.Logger) -> ComfyUIWebSocketClient:
    """获取或创建WebSocket客户端"""
    global _websocket_client
    
    if _websocket_client is None or not _websocket_client.connected:
        _websocket_client = ComfyUIWebSocketClient(api_url, logger)
        if not await _websocket_client.connect():
            raise Exception("无法连接到ComfyUI WebSocket")
    
    return _websocket_client


async def cleanup_websocket_client():
    """清理WebSocket客户端"""
    global _websocket_client
    
    if _websocket_client:
        await _websocket_client.disconnect()
        _websocket_client = None 