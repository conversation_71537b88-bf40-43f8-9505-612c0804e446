"""
[二次开发] 共享 LoRA 管理器

提供跨工作流的 LoRA 模型管理功能

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：所有工作流共享的LoRA模型管理
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：DEV-05 配置模块实现
- 依赖关系：依赖本地JSON/YAML配置文件
"""

import json
import logging
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum


class LoraCategory(Enum):
    """Lora模型分类"""
    ARCHITECTURE = "architecture"  # 建筑类
    PORTRAIT = "portrait"          # 人像类
    LANDSCAPE = "landscape"        # 风景类
    ANIME = "anime"                # 动漫类
    DETAIL = "detail"              # 细节增强类
    STYLE = "style"                # 风格类
    OBJECT = "object"              # 物体类
    KONTEXT = "kontext"            # Kontext专用类
    OTHER = "other"                # 其他类

    @classmethod
    def get_priority_score(cls, category) -> int:
        """获取分类的优先级分数（分数越高优先级越高）"""
        # 风格类LoRA - 高优先级
        style_categories = {
            cls.ANIME: 100,      # 动漫风格
            cls.STYLE: 95,       # 通用风格
            cls.DETAIL: 90,      # 细节增强
        }

        # 内容类LoRA - 低优先级
        content_categories = {
            cls.LANDSCAPE: 60,   # 风景
            cls.PORTRAIT: 50,    # 人像
            cls.ARCHITECTURE: 40, # 建筑
            cls.KONTEXT: 35,     # Kontext专用
            cls.OBJECT: 30,      # 物体
            cls.OTHER: 20,       # 其他
        }

        return style_categories.get(category, content_categories.get(category, 10))


@dataclass
class LoraModel:
    """Lora模型配置"""
    name: str                    # 模型名称
    filename: str                # 文件名
    file_path: str               # 文件路径
    category: LoraCategory       # 分类
    model_type: str              # 模型类型 (flux/sdxl/sd15)
    weight: float                # 默认权重
    trigger_words: List[str]     # 触发词
    description: str             # 描述
    civitai_id: Optional[str]    # Civitai ID
    civitai_url: Optional[str]   # Civitai URL
    rating: Optional[float]      # 评分
    downloads: Optional[int]     # 下载次数
    is_local: bool               # 是否为本地模型
    is_active: bool              # 是否启用
    is_priority: bool            # 是否为优先模型（通用性强）


class SharedLoraManager:
    """共享 LoRA 管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.lora_models: Dict[str, LoraModel] = {}
        self._initialized = False
        
    def initialize(self):
        """初始化管理器，加载配置文件中的模型"""
        if self._initialized:
            return

        try:
            # 从JSON配置文件加载（唯一配置源）
            self._load_from_json_config()
            self.logger.info(f"从JSON配置文件加载了 {len(self.lora_models)} 个LoRA模型")
        except Exception as e:
            self.logger.error(f"从JSON配置文件加载失败: {e}")
            # 加载默认模型作为备用
            self._load_default_models()
            self.logger.info("加载了默认LoRA模型")

        self._initialized = True
    
    def _load_from_json_config(self):
        """从JSON配置文件加载模型"""
        config_path = "config/lora_models.json"
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        models_data = config_data.get('models', [])
        for model_data in models_data:
            try:
                # 映射分类字符串到枚举
                category_str = model_data.get('category', 'other')
                category = self._map_category_string(category_str)
                
                model = LoraModel(
                    name=model_data['name'],
                    filename=model_data['filename'],
                    file_path=model_data.get('file_path', ''),
                    category=category,
                    model_type=model_data.get('model_type', 'flux'),  # 默认为flux
                    weight=model_data.get('weight', 0.8),
                    trigger_words=model_data.get('trigger_words', []),
                    description=model_data.get('description', ''),
                    civitai_id=model_data.get('civitai_id'),
                    civitai_url=model_data.get('civitai_url'),
                    rating=model_data.get('rating'),
                    downloads=model_data.get('downloads'),
                    is_local=model_data.get('is_local', True),
                    is_active=model_data.get('is_active', True),
                    is_priority=model_data.get('is_priority', False)
                )
                
                self.lora_models[model.name] = model
                
            except Exception as e:
                self.logger.warning(f"加载模型 {model_data.get('name', 'unknown')} 失败: {e}")
    
    # YAML配置文件支持已移除
    # 现在只使用JSON配置文件作为唯一配置源
    # 原YAML配置已迁移到JSON文件中
    
    def _load_default_models(self):
        """加载默认模型"""
        default_models = [
            LoraModel(
                name="detail_aidmafluxproultra-FLUX-v0.1",
                filename="detail_aidmafluxproultra-FLUX-v0.1.safetensors",
                file_path="/home/<USER>/Workspace/ComfyUI/models/loras/detail_aidmafluxproultra-FLUX-v0.1.safetensors",
                category=LoraCategory.DETAIL,
                weight=0.8,
                trigger_words=["细节", "detail", "高清", "high quality"],
                description="FLUX细节增强模型",
                civitai_id=None,
                civitai_url=None,
                rating=None,
                downloads=None,
                is_local=True,
                is_active=True,
                is_priority=True
            ),
            LoraModel(
                name="Anime_niji",
                filename="Anime_niji.safetensors",
                file_path="/home/<USER>/Workspace/ComfyUI/models/loras/Anime_niji.safetensors",
                category=LoraCategory.ANIME,
                model_type="flux",
                weight=0.8,
                trigger_words=["动漫", "anime", "二次元", "manga"],
                description="Niji动漫风格",
                civitai_id=None,
                civitai_url=None,
                rating=None,
                downloads=None,
                is_local=True,
                is_active=True,
                is_priority=False
            ),
            LoraModel(
                name="Flux Flat Anime",
                filename="Flux Flat Anime.safetensors",
                file_path="/home/<USER>/Workspace/ComfyUI/models/loras/Flux Flat Anime.safetensors",
                category=LoraCategory.ANIME,
                model_type="flux",
                weight=0.8,
                trigger_words=["动漫", "anime", "二次元", "manga"],
                description="Flux扁平化动漫风格",
                civitai_id=None,
                civitai_url=None,
                rating=None,
                downloads=None,
                is_local=True,
                is_active=True,
                is_priority=False
            )
        ]
        
        for model in default_models:
            self.lora_models[model.name] = model
    
    def _map_category_string(self, category_str: str) -> LoraCategory:
        """映射分类字符串到枚举"""
        category_mapping = {
            "architecture": LoraCategory.ARCHITECTURE,
            "portrait": LoraCategory.PORTRAIT,
            "landscape": LoraCategory.LANDSCAPE,
            "anime": LoraCategory.ANIME,
            "detail": LoraCategory.DETAIL,
            "style": LoraCategory.STYLE,
            "object": LoraCategory.OBJECT,
            "kontext": LoraCategory.KONTEXT,
            "other": LoraCategory.OTHER
        }
        return category_mapping.get(category_str.lower(), LoraCategory.OTHER)

    def _tokenize_input(self, text: str) -> List[str]:
        """将文本分词为小写关键词列表（改进版，更好地处理连字符和复合词）"""
        import re

        # 转换为小写
        text = text.lower()

        # 先保留连字符词汇，然后再处理
        # 1. 提取连字符词汇（如 semi-realistic）
        hyphenated_words = re.findall(r'\b\w+(?:-\w+)+\b', text)

        # 2. 移除标点符号，但保留空格
        cleaned_text = re.sub(r'[^\w\s]', ' ', text)

        # 3. 分割成词汇
        basic_tokens = [word for word in cleaned_text.split() if word and len(word) > 1]

        # 4. 添加连字符词汇的原始形式和分解形式
        all_tokens = set(basic_tokens)
        for hyphen_word in hyphenated_words:
            all_tokens.add(hyphen_word)  # 保留原始连字符形式
            # 添加分解后的部分（如 semi-realistic -> semi, realistic）
            parts = hyphen_word.split('-')
            all_tokens.update(parts)

        return list(all_tokens)
        
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        # 确保已初始化
        if not self._initialized:
            self.initialize()
            
        total = len(self.lora_models)
        active = sum(1 for model in self.lora_models.values() if model.is_active)
        local = sum(1 for model in self.lora_models.values() if model.is_local)
        remote = total - local
        priority = sum(1 for model in self.lora_models.values() if model.is_priority)
        
        # 按分类统计
        by_category = {}
        for model in self.lora_models.values():
            category = model.category.value
            if category not in by_category:
                by_category[category] = 0
            by_category[category] += 1
        
        return {
            "total": total,
            "active": active,
            "local": local,
            "remote": remote,
            "priority": priority,
            "by_category": by_category
        }
    
    def get_all_models(self) -> List[LoraModel]:
        """获取所有模型"""
        # 确保已初始化
        if not self._initialized:
            self.initialize()
            
        return list(self.lora_models.values())
    
    def get_models_by_category(self, category: LoraCategory) -> List[LoraModel]:
        """根据分类获取模型"""
        # 确保已初始化
        if not self._initialized:
            self.initialize()
            
        return [model for model in self.lora_models.values() if model.category == category]
    
    def get_models_by_trigger(self, search_keywords: List[str], model_type: str = "flux",
                             user_preferences: dict = None) -> List[LoraModel]:
        """
        根据触发词搜索LoRA模型（智能选择版本）

        Args:
            search_keywords: 搜索关键词列表（已分词的小写关键词）
            model_type: 模型类型过滤 ("flux", "sdxl", "sd15", "kontext")
            user_preferences: 用户偏好设置，包含系列偏好、排除规则等

        Returns:
            List[LoraModel]: 匹配的模型列表，按相关性排序，已去重和冲突处理
        """
        # 确保已初始化
        if not self._initialized:
            self.initialize()

        matching_models = []
        user_preferences = user_preferences or {}

        # 将输入的关键词列表转换为小写，并确保唯一性
        processed_keywords = [kw.lower() for kw in search_keywords]
        # 移除重复关键词
        processed_keywords = list(set(processed_keywords))

        # 定义近义词映射
        synonym_mapping = {
            "建筑": ["architecture", "building", "house", "construction", "建筑", "房屋", "楼房"],
            "人像": ["portrait", "person", "face", "character", "人像", "人物", "角色", "头像"],
            "风景": ["landscape", "nature", "scenery", "风景", "自然", "景观", "山水"],
            "动漫": ["anime", "manga", "cartoon", "动漫", "漫画", "卡通", "二次元"],
            "细节": ["detail", "enhance", "quality", "upscale", "细节", "增强", "质量", "高清"],
            "写实": ["realistic", "photorealistic", "真实", "写实", "照片"],
            "风格": ["style", "art", "artistic", "风格", "艺术", "画风"]
        }

        if not processed_keywords:
            self.logger.info("没有有效的搜索关键词，返回空列表")
            return []
        
        for model in self.lora_models.values():
            if not model.is_active:
                continue

            # 过滤模型类型（只匹配对应类型的模型）
            if model.model_type != model_type:
                continue

            current_score = 0.0
            
            
            
            # 1. 检查触发词智能匹配 (累积加分) - 主要匹配方式
            for trigger_word in model.trigger_words:
                trigger_lower = trigger_word.lower()

                # 精确匹配 - 最高权重，因为这是最可靠的匹配
                if trigger_lower in processed_keywords:
                    current_score += 10.0
                    continue  # 精确匹配后跳过其他匹配

                # 部分匹配 - 检查触发词是否包含在任何关键词中，或关键词包含在触发词中
                partial_match_found = False
                for keyword in processed_keywords:
                    # 双向部分匹配：触发词包含关键词 或 关键词包含触发词
                    if (len(trigger_lower) >= 3 and len(keyword) >= 3 and
                        (trigger_lower in keyword or keyword in trigger_lower)):
                        current_score += 8.0  # 部分匹配得分略低于精确匹配
                        partial_match_found = True
                        break

                if partial_match_found:
                    continue  # 找到部分匹配后跳过近义词匹配

                # 近义词匹配 - 提高权重
                for category, synonyms in synonym_mapping.items():
                    if trigger_lower in synonyms:
                        for synonym in synonyms:
                            if synonym in processed_keywords:
                                current_score += 6.0  # 从3.0提高到6.0
                                break # 找到一个近义词匹配即可

            # 2. 用户偏好加分
            if user_preferences:
                # 用户明确指定的关键词 (--lora+关键词) - 最高优先级
                prefer_keywords = user_preferences.get('prefer_keywords', [])
                for keyword in prefer_keywords:
                    if keyword.lower() in model.name.lower():
                        current_score += 20.0  # 用户明确指定获得最高加分
                        self.logger.info(f"用户偏好关键词匹配: {model.name} (+20.0)")

                # 排除关键词检查 (--lora-关键词)
                exclude_keywords = user_preferences.get('exclude_keywords', [])
                for keyword in exclude_keywords:
                    if keyword.lower() in model.name.lower():
                        current_score = -1.0  # 直接排除
                        self.logger.info(f"用户排除关键词匹配: {model.name} (排除)")
                        break

            # 3. 分类优先级加分（风格类LoRA优先级更高）
            category_score = LoraCategory.get_priority_score(model.category) / 100.0
            current_score += category_score

            # 4. 优先级模型加分
            if model.is_priority:
                current_score += 2.0  # 提高优先级模型的加分

            # 5. 评分加分
            if model.rating:
                current_score += model.rating / 5.0

            if current_score > 0:
                matching_models.append((model, current_score))

        # 按匹配分数排序
        matching_models.sort(key=lambda x: x[1], reverse=True)

        # 设置最低分数阈值，过滤掉不相关的模型
        MIN_SCORE_THRESHOLD = 5.0
        filtered_models = [(model, score) for model, score in matching_models if score >= MIN_SCORE_THRESHOLD]

        # 智能去重和冲突处理
        final_models = self._resolve_conflicts_and_deduplicate(filtered_models, user_preferences)

        return final_models

    def _resolve_conflicts_and_deduplicate(self, scored_models: List[tuple], user_preferences: dict) -> List[LoraModel]:
        """
        智能去重和冲突处理

        Args:
            scored_models: 带分数的模型列表 [(model, score), ...]
            user_preferences: 用户偏好设置

        Returns:
            List[LoraModel]: 处理后的模型列表
        """
        if not scored_models:
            return []

        # 智能检测冲突组
        conflict_groups = self._detect_conflict_groups([model for model, score in scored_models])

        # 按分数分组处理
        import random

        # 分离用户明确指定的模型（分数>=20）和其他模型
        user_specified = [(model, score) for model, score in scored_models if score >= 20.0]
        priority_models = [(model, score) for model, score in scored_models if 10.0 <= score < 20.0 and model.is_priority]
        regular_models = [(model, score) for model, score in scored_models if score < 20.0 and not model.is_priority]

        selected_models = []
        used_groups = set()

        # 处理所有模型，按优先级顺序
        all_models = user_specified + priority_models + regular_models

        for model, score in all_models:
            # 检查是否属于冲突组
            model_group = None
            for group_name, group_models in conflict_groups.items():
                if model.name in group_models:
                    model_group = group_name
                    break

            # 如果属于冲突组
            if model_group:
                if model_group in used_groups:
                    continue  # 该组已有模型被选中

                # 获取同组的所有候选模型
                group_candidates = [(m, s) for m, s in all_models
                                  if m.name in conflict_groups[model_group] and s > 0]

                if group_candidates:
                    # 按优先级分层
                    user_specified_in_group = [(m, s) for m, s in group_candidates if s >= 20.0]
                    priority_in_group = [(m, s) for m, s in group_candidates if 10.0 <= s < 20.0 and m.is_priority]
                    regular_in_group = [(m, s) for m, s in group_candidates if s < 20.0 and not m.is_priority]

                    # 选择逻辑：用户指定 > 优先级 > 随机
                    if user_specified_in_group:
                        chosen_model, chosen_score = random.choice(user_specified_in_group)
                    elif priority_in_group:
                        chosen_model, chosen_score = random.choice(priority_in_group)
                    else:
                        chosen_model, chosen_score = random.choice(regular_in_group)

                    selected_models.append(chosen_model)
                    used_groups.add(model_group)
                    self.logger.info(f"选择模型: {chosen_model.name} (组: {model_group}, 分数: {chosen_score:.2f})")
            else:
                # 不属于冲突组，直接添加
                if score > 0:  # 排除被用户排除的模型
                    selected_models.append(model)
                    self.logger.info(f"选择模型: {model.name} (分数: {score:.2f})")

        return selected_models

    def _detect_conflict_groups(self, models: List[LoraModel]) -> Dict[str, List[str]]:
        """
        智能检测冲突组

        基于以下规则检测模型间的潜在冲突：
        1. 相同分类且触发词重叠度高
        2. 触发词包含明确的冲突指示词
        3. 功能描述相似

        Args:
            models: 候选模型列表

        Returns:
            Dict[str, List[str]]: 冲突组字典 {group_name: [model_names]}
        """
        if len(models) <= 1:
            return {}

        conflict_groups = {}
        processed_models = set()

        # 定义冲突指示词组合
        conflict_indicators = {
            'anime_styles': {
                'keywords': ['anime', 'niji', '动漫', '二次元', 'manga', '漫画', 'cartoon', '卡通'],
                'exclusions': ['semi-realistic', 'semi realistic', 'realistic', '写实']  # 半写实不与纯动漫冲突
            },
            'realistic_styles': {
                'keywords': ['realistic', 'photorealistic', '写实', '真实', 'photo'],
                'exclusions': ['semi', 'anime', 'cartoon']  # 纯写实不与半写实冲突
            },
            'detail_enhancement': {
                'keywords': ['detail', 'enhance', '细节', '增强', 'sharp', 'clarity'],
                'exclusions': []
            },
            'architecture': {
                'keywords': ['architecture', 'building', '建筑', 'architectural', 'render'],
                'exclusions': []
            },
            'minimalist': {
                'keywords': ['minimal', 'simple', '简约', '极简', 'clean'],
                'exclusions': []
            },
            'flat_style': {
                'keywords': ['flat', '扁平', '平面', '2d', 'cell-shading'],
                'exclusions': []
            }
        }

        for model in models:
            if model.name in processed_models:
                continue

            # 查找与当前模型冲突的其他模型
            conflicting_models = [model.name]
            model_triggers = [word.lower() for word in model.trigger_words]

            for other_model in models:
                if other_model.name == model.name or other_model.name in processed_models:
                    continue

                other_triggers = [word.lower() for word in other_model.trigger_words]

                # 检查是否存在冲突
                if self._models_conflict(model_triggers, other_triggers, conflict_indicators):
                    conflicting_models.append(other_model.name)
                    processed_models.add(other_model.name)

            # 如果找到冲突模型，创建冲突组
            if len(conflicting_models) > 1:
                group_name = self._generate_group_name(model, conflict_indicators)
                conflict_groups[group_name] = conflicting_models
                processed_models.add(model.name)

                self.logger.info(f"检测到冲突组 '{group_name}': {conflicting_models}")

        return conflict_groups

    def _models_conflict(self, triggers1: List[str], triggers2: List[str], conflict_indicators: Dict) -> bool:
        """
        判断两个模型是否冲突

        Args:
            triggers1: 模型1的触发词列表
            triggers2: 模型2的触发词列表
            conflict_indicators: 冲突指示词配置

        Returns:
            bool: 是否冲突
        """
        # 计算触发词重叠度
        overlap_count = len(set(triggers1) & set(triggers2))
        overlap_ratio = overlap_count / min(len(triggers1), len(triggers2)) if triggers1 and triggers2 else 0

        # 如果重叠度很高，可能冲突
        if overlap_ratio >= 0.6:  # 60%以上重叠
            return True

        # 检查是否属于同一冲突类别
        for category, config in conflict_indicators.items():
            keywords = config['keywords']
            exclusions = config['exclusions']

            # 检查模型1是否匹配此类别
            model1_matches = any(keyword in trigger for trigger in triggers1 for keyword in keywords)
            model1_excluded = any(exclusion in trigger for trigger in triggers1 for exclusion in exclusions)

            # 检查模型2是否匹配此类别
            model2_matches = any(keyword in trigger for trigger in triggers2 for keyword in keywords)
            model2_excluded = any(exclusion in trigger for trigger in triggers2 for exclusion in exclusions)

            # 如果两个模型都匹配同一类别，且都没有排除词，则冲突
            if model1_matches and model2_matches and not model1_excluded and not model2_excluded:
                return True

        return False

    def _generate_group_name(self, model: LoraModel, conflict_indicators: Dict) -> str:
        """
        为冲突组生成名称

        Args:
            model: 代表性模型
            conflict_indicators: 冲突指示词配置

        Returns:
            str: 冲突组名称
        """
        model_triggers = [word.lower() for word in model.trigger_words]

        # 根据触发词确定组名
        for category, config in conflict_indicators.items():
            keywords = config['keywords']
            if any(keyword in trigger for trigger in model_triggers for keyword in keywords):
                return f"auto_{category}_{hash(model.name) % 1000}"

        # 如果没有匹配的类别，使用模型分类
        return f"auto_{model.category.value}_{hash(model.name) % 1000}"

    async def update_from_civitai(self, api_key: Optional[str] = None, query: str = "flux", limit: int = 50):
        """从Civitai更新模型信息（真实实现）"""
        try:
            from .civitai_client import civitai_client

            self.logger.info(f"从Civitai搜索模型: {query} (限制: {limit})")

            # 搜索模型
            models = await civitai_client.search_models(
                query=query,
                types="LORA",
                sort="Highest Rated",
                limit=limit,
                nsfw=False
            )

            if not models:
                return f"未找到匹配 '{query}' 的模型"

            # 更新本地模型信息
            updated_count = 0
            for civitai_model in models:
                try:
                    # 创建或更新LoRA模型
                    model_name = f"civitai_{civitai_model.id}_{civitai_model.name}"

                    # 检查是否已存在
                    if model_name in self.lora_models:
                        # 更新现有模型的Civitai信息
                        existing_model = self.lora_models[model_name]
                        existing_model.civitai_id = str(civitai_model.id)
                        existing_model.civitai_url = f"https://civitai.com/models/{civitai_model.id}"
                        existing_model.rating = civitai_model.rating
                        existing_model.downloads = civitai_model.download_count
                        existing_model.description = civitai_model.description[:200]  # 限制长度
                        existing_model.trigger_words = civitai_model.tags[:10]  # 限制标签数量
                    else:
                        # 创建新的远程模型记录
                        new_model = LoraModel(
                            name=model_name,
                            filename=f"{civitai_model.name}.safetensors",  # 假设文件名
                            file_path="",  # 远程模型暂无本地路径
                            category=self._guess_category_from_tags(civitai_model.tags),
                            model_type="flux",  # Civitai搜索默认为flux类型
                            weight=0.8,
                            trigger_words=civitai_model.tags[:10],
                            description=civitai_model.description[:200],
                            civitai_id=str(civitai_model.id),
                            civitai_url=f"https://civitai.com/models/{civitai_model.id}",
                            rating=civitai_model.rating,
                            downloads=civitai_model.download_count,
                            is_local=False,  # 远程模型
                            is_active=False,  # 默认不激活远程模型
                            is_priority=False
                        )
                        self.lora_models[model_name] = new_model

                    updated_count += 1

                except Exception as e:
                    self.logger.warning(f"更新模型 {civitai_model.name} 失败: {e}")

            self.logger.info(f"成功更新 {updated_count} 个模型信息")
            return f"✅ 已从Civitai更新 {updated_count} 个模型信息"

        except Exception as e:
            self.logger.error(f"从Civitai更新模型失败: {e}")
            return f"❌ 更新失败: {str(e)}"
    
    def update_model(self, name: str, **kwargs):
        """更新模型配置"""
        if name not in self.lora_models:
            raise ValueError(f"模型不存在: {name}")
        
        model = self.lora_models[name]
        for key, value in kwargs.items():
            if hasattr(model, key):
                setattr(model, key, value)
        
        self.logger.info(f"更新模型配置: {name} - {kwargs}")
        return f"模型 {name} 配置已更新"

    def _guess_category_from_tags(self, tags: List[str]) -> LoraCategory:
        """根据标签猜测模型分类"""
        tags_lower = [tag.lower() for tag in tags]

        # 建筑类关键词
        if any(keyword in tags_lower for keyword in ['architecture', 'building', 'house', 'construction', '建筑', '房屋']):
            return LoraCategory.ARCHITECTURE

        # 人像类关键词
        if any(keyword in tags_lower for keyword in ['portrait', 'person', 'face', 'character', '人像', '人物', '角色']):
            return LoraCategory.PORTRAIT

        # 风景类关键词
        if any(keyword in tags_lower for keyword in ['landscape', 'nature', 'scenery', '风景', '自然', '景观']):
            return LoraCategory.LANDSCAPE

        # 动漫类关键词
        if any(keyword in tags_lower for keyword in ['anime', 'manga', 'cartoon', '动漫', '漫画', '卡通']):
            return LoraCategory.ANIME

        # 细节增强类关键词
        if any(keyword in tags_lower for keyword in ['detail', 'enhance', 'quality', 'upscale', '细节', '增强', '质量']):
            return LoraCategory.DETAIL

        # 风格类关键词
        if any(keyword in tags_lower for keyword in ['style', 'art', 'artistic', '风格', '艺术']):
            return LoraCategory.STYLE

        # 物体类关键词
        if any(keyword in tags_lower for keyword in ['object', 'item', 'tool', '物体', '物品', '工具']):
            return LoraCategory.OBJECT

        # 默认为其他类
        return LoraCategory.OTHER

    async def download_civitai_model(self, model_name: str) -> Optional[str]:
        """下载Civitai模型到本地"""
        try:
            if model_name not in self.lora_models:
                return None

            model = self.lora_models[model_name]
            if model.is_local or not model.civitai_id:
                return None

            from .civitai_client import civitai_client

            # 获取模型详情
            civitai_model = await civitai_client.get_model_details(int(model.civitai_id))
            if not civitai_model:
                return None

            # 下载模型
            local_path = await civitai_client.download_model(civitai_model)
            if local_path:
                # 更新模型信息
                model.file_path = local_path
                model.is_local = True
                model.filename = os.path.basename(local_path)

                self.logger.info(f"成功下载模型: {model_name} -> {local_path}")
                return local_path

            return None

        except Exception as e:
            self.logger.error(f"下载Civitai模型失败: {e}")
            return None

    def get_model_by_civitai_id(self, civitai_id: int) -> Optional[LoraModel]:
        """根据Civitai ID获取模型"""
        civitai_id_str = str(civitai_id)
        for model in self.lora_models.values():
            if model.civitai_id == civitai_id_str:
                return model
        return None

    def check_lora_status(self, model_name: str) -> Dict[str, Any]:
        """检查LoRA模型状态"""
        if model_name not in self.lora_models:
            return {"error": f"模型不存在: {model_name}"}

        model = self.lora_models[model_name]

        status = {
            "name": model.name,
            "category": model.category.value,
            "is_local": model.is_local,
            "is_active": model.is_active,
            "weight": model.weight,
            "file_path": model.file_path,
            "civitai_id": model.civitai_id,
            "civitai_url": model.civitai_url,
            "rating": model.rating,
            "downloads": model.downloads,
            "trigger_words": model.trigger_words,
            "description": model.description
        }

        # 检查本地文件状态
        if model.is_local and model.file_path:
            from .civitai_client import civitai_client
            file_status = civitai_client.check_model_status(os.path.basename(model.file_path))
            status.update({
                "file_exists": file_status["exists"],
                "file_size": file_status["size"],
                "file_readable": file_status["readable"]
            })

        return status


# 全局实例
_lora_manager = None


def get_lora_manager() -> SharedLoraManager:
    """获取全局 LoRA 管理器实例"""
    global _lora_manager
    if _lora_manager is None:
        _lora_manager = SharedLoraManager()
        _lora_manager.initialize()  # 自动初始化
    return _lora_manager 