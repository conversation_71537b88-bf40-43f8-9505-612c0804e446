"""
[二次开发] 微信图片发送器
统一的微信图片发送实现，避免重复代码

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：提供统一的微信图片发送接口
- 维护者：开发团队
- 最后更新：2025-01-13
- 相关任务：代码冗余清理
- 依赖关系：pkg.platform.types.message
"""

import tempfile
import os
from typing import Any, Optional
from PIL import Image
import io
from pkg.platform.types import message as platform_message


class WeChatImageSender:
    """统一的微信图片发送器"""
    
    def __init__(self, logger=None):
        """
        初始化微信图片发送器
        
        Args:
            logger: 日志记录器，可选
        """
        self.logger = logger
    
    def _log(self, message: str, level: str = "info"):
        """记录日志"""
        if self.logger:
            if level == "error":
                self.logger.error(message)
            elif level == "warning":
                self.logger.warning(message)
            else:
                self.logger.info(message)

    def _compress_image_if_needed(self, image_data: bytes, max_size_mb: float = 15.0) -> bytes:
        """
        智能图片处理：清理元数据 + 无损压缩
        尊重用户的尺寸设置，不进行尺寸调整

        Args:
            image_data: 原始图片数据
            max_size_mb: 最大允许大小(MB) (仅用于日志提示，不强制限制)

        Returns:
            bytes: 处理后的图片数据
        """
        try:
            original_size = len(image_data)

            self._log(f"开始处理图片: {original_size/1024/1024:.1f}MB")

            # 🎯 所有图片都进行元数据清理和无损压缩
            # 不管大小，都要处理

            # 打开图片
            image = Image.open(io.BytesIO(image_data))

            # 🎯 检测ComfyUI无压缩PNG文件
            theoretical_size = image.width * image.height * 3  # RGB
            inflation_ratio = original_size / theoretical_size if theoretical_size > 0 else 1

            if inflation_ratio > 2.5:
                self._log(f"检测到ComfyUI无压缩PNG文件 (膨胀比例: {inflation_ratio:.1f}x)")

            # 🎯 第一步：总是进行元数据清理和无损压缩
            cleaned_data = self._clean_and_compress_image(image, original_size)
            cleaned_size = len(cleaned_data)

            self._log(f"元数据清理+无损压缩: {original_size/1024/1024:.1f}MB → {cleaned_size/1024/1024:.1f}MB")

            # 🎯 处理完成，返回清理后的图片
            self._log(f"处理完成 (压缩率: {cleaned_size/original_size*100:.1f}%)")
            return cleaned_data



        except Exception as e:
            self._log(f"图片压缩失败: {e}", "error")
            return image_data  # 压缩失败时返回原图

    def _clean_and_compress_image(self, image: Image.Image, original_size: int) -> bytes:
        """
        清理元数据并进行无损压缩

        Args:
            image: PIL图片对象
            original_size: 原始文件大小

        Returns:
            bytes: 清理和压缩后的图片数据
        """
        try:
            output = io.BytesIO()

            if image.format == 'PNG' or image.mode in ('RGBA', 'LA', 'P'):
                # PNG图片或带透明度：保持PNG格式，清理元数据 + 高压缩
                self._log("PNG格式：清理元数据 + 无损压缩")
                image.save(output, format='PNG', compress_level=9, optimize=True)
            else:
                # 其他格式：转换为JPEG，自动清理元数据
                if image.mode in ('RGBA', 'LA', 'P'):
                    # 转换为RGB
                    rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                    if image.mode == 'P':
                        image = image.convert('RGBA')
                    rgb_image.paste(image, mask=image.split()[-1] if image.mode in ('RGBA', 'LA') else None)
                    image = rgb_image

                self._log("非PNG格式：转换为JPEG + 清理元数据")
                image.save(output, format='JPEG', quality=95, optimize=True)

            return output.getvalue()

        except Exception as e:
            self._log(f"元数据清理失败: {e}", "error")
            # 失败时至少尝试基本保存
            output = io.BytesIO()
            image.save(output, format='PNG', compress_level=6)
            return output.getvalue()
    
    async def send_image_to_wechat(self, image_data: bytes, query: Any) -> bool:
        """
        发送图片到微信

        Args:
            image_data: 图片二进制数据
            query: 查询对象，包含adapter和message_event

        Returns:
            bool: 发送是否成功
        """
        try:
            self._log(f"开始发送图片到微信，原始大小: {len(image_data)} bytes ({len(image_data)/1024/1024:.1f}MB)")

            # 🔥 优化：自动压缩过大的图片 (针对ComfyUI的大PNG文件)
            processed_image_data = self._compress_image_if_needed(image_data, max_size_mb=15.0)

            # 🔥 优化：直接使用字节数据创建图片消息，避免临时文件
            self._log("直接从内存创建图片消息（无临时文件）")
            image_message = await platform_message.Image.from_local(content=processed_image_data)
            message_chain = platform_message.MessageChain([image_message])

            self._log("开始发送图片消息到微信...")

            # 发送消息
            await query.adapter.reply_message(
                message_source=query.message_event,
                message=message_chain,
                quote_origin=False
            )

            self._log("✅ 图片已成功发送到微信")
            return True

        except Exception as e:
            self._log(f"发送图片到微信失败: {e}", "error")
            return False
    
    async def create_image_message_base64(self, image_data: bytes) -> Optional[Any]:
        """
        创建base64格式的图片消息（用于某些特殊场景）
        
        Args:
            image_data: 图片二进制数据
            
        Returns:
            图片消息对象或None
        """
        try:
            import base64
            from ....provider import entities as llm_entities

            # 转换为base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            image_content = llm_entities.ContentElement.from_image_base64(image_base64)

            return llm_entities.Message(
                role='assistant',
                content=[image_content]
            )

        except Exception as e:
            self._log(f"创建base64图片消息失败: {e}", "error")
            return None
