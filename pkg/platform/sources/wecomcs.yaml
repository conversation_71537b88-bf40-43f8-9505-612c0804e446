apiVersion: v1
kind: MessagePlatformAdapter
metadata:
  name: wecomcs
  label:
    en_US: WeComCustomerService
    zh_Hans: 企业微信客服
  description:
    en_US: WeComCSAdapter
    zh_Hans: 企业微信客服适配器
  icon: wecom.png
spec:
  config:
    - name: port
      label:
        en_US: Port
        zh_Hans: 监听端口
      type: int
      required: true
      default: 2289
    - name: corpid
      label:
        en_US: Corpid
        zh_Hans: 企业ID
      type: string
      required: true
      default: ""
    - name: secret
      label:
        en_US: Secret
        zh_Hans: 密钥
      type: string
      required: true
      default: ""
    - name: token
      label:
        en_US: Token
        zh_Hans: 令牌
      type: string
      required: true
      default: ""
    - name: EncodingAESKey
      label:
        en_US: EncodingAESKey
        zh_Hans: 消息加解密密钥
      type: string
      required: true
      default: ""
execution:
  python:
    path: ./wecomcs.py
    attr: WecomCSAdapter