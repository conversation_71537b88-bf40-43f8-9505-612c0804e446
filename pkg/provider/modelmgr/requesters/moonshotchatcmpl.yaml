apiVersion: v1
kind: LLMAPIRequester
metadata:
  name: moonshot-chat-completions
  label:
    en_US: Moonshot
    zh_Hans: 月之暗面
  icon: moonshot.png
spec:
  config:
    - name: base_url
      label:
        en_US: Base URL
        zh_Hans: 基础 URL
      type: string
      required: true
      default: "https://api.moonshot.com/v1"
    - name: timeout
      label:
        en_US: Timeout
        zh_Hans: 超时时间
      type: integer
      required: true
      default: 120
execution:
  python:
    path: ./moonshotchatcmpl.py
    attr: MoonshotChatCompletions
