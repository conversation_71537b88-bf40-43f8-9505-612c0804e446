apiVersion: v1
kind: LLMAPIRequester
metadata:
  name: deepseek-chat-completions
  label:
    en_US: DeepSeek
    zh_Hans: 深度求索
  icon: deepseek.svg
spec:
  config:
    - name: base_url
      label:
        en_US: Base URL
        zh_Hans: 基础 URL
      type: string
      required: true
      default: "https://api.deepseek.com"
    - name: timeout
      label:
        en_US: Timeout
        zh_Hans: 超时时间
      type: integer
      required: true
      default: 120
execution:
  python:
    path: ./deepseekchatcmpl.py
    attr: DeepseekChatCompletions