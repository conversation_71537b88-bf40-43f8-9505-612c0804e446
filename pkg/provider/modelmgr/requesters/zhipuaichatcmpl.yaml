apiVersion: v1
kind: LLMAPIRequester
metadata:
  name: zhipuai-chat-completions
  label:
    en_US: ZhipuAI
    zh_Hans: 智谱 AI
  icon: zhipuai.svg
spec:
  config:
    - name: base_url
      label:
        en_US: Base URL
        zh_Hans: 基础 URL
      type: string
      required: true
      default: "https://open.bigmodel.cn/api/paas/v4"
    - name: timeout
      label:
        en_US: Timeout
        zh_Hans: 超时时间
      type: integer
      required: true
      default: 120
execution:
  python:
    path: ./zhipuaichatcmpl.py
    attr: ZhipuAIChatCompletions
