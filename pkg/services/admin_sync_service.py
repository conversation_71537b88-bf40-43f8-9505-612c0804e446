import asyncio
import logging
from typing import Dict, Any, Optional, TYPE_CHECKING
from datetime import datetime

from pkg.platform.types import events as platform_events, message as platform_message

if TYPE_CHECKING:
    from pkg.platform.adapter import MessagePlatformAdapter

class AdminSyncService:
    """管理员消息同步服务，控制是否向管理员微信同步用户生成的图片和消息"""
    
    def __init__(self, pipeline_config: Dict[str, Any], query, logger):
        """
        初始化管理员同步服务
        :param pipeline_config: 流水线配置信息
        :param query: 查询对象，用于获取机器人配置
        :param logger: 日志记录器
        """
        self.pipeline_config = pipeline_config
        self.query = query
        self.logger = logger
        # 从ComfyUI Agent配置中读取管理员同步设置
        self.comfyui_config = pipeline_config.get("ai", {}).get("comfyui-agent", {})
        
    def is_sync_enabled(self) -> bool:
        """检查是否启用了管理员同步"""
        return self.comfyui_config.get("admin-sync-enabled", False)
    
    def get_admin_wxid(self) -> Optional[str]:
        """获取管理员微信ID - 从机器人配置中获取"""
        try:
            # 从query中获取机器人配置
            if hasattr(self.query, 'adapter') and hasattr(self.query.adapter, 'config'):
                # 获取当前微信适配器的wxid配置
                admin_wxid = self.query.adapter.config.get('wxid', '')
                return admin_wxid if admin_wxid else None
            return None
        except Exception as e:
            self.logger.error(f"获取管理员微信ID失败: {str(e)}")
            return None
    
    async def sync_user_request(
        self, 
        event: platform_events.GroupMessage,
        analysis: Dict[str, Any],
        adapter_instance: "MessagePlatformAdapter"
    ):
        """
        同步用户图片生成请求到管理员
        :param event: 群消息事件
        :param analysis: 请求分析结果
        :param adapter_instance: 平台适配器实例
        """
        if not self.is_sync_enabled():
            return
            
        admin_wxid = self.get_admin_wxid()
        if not admin_wxid:
            self.logger.warning("管理员同步已启用但未配置管理员微信ID")
            return
            
        try:
            # 构建通知消息
            message_text = self._build_simple_request_notification(event, analysis)
            
            # 发送通知到管理员
            await adapter_instance.send_message(
                target_type="friend",
                target_id=admin_wxid,
                message=platform_message.MessageChain([
                    platform_message.Plain(message_text)
                ])
            )
            
            self.logger.info(f"已向管理员同步用户请求: {admin_wxid}")
            
        except Exception as e:
            self.logger.error(f"同步用户请求到管理员失败: {str(e)}")
    
    async def sync_generation_status(
        self,
        event: platform_events.GroupMessage,
        status: str,
        details: str = "",
        adapter_instance: Optional["MessagePlatformAdapter"] = None
    ):
        """
        同步生成状态到管理员
        :param event: 群消息事件
        :param status: 状态 (starting, completed, failed)
        :param details: 详细信息
        :param adapter_instance: 平台适配器实例
        """
        if not self.is_sync_enabled():
            return
            
        admin_wxid = self.get_admin_wxid()
        if not admin_wxid or not adapter_instance:
            return
            
        try:
            status_text = {
                "starting": "🎨 开始生成",
                "completed": "✅ 生成完成", 
                "failed": "❌ 生成失败"
            }.get(status, status)
            
            message_text = f"[图片生成状态] {status_text}\n"
            message_text += f"群组: {event.sender.group.name[:20]}\n"
            message_text += f"用户: {event.sender.member_name[:10]}\n"
            message_text += f"时间: {datetime.now().strftime('%H:%M:%S')}"
            
            if details:
                message_text += f"\n详情: {details[:50]}"
            
            await adapter_instance.send_message(
                target_type="friend",
                target_id=admin_wxid,
                message=platform_message.MessageChain([
                    platform_message.Plain(message_text)
                ])
            )
            
        except Exception as e:
            self.logger.error(f"同步生成状态到管理员失败: {str(e)}")
    
    async def sync_generated_image(
        self,
        event: platform_events.GroupMessage,
        image_path: str,
        analysis: Dict[str, Any],
        adapter_instance: "MessagePlatformAdapter"
    ):
        """
        同步生成的图片到管理员
        :param event: 群消息事件
        :param image_path: 图片路径
        :param analysis: 请求分析结果
        :param adapter_instance: 平台适配器实例
        """
        if not self.is_sync_enabled():
            return
            
        admin_wxid = self.get_admin_wxid()
        if not admin_wxid:
            return
            
        try:
            # 构建图片描述信息
            description = self._build_image_description(event, analysis)
            
            # 发送描述信息
            await adapter_instance.send_message(
                target_type="friend",
                target_id=admin_wxid,
                message=platform_message.MessageChain([
                    platform_message.Plain(description)
                ])
            )
            
            # 发送图片
            await adapter_instance.send_message(
                target_type="friend", 
                target_id=admin_wxid,
                message=platform_message.MessageChain([
                    platform_message.Image(path=image_path)
                ])
            )
            
            self.logger.info(f"已向管理员同步生成图片: {admin_wxid}")
            
        except Exception as e:
            self.logger.error(f"同步生成图片到管理员失败: {str(e)}")
    
    def _build_simple_request_notification(
        self, 
        event: platform_events.GroupMessage, 
        analysis: Dict[str, Any]
    ) -> str:
        """构建简单的请求通知消息"""
        return (
            f"[图片生成请求]\n"
            f"群组: {event.sender.group.name[:20]}\n"
            f"用户: {event.sender.member_name[:10]}\n"
            f"主题: {analysis.get('theme', '未指定')[:30]}"
        )
    

    
    def _build_image_description(
        self, 
        event: platform_events.GroupMessage, 
        analysis: Dict[str, Any]
    ) -> str:
        """构建图片描述信息"""
        return (
            f"[生成完成]\n"
            f"群组: {event.sender.group.name[:20]}\n"
            f"用户: {event.sender.member_name[:10]}\n"
            f"主题: {analysis.get('theme', '未指定')[:30]}\n"
            f"时间: {datetime.now().strftime('%H:%M:%S')}"
        ) 