from __future__ import annotations


from . import rule

from .. import stage, entities
from ...core import entities as core_entities
from ...utils import importutil

from . import rules

importutil.import_modules_in_pkg(rules)


@stage.stage_class('GroupRespondRuleCheckStage')
class GroupRespondRuleCheckStage(stage.PipelineStage):
    """群组响应规则检查器

    仅检查群消息是否符合规则。
    """

    rule_matchers: list[rule.GroupRespondRule]
    """检查器实例"""

    async def initialize(self, pipeline_config: dict):
        """初始化检查器"""

        self.rule_matchers = []

        for rule_matcher in rule.preregisetered_rules:
            rule_inst = rule_matcher(self.ap)
            await rule_inst.initialize()
            self.rule_matchers.append(rule_inst)

    async def process(self, query: core_entities.Query, stage_inst_name: str) -> entities.StageProcessResult:
        if query.launcher_type.value != 'group':  # 只处理群消息
            return entities.StageProcessResult(result_type=entities.ResultType.CONTINUE, new_query=query)

        # 🔥 新增：检查是否有活跃会话
        # 如果用户有活跃会话，跳过@bot检查，直接放行
        try:
            from ...core.message.processor import get_message_processor
            processor = get_message_processor(self.ap)

            # 获取用户ID和聊天ID
            user_id = str(query.sender_id)
            chat_id = f"{query.launcher_type.value}_{query.launcher_id}"

            # 检查会话状态
            session_info = processor.get_session_info(user_id, chat_id)
            if session_info and session_info.get('is_active', False):
                # 记录会话信息用于调试（包含多用户隔离验证）
                workflow_type = session_info.get('workflow_type', 'unknown')
                state = session_info.get('state', 'unknown')
                session_id = session_info.get('session_id', 'unknown')
                self.ap.logger.info(f"🔄 群聊会话检查 - 用户: {user_id}, 聊天: {chat_id}")
                self.ap.logger.info(f"📋 会话详情 - ID: {session_id}, 工作流: {workflow_type}, 状态: {state}")
                self.ap.logger.info(f"✅ 跳过@bot检查，直接放行消息: {str(query.message_chain)[:50]}...")

                return entities.StageProcessResult(
                    result_type=entities.ResultType.CONTINUE,
                    new_query=query,
                )
            else:
                # 没有活跃会话，需要进行正常的@bot检查
                self.ap.logger.debug(f"🔍 群聊规则检查 - 用户 {user_id} 无活跃会话，需要@bot检查")

        except Exception as e:
            self.ap.logger.warning(f"⚠️ 检查会话状态失败: {e}")
            # 出错时继续正常的规则检查流程

        rules = query.pipeline_config['trigger']['group-respond-rules']

        use_rule = rules

        # TODO revert it
        # if str(query.launcher_id) in rules:
        #     use_rule = rules[str(query.launcher_id)]

        for rule_matcher in self.rule_matchers:  # 任意一个匹配就放行
            res = await rule_matcher.match(str(query.message_chain), query.message_chain, use_rule, query)
            if res.matching:
                query.message_chain = res.replacement

                return entities.StageProcessResult(
                    result_type=entities.ResultType.CONTINUE,
                    new_query=query,
                )

        return entities.StageProcessResult(result_type=entities.ResultType.INTERRUPT, new_query=query)
