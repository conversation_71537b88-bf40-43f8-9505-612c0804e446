from .. import rule as rule_model
from .. import entities
from ....core import entities as core_entities
from ....platform.types import message as platform_message


@rule_model.rule_class('prefix')
class PrefixRule(rule_model.GroupRespondRule):
    async def match(
        self,
        message_text: str,
        message_chain: platform_message.MessageChain,
        rule_dict: dict,
        query: core_entities.Query,
    ) -> entities.RuleJudgeResult:
        # 🔥 修复：检查prefix规则是否启用
        # 如果配置中没有明确启用prefix规则，则不匹配
        if not rule_dict.get('prefix-enabled', False):
            return entities.RuleJudgeResult(matching=False, replacement=message_chain)

        prefixes = rule_dict.get('prefix', [])
        if not prefixes:
            return entities.RuleJudgeResult(matching=False, replacement=message_chain)

        for prefix in prefixes:
            if message_text.startswith(prefix):
                # 查找第一个plain元素
                for me in message_chain:
                    if isinstance(me, platform_message.Plain):
                        me.text = me.text[len(prefix) :]

                return entities.RuleJudgeResult(
                    matching=True,
                    replacement=message_chain,
                )

        return entities.RuleJudgeResult(matching=False, replacement=message_chain)
