from __future__ import annotations

import re

from ...core import entities as core_entities
from . import handler
from .handlers import chat, command
from .. import entities
from .. import stage


@stage.stage_class('MessageProcessor')
class Processor(stage.PipelineStage):
    """请求实际处理阶段

    通过命令处理器和聊天处理器处理消息。

    改写：
        - resp_messages
    """

    cmd_handler: handler.MessageHandler

    chat_handler: handler.MessageHandler

    async def initialize(self, pipeline_config: dict):
        self.cmd_handler = command.CommandHandler(self.ap)
        self.chat_handler = chat.ChatMessageHandler(self.ap)

        await self.cmd_handler.initialize()
        await self.chat_handler.initialize()

    async def process(
        self,
        query: core_entities.Query,
        stage_inst_name: str,
    ) -> entities.StageProcessResult:
        """处理"""
        # 获取完整消息文本用于命令检查
        full_message_text = str(query.message_chain).strip()
        # 获取简化消息文本用于日志显示
        simplified_message_text = self._get_simplified_message_text(query.message_chain)

        self.ap.logger.info(
            f'处理 {query.launcher_type.value}_{query.launcher_id} 的请求({query.query_id}): {simplified_message_text}'
        )

        async def generator():
            cmd_prefix = self.ap.instance_config.data['command']['prefix']

            if any(full_message_text.startswith(prefix) for prefix in cmd_prefix):
                async for result in self.cmd_handler.handle(query):
                    yield result
            else:
                async for result in self.chat_handler.handle(query):
                    yield result

        return generator()

    def _get_simplified_message_text(self, message_chain) -> str:
        """获取简化的消息文本用于日志显示"""
        try:
            full_text = str(message_chain).strip()

            # 如果消息很短，直接返回
            if len(full_text) <= 200:  # 🔧 增加到200字符以便调试civitai参数
                return full_text

            # 检查是否包含XML数据（appmsg等）
            if '<appmsg' in full_text or '<msg>' in full_text:
                # 尝试提取关键信息
                if 'aigen' in full_text:
                    # 提取aigen命令和简短描述
                    lines = full_text.split('\n')
                    for line in lines:
                        if 'aigen' in line and '<title>' in line:
                            # 提取title标签内容
                            title_match = re.search(r'<title>(.*?)</title>', line)
                            if title_match:
                                title_content = title_match.group(1)
                                return f"aigen命令: {title_content[:80]}{'...' if len(title_content) > 80 else ''}"

                    # 如果包含aigen但没有找到title，尝试直接搜索
                    title_match = re.search(r'<title>(.*?aigen.*?)</title>', full_text, re.DOTALL)
                    if title_match:
                        title_content = title_match.group(1).strip()
                        return f"aigen命令: {title_content[:80]}{'...' if len(title_content) > 80 else ''}"

                # 如果是其他XML消息，显示消息类型
                if '<appmsg' in full_text:
                    return "[引用消息/复合消息]"
                elif '<msg>' in full_text:
                    return "[XML消息]"

            # 对于普通长文本，截断显示
            return full_text[:200] + "..." if len(full_text) > 200 else full_text  # 🔧 增加到200字符

        except Exception as e:
            return f"[消息解析失败: {str(e)[:50]}]"
