"""
[二次开发] ComfyUI适配器
用于与ComfyUI进行交互的适配器模块

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：提供ComfyUI API交互的适配器接口
- 维护者：开发团队
- 最后更新：2025-07-15
- 相关任务：ComfyUI集成系统
- 依赖关系：独立的适配器模块
"""

import requests
import json
import os
from typing import Dict, Any, Optional

class ComfyUIAdapter:
    """ComfyUI适配器，用于与ComfyUI进行交互"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化ComfyUI适配器
        :param config: 配置信息，包含ComfyUI的API地址等
        """
        self.api_url = config.get("api_url", "http://127.0.0.1:8188")
        self.client_id = self._get_client_id()
        
    def _get_client_id(self) -> str:
        """获取ComfyUI客户端ID"""
        response = requests.get(f"{self.api_url}/prompt")
        return response.json().get("client_id", "")
    
    def execute_workflow(self, workflow_file: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行ComfyUI工作流
        :param workflow_file: 工作流JSON文件路径
        :param params: 工作流参数
        :return: 执行结果
        """
        # 加载工作流文件
        with open(workflow_file, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
            
        # 更新工作流参数
        for node_id, node_params in params.items():
            if node_id in workflow:
                for param_name, param_value in node_params.items():
                    if param_name in workflow[node_id]["inputs"]:
                        workflow[node_id]["inputs"][param_name] = param_value
        
        # 发送工作流到ComfyUI
        response = requests.post(
            f"{self.api_url}/prompt",
            json={"prompt": workflow, "client_id": self.client_id}
        )
        
        return response.json()
    
    def get_workflow_status(self, prompt_id: str) -> Dict[str, Any]:
        """
        获取工作流执行状态
        :param prompt_id: 工作流ID
        :return: 执行状态
        """
        response = requests.get(f"{self.api_url}/history/{prompt_id}")
        return response.json()
    
    def get_image(self, filename: str, folder_type: str = "output") -> Optional[bytes]:
        """
        获取生成的图片
        :param filename: 文件名
        :param folder_type: 文件夹类型
        :return: 图片二进制数据
        """
        response = requests.get(f"{self.api_url}/view?filename={filename}&type={folder_type}")
        if response.status_code == 200:
            return response.content
        return None