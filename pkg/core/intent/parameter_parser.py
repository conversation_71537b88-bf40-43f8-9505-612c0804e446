"""
[二次开发] 用户输入参数解析器
支持解析用户输入中的特殊参数，如 --civitai, --seed, --lora 等

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：解析用户输入中的命令行风格参数
- 维护者：开发团队
- 最后更新：2025-07-15
- 相关任务：参数解析系统重构
- 依赖关系：被workflow/unified_parameter_service.py依赖
"""

import re
from typing import Dict, Any, Tuple, List, Optional
from dataclasses import dataclass


@dataclass
class ParsedParameters:
    """解析后的参数"""
    clean_prompt: str           # 清理后的提示词（移除参数）
    use_civitai: bool = False   # 是否使用Civitai搜索
    civitai_query: str = ""     # Civitai搜索关键词
    seed: Optional[int] = None  # 指定种子
    lora_names: List[str] = None  # 指定的LoRA模型名称
    lora_weights: Dict[str, float] = None  # LoRA权重
    lora_prefer_keywords: List[str] = None  # 优先选择的关键词 (--lora+)
    lora_exclude_keywords: List[str] = None  # 排除的关键词 (--lora-)
    quality: str = "standard"   # 质量级别：low, standard, high
    style: str = ""             # 风格参数
    aspect_ratio: str = ""      # 纵横比
    steps: Optional[int] = None # 生成步数
    cfg_scale: Optional[float] = None  # CFG引导强度
    batch_size: int = 1         # 批次大小，默认为1
    use_again: bool = False     # 是否使用上一次的工作流
    no_trans: bool = False      # 是否禁用提示词润色，仅简单翻译
    no_lora: bool = False       # 是否强制不使用任何LoRA模型
    show_help: bool = False     # 是否显示帮助信息
    help_type: str = "full"     # 帮助类型：full, brief, workflow
    raw_parameters: Dict[str, Any] = None  # 原始参数字典


class ParameterParser:
    """参数解析器"""
    
    def __init__(self):
        # 参数模式定义
        self.parameter_patterns = {
            'civitai': r'--civitai(?:\s+([^-]+?))?(?=\s+--|$)',  # --civitai 或 --civitai Asuka Langley Soryu
            'seed': r'--seed\s+(\d+)',
            'lora': r'--lora\s+([^\s-]+)(?:\s+(\d*\.?\d+))?',  # --lora model_name [weight]
            'lora_prefer': r'--lora\+([^\s]+)',  # --lora+keyword
            'lora_exclude': r'--lora-([^\s]+)',  # --lora-keyword
            'quality': r'--quality\s+(low|standard|high)',
            'style': r'--style\s+([^\s-]+)',
            'aspect_ratio': r'--(?:ar|aspect-ratio)\s+(\d+:\d+)',
            'steps': r'--steps\s+(\d+)',
            'cfg': r'--cfg\s+(\d*\.?\d+)',
            'batch': r'--batch\s+(\d+)',  # --batch N 批次大小
            'again': r'--again(?=\s|$)',  # --again 重新生成上一次的工作流
            'repeat': r'--repeat(?=\s|$)',  # --repeat 重新生成上一次的工作流
            'retry': r'--retry(?=\s|$)',    # --retry 重新生成上一次的工作流
            'no_trans': r'--no-trans(?=\s|$)',  # --no-trans 禁用提示词润色，仅简单翻译

            'help': r'--help(?=\s|$)',      # --help 显示完整帮助信息
            'help_brief': r'--h(?=\s|$)',   # --h 显示简化帮助信息
        }
    
    def parse_user_input(self, user_input: str) -> ParsedParameters:
        """
        解析用户输入，提取参数和清理后的提示词
        
        Args:
            user_input: 用户原始输入
            
        Returns:
            ParsedParameters: 解析结果
        """
        if not user_input:
            return ParsedParameters(clean_prompt="")
        
        # 初始化结果
        params = ParsedParameters(
            clean_prompt=user_input,
            lora_names=[],
            lora_weights={},
            lora_prefer_keywords=[],
            lora_exclude_keywords=[],
            raw_parameters={}
        )
        
        # 解析各种参数
        remaining_text = user_input

        # 0. 解析 --again/--repeat/--retry 参数（优先检查）
        for again_keyword in ['again', 'repeat', 'retry']:
            again_match = re.search(self.parameter_patterns[again_keyword], remaining_text, re.IGNORECASE)
            if again_match:
                params.use_again = True
                params.raw_parameters[again_keyword] = True
                # 🔥 修复：保留again参数用于显示，不从remaining_text中移除
                break  # 只匹配第一个找到的关键词

        # 0.1 解析 --no-trans 参数
        no_trans_match = re.search(self.parameter_patterns['no_trans'], remaining_text, re.IGNORECASE)
        if no_trans_match:
            params.no_trans = True
            params.raw_parameters['no_trans'] = True
            remaining_text = re.sub(self.parameter_patterns['no_trans'], '', remaining_text, flags=re.IGNORECASE)





        # 0.2 解析 --help 和 --h 参数（优先检查）
        help_match = re.search(self.parameter_patterns['help'], remaining_text, re.IGNORECASE)
        help_brief_match = re.search(self.parameter_patterns['help_brief'], remaining_text, re.IGNORECASE)

        if help_match:
            params.show_help = True
            params.help_type = "full"
            params.raw_parameters['help'] = True
            # 保留help参数用于显示，不从remaining_text中移除
        elif help_brief_match:
            params.show_help = True
            params.help_type = "brief"
            params.raw_parameters['help_brief'] = True
            # 保留help参数用于显示，不从remaining_text中移除

        # 1. 解析 --civitai 参数
        civitai_match = re.search(self.parameter_patterns['civitai'], remaining_text, re.IGNORECASE)
        if civitai_match:
            params.use_civitai = True
            civitai_query = civitai_match.group(1) or ""
            # 清理civitai查询：去除首尾空格和引号
            civitai_query = civitai_query.strip().strip('"\'')
            params.civitai_query = civitai_query
            params.raw_parameters['civitai'] = params.civitai_query
            remaining_text = re.sub(self.parameter_patterns['civitai'], '', remaining_text, flags=re.IGNORECASE)
        
        # 2. 解析 --seed 参数
        seed_match = re.search(self.parameter_patterns['seed'], remaining_text, re.IGNORECASE)
        if seed_match:
            try:
                params.seed = int(seed_match.group(1))
                params.raw_parameters['seed'] = params.seed
                remaining_text = re.sub(self.parameter_patterns['seed'], '', remaining_text, flags=re.IGNORECASE)
            except ValueError:
                pass
        
        # 3. 解析 --lora 参数（可能有多个）
        lora_matches = re.finditer(self.parameter_patterns['lora'], remaining_text, re.IGNORECASE)
        for match in lora_matches:
            lora_name = match.group(1)

            # 检查是否是 --lora no（禁用LoRA）
            if lora_name.lower() == 'no':
                params.no_lora = True
                params.raw_parameters['no_lora'] = True
                continue

            lora_weight = float(match.group(2)) if match.group(2) else 0.8
            params.lora_names.append(lora_name)
            params.lora_weights[lora_name] = lora_weight

        if params.lora_names or params.no_lora:
            if params.lora_names:
                params.raw_parameters['lora'] = params.lora_weights
            remaining_text = re.sub(self.parameter_patterns['lora'], '', remaining_text, flags=re.IGNORECASE)



        # 3.1 解析 --lora+ 参数（可能有多个）
        prefer_matches = re.finditer(self.parameter_patterns['lora_prefer'], remaining_text, re.IGNORECASE)
        for match in prefer_matches:
            keyword = match.group(1)
            params.lora_prefer_keywords.append(keyword)

        if params.lora_prefer_keywords:
            params.raw_parameters['lora_prefer'] = params.lora_prefer_keywords
            remaining_text = re.sub(self.parameter_patterns['lora_prefer'], '', remaining_text, flags=re.IGNORECASE)

        # 3.2 解析 --lora- 参数（可能有多个）
        exclude_matches = re.finditer(self.parameter_patterns['lora_exclude'], remaining_text, re.IGNORECASE)
        for match in exclude_matches:
            keyword = match.group(1)
            params.lora_exclude_keywords.append(keyword)

        if params.lora_exclude_keywords:
            params.raw_parameters['lora_exclude'] = params.lora_exclude_keywords
            remaining_text = re.sub(self.parameter_patterns['lora_exclude'], '', remaining_text, flags=re.IGNORECASE)

        # 4. 解析 --quality 参数
        quality_match = re.search(self.parameter_patterns['quality'], remaining_text, re.IGNORECASE)
        if quality_match:
            params.quality = quality_match.group(1).lower()
            params.raw_parameters['quality'] = params.quality
            remaining_text = re.sub(self.parameter_patterns['quality'], '', remaining_text, flags=re.IGNORECASE)
        
        # 5. 解析 --style 参数
        style_match = re.search(self.parameter_patterns['style'], remaining_text, re.IGNORECASE)
        if style_match:
            params.style = style_match.group(1)
            params.raw_parameters['style'] = params.style
            remaining_text = re.sub(self.parameter_patterns['style'], '', remaining_text, flags=re.IGNORECASE)
        
        # 6. 解析 --aspect-ratio 参数
        ar_match = re.search(self.parameter_patterns['aspect_ratio'], remaining_text, re.IGNORECASE)
        if ar_match:
            params.aspect_ratio = ar_match.group(1)
            params.raw_parameters['aspect_ratio'] = params.aspect_ratio
            remaining_text = re.sub(self.parameter_patterns['aspect_ratio'], '', remaining_text, flags=re.IGNORECASE)
        
        # 7. 解析 --steps 参数
        steps_match = re.search(self.parameter_patterns['steps'], remaining_text, re.IGNORECASE)
        if steps_match:
            try:
                params.steps = int(steps_match.group(1))
                params.raw_parameters['steps'] = params.steps
                remaining_text = re.sub(self.parameter_patterns['steps'], '', remaining_text, flags=re.IGNORECASE)
            except ValueError:
                pass
        
        # 8. 解析 --cfg 参数
        cfg_match = re.search(self.parameter_patterns['cfg'], remaining_text, re.IGNORECASE)
        if cfg_match:
            try:
                params.cfg_scale = float(cfg_match.group(1))
                params.raw_parameters['cfg_scale'] = params.cfg_scale
                remaining_text = re.sub(self.parameter_patterns['cfg'], '', remaining_text, flags=re.IGNORECASE)
            except ValueError:
                pass

        # 9. 解析 --batch 参数
        batch_match = re.search(self.parameter_patterns['batch'], remaining_text, re.IGNORECASE)
        if batch_match:
            try:
                batch_size = int(batch_match.group(1))
                # 限制batch_size在1-4之间
                if 1 <= batch_size <= 4:
                    params.batch_size = batch_size
                    params.raw_parameters['batch_size'] = params.batch_size
                else:
                    # 超出范围时使用默认值1
                    params.batch_size = 1
                    params.raw_parameters['batch_size'] = params.batch_size
                remaining_text = re.sub(self.parameter_patterns['batch'], '', remaining_text, flags=re.IGNORECASE)
            except ValueError:
                pass
        
        # 清理剩余文本
        params.clean_prompt = self._clean_text(remaining_text)
        
        return params
    
    def _clean_text(self, text: str) -> str:
        """清理文本，移除多余的空格和换行"""
        if not text:
            return ""
        
        # 移除多余的空格
        cleaned = re.sub(r'\s+', ' ', text)
        
        # 移除首尾空格
        cleaned = cleaned.strip()
        
        return cleaned
    
    def format_parameters_help(self) -> str:
        """格式化全局参数帮助信息"""
        help_text = """
🎛️ 支持的参数

📦 Civitai集成
  --civitai 关键词
  从Civitai平台搜索相关模型

🎯 模型控制
  --seed 12345
  指定生成图片使用的随机种子
  --lora 模型名
  指定要使用的特定LoRA模型

⚙️ 质量控制
  --quality high
  设置图片生成的质量和精度等级
  --steps 30
  设置生成过程的详细迭代步数
  --cfg 7.5
  设置CFG引导强度的参数值
  --batch 2
  设置批次大小，一次生成多张不同图片（1-4张）

🎨 样式控制
  --style realistic
  设置生成图片的整体艺术风格
  --ar 16:9
  设置生成图片的宽高纵横比例

⚡ 快速操作
  --again
  重新生成上一次的图片和参数

🚫 特殊控制
  --no-trans
  禁用提示词润色仅简单翻译

💡 使用示例
  基础生成
  aigen 生成一只可爱的小猫

  高质量生成
  aigen 现代建筑设计风格
    --quality high --steps 30

  指定LoRA模型
  aigen 人像摄影作品风格
    --lora portrait --civitai cat

  重新生成
  aigen --again
        """
        return help_text.strip()

    def format_brief_help(self) -> str:
        """格式化简化帮助信息"""
        help_text = """
🎛️ 常用参数

  --seed 12345
  指定生成图片使用的随机种子
  --quality high
  设置图片生成的质量和精度等级
  --again
  重新生成上一次的图片和参数
  --help
  显示完整的参数帮助和使用说明

💡 示例
  aigen 生成一只猫
        """
        return help_text.strip()

    def format_workflow_help(self, workflow_type: str) -> str:
        """格式化工作流特定帮助信息"""
        if workflow_type.lower() == "aigen":
            return self._format_aigen_help()
        elif workflow_type.lower() in ["kontext", "kontext_api"]:
            return self._format_kontext_help()
        else:
            return self.format_parameters_help()

    def _format_aigen_help(self) -> str:
        """格式化Aigen工作流帮助"""
        help_text = """
🎨 Aigen工作流帮助

📋 工作流类型
  • 纯文生图
  无需上传任何图片直接生成
  • 控制图+文生图
  上传草图线稿等作为控制图
  • 参考图+文生图
  上传风格参考图进行风格生成
  • 混合模式
  同时使用控制图和风格参考图

⚙️ 常用参数
  --seed 12345
  指定生成图片使用的随机种子
  --quality high
  设置图片生成的质量和精度等级
  --lora 模型名
  指定要使用的特定LoRA模型
  --civitai 关键词
  从Civitai平台搜索相关模型
  --again
  重新生成上一次的图片和参数

💡 使用示例
  基础生成
  aigen 生成一只猫咪

  高质量生成
  aigen 现代建筑设计
    --quality high

  使用LoRA
  aigen 建筑效果图
    --lora+比鲁斯

  参考图生成
  [上传参考图]
  aigen 以这张图为参考

💡 提示
  上传图片后系统会
  自动选择合适工作流
        """
        return help_text.strip()

    def _format_kontext_help(self) -> str:
        """格式化Kontext工作流帮助"""
        help_text = """
🖼️ Kontext工作流帮助

📋 工作流类型
  • 单图编辑
  上传1张图片进行风格转换处理
  • 双图编辑
  上传2张图片进行融合对比处理
  • 多图编辑
  上传3张图片进行复杂多元处理

⚙️ 常用参数
  --seed 12345
  指定生成图片使用的随机种子
  --quality high
  设置图片生成的质量和精度等级
  --ar 3:2
  设置生成图片的宽高纵横比例
  --again
  重新生成上一次的图片和参数

🎨 LoRA模型控制
  --lora+关键词
  优先选择包含该关键词的LoRA模型

  --lora-关键词
  排除包含该关键词的LoRA模型

  --lora 模型名 [权重]
  指定使用特定LoRA模型

  --lora no
  强制不使用任何LoRA模型

💡 使用示例
  单图编辑
  [上传1张图]
  kontext 转换为油画风格

  双图编辑
  [上传2张图]
  kontext 将第一张图的
  风格应用到第二张图

  多图编辑
  [上传3张图]
  kontext 融合这三张图
  的元素创建新图片

  云端API
  kontext_api 处理图片
    --quality high

💡 提示
  必须先上传图片
  系统根据图片数量
  自动选择工作流
        """
        return help_text.strip()


# 全局参数解析器实例
parameter_parser = ParameterParser()
