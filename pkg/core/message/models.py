"""
[二次开发] 消息处理数据模型
统一的消息数据结构和枚举定义

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：定义消息处理相关的数据模型和枚举类型
- 维护者：开发团队
- 最后更新：2025-07-15
- 相关任务：消息处理系统重构
- 依赖关系：被message/processor.py和message/sender.py依赖
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, Any, List, Optional, Union
import time


class MessageType(Enum):
    """消息类型枚举"""
    TEXT = "text"                      # 纯文本消息
    IMAGE = "image"                    # 纯图片消息
    MIXED = "mixed"                    # 混合消息（文本+图片）
    PROGRESS = "progress"              # 进度消息
    ERROR = "error"                    # 错误消息
    SUCCESS = "success"                # 成功消息
    HELP = "help"                      # 帮助消息
    STATUS = "status"                  # 状态消息


class MessageFormat(Enum):
    """消息格式枚举"""
    PLAIN = "plain"                    # 纯文本格式
    MARKDOWN = "markdown"              # Markdown格式
    HTML = "html"                      # HTML格式
    JSON = "json"                      # JSON格式


class ProcessAction(Enum):
    """处理动作枚举"""
    CONTINUE = "continue"              # 继续处理
    CANCEL = "cancel"                  # 取消会话
    EXECUTE = "execute"                # 执行工作流
    IMAGE_ADDED = "image_added"        # 图片已添加
    PROMPT_UPDATED = "prompt_updated"  # 提示词已更新
    SESSION_CREATED = "session_created" # 会话已创建
    ERROR = "error"                    # 处理错误
    TIMEOUT = "timeout"                # 会话超时
    ROUTE_DECIDED = "route_decided"    # 路由已决策


@dataclass
class MessageContent:
    """消息内容"""
    text: str = ""
    images: List[bytes] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def has_text(self) -> bool:
        """是否包含文本"""
        return bool(self.text.strip())
    
    def has_images(self) -> bool:
        """是否包含图片"""
        return len(self.images) > 0
    
    def is_empty(self) -> bool:
        """是否为空消息"""
        return not self.has_text() and not self.has_images()
    
    def get_type(self) -> MessageType:
        """获取消息类型"""
        if self.has_text() and self.has_images():
            return MessageType.MIXED
        elif self.has_text():
            return MessageType.TEXT
        elif self.has_images():
            return MessageType.IMAGE
        else:
            return MessageType.TEXT  # 默认文本类型


@dataclass
class ProcessContext:
    """消息处理上下文"""
    user_id: str
    chat_id: str = ""
    session_id: str = ""
    
    # 原始输入
    raw_message: Any = None  # 原始查询对象
    user_input: MessageContent = field(default_factory=MessageContent)
    
    # 处理结果
    processed_input: MessageContent = field(default_factory=MessageContent)
    
    # 会话信息
    has_active_session: bool = False
    session_data: Dict[str, Any] = field(default_factory=dict)
    
    # 路由信息
    routing_decision: Optional[Any] = None  # RoutingDecision类型，避免循环导入
    
    # 元数据
    timestamp: float = field(default_factory=time.time)
    processing_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ProcessResult:
    """消息处理结果"""
    success: bool
    action: ProcessAction
    message: str = ""
    
    # 处理后的内容
    processed_content: MessageContent = field(default_factory=MessageContent)
    
    # 会话信息
    session_updated: bool = False
    session_data: Dict[str, Any] = field(default_factory=dict)
    
    # 路由信息
    workflow_selected: bool = False
    workflow_type: str = ""
    environment: str = ""
    
    # 建议操作
    suggested_actions: List[str] = field(default_factory=list)
    next_step: str = ""
    
    # 错误信息
    error_code: str = ""
    error_details: str = ""
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    
    def is_execution_ready(self) -> bool:
        """是否准备执行工作流"""
        return (self.action == ProcessAction.EXECUTE and 
                self.workflow_selected and 
                self.session_updated)
    
    def needs_user_input(self) -> bool:
        """是否需要用户输入"""
        return self.action in [
            ProcessAction.CONTINUE,
            ProcessAction.IMAGE_ADDED,
            ProcessAction.PROMPT_UPDATED,
            ProcessAction.SESSION_CREATED
        ]
    
    def has_error(self) -> bool:
        """是否有错误"""
        return self.action in [ProcessAction.ERROR, ProcessAction.TIMEOUT]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'success': self.success,
            'action': self.action.value,
            'message': self.message,
            'session_updated': self.session_updated,
            'workflow_selected': self.workflow_selected,
            'workflow_type': self.workflow_type,
            'environment': self.environment,
            'suggested_actions': self.suggested_actions,
            'next_step': self.next_step,
            'error_code': self.error_code,
            'metadata': self.metadata,
            'timestamp': self.timestamp
        }


@dataclass
class MessageOptions:
    """消息选项"""
    format: MessageFormat = MessageFormat.PLAIN
    role: str = "assistant"
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout: float = 30.0
    
    # 图片选项
    image_quality: str = "high"         # high, medium, low
    image_format: str = "png"           # png, jpg, webp
    max_image_size: int = 10 * 1024 * 1024  # 10MB
    
    # 文本选项
    max_text_length: int = 4000
    truncate_long_text: bool = True
    
    # 显示选项
    show_metadata: bool = False
    show_timestamp: bool = False
    show_confidence: bool = True
    
    def validate(self) -> bool:
        """验证选项有效性"""
        return (self.max_retries > 0 and 
                self.retry_delay >= 0 and
                self.timeout > 0 and
                self.max_image_size > 0 and
                self.max_text_length > 0) 