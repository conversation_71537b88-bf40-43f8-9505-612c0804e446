"""
[二次开发] 核心消息处理模块
提供统一的消息处理和发送接口，支持所有工作流类型

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：消息处理模块的统一导出接口
- 维护者：开发团队
- 最后更新：2025-07-15
- 相关任务：消息处理系统重构
- 依赖关系：导出消息处理模块的核心接口
"""

from .processor import MessageProcessor, message_processor
from .sender import MessageSender, message_sender
from .models import (
    MessageType,
    MessageContent,
    ProcessResult,
    ProcessContext,
    MessageFormat
)

__all__ = [
    'MessageProcessor',
    'message_processor',
    'MessageSender', 
    'message_sender',
    'MessageType',
    'MessageContent',
    'ProcessResult',
    'ProcessContext',
    'MessageFormat'
] 