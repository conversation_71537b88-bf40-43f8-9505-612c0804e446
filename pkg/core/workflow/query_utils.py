"""
[二次开发] Query对象工具函数
提供安全的Query对象属性操作，避免修改官方代码

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：提供Query对象的安全属性操作
- 维护者：开发团队
- 最后更新：2025-07-14
- 相关任务：修复Query对象ap属性问题
"""

from typing import Any, Optional
import logging

def safe_set_query_ap(query: Any, ap: Any, logger: Optional[logging.Logger] = None) -> bool:
    """
    安全地为Query对象设置ap属性
    
    Args:
        query: Query对象
        ap: Application实例
        logger: 日志记录器
        
    Returns:
        bool: 是否成功设置
    """
    if not query or not ap:
        if logger:
            logger.warning("🔍 query或ap参数为空，无法设置ap属性")
        return False
    
    try:
        # 检查是否已经有ap属性
        if hasattr(query, 'ap') and query.ap:
            if logger:
                logger.info("✅ query对象已有ap属性")
            return True
        
        # 尝试使用__dict__直接设置，绕过Pydantic验证
        if hasattr(query, '__dict__'):
            query.__dict__['ap'] = ap
            if logger:
                logger.info("✅ 已通过__dict__设置ap属性")
            return True
        
        # 如果__dict__不可用，尝试setattr
        try:
            setattr(query, 'ap', ap)
            if logger:
                logger.info("✅ 已通过setattr设置ap属性")
            return True
        except Exception as e:
            if logger:
                logger.warning(f"⚠️ setattr设置ap属性失败: {e}")
            return False
            
    except Exception as e:
        if logger:
            logger.error(f"❌ 设置ap属性时发生错误: {e}")
        return False

def safe_get_query_ap(query: Any, logger: Optional[logging.Logger] = None) -> Optional[Any]:
    """
    安全地获取Query对象的ap属性
    
    Args:
        query: Query对象
        logger: 日志记录器
        
    Returns:
        Application实例或None
    """
    if not query:
        if logger:
            logger.warning("🔍 query参数为空")
        return None
    
    try:
        # 尝试直接访问
        if hasattr(query, 'ap'):
            return query.ap
        
        # 尝试从__dict__获取
        if hasattr(query, '__dict__') and 'ap' in query.__dict__:
            return query.__dict__['ap']
        
        if logger:
            logger.warning("🔍 query对象没有ap属性")
        return None
        
    except Exception as e:
        if logger:
            logger.error(f"❌ 获取ap属性时发生错误: {e}")
        return None

def ensure_query_has_ap(query: Any, ap: Any, logger: Optional[logging.Logger] = None) -> bool:
    """
    确保Query对象有ap属性，如果没有则设置
    
    Args:
        query: Query对象
        ap: Application实例
        logger: 日志记录器
        
    Returns:
        bool: 是否成功确保ap属性存在
    """
    if not query or not ap:
        return False
    
    # 检查是否已经有ap属性
    existing_ap = safe_get_query_ap(query, logger)
    if existing_ap:
        return True
    
    # 设置ap属性
    return safe_set_query_ap(query, ap, logger)
