"""
[二次开发] 统一图片处理器
整合所有工作流类型的图片处理功能

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：提供统一的图片处理接口，整合多种图片处理功能
- 维护者：开发团队
- 最后更新：2025-07-15
- 相关任务：图片处理系统重构
- 依赖关系：依赖image/utils.py和image/analyzer.py
"""

import base64
import xml.etree.ElementTree as ET
from typing import List, Optional, Tuple, Dict, Any
from .utils import decode_base64_image, validate_image, get_image_hash
from .analyzer import ImageAnalyzer, ImageInfo


class ImageProcessor:
    """统一图片处理器"""

    def __init__(self, logger=None, app=None):
        self.logger = logger
        self.app = app
        self.analyzer = ImageAnalyzer(logger)
    
    def _log(self, message: str, level: str = "info"):
        """日志记录"""
        if self.logger:
            if level == "error":
                self.logger.error(f"[ImageProcessor] {message}")
            elif level == "warning":
                self.logger.warning(f"[ImageProcessor] {message}")
            else:
                self.logger.info(f"[ImageProcessor] {message}")
    
    async def extract_user_images(self, query) -> List[bytes]:
        """
        提取用户消息中的图片数据
        
        Args:
            query: 查询对象
            
        Returns:
            图片数据列表
        """
        images = []
        try:
            # 提取直接消息中的图片
            direct_images = await self._extract_direct_images(query)
            images.extend(direct_images)
            
            # 提取引用消息中的图片
            quoted_images = await self.extract_quoted_images(query)
            images.extend(quoted_images)
            
            # 去重
            images = self._deduplicate_images(images)
            
            self._log(f"提取到 {len(images)} 张图片")
        except Exception as e:
            self._log(f"提取图片数据失败: {e}", "error")
        
        return images
    
    async def _extract_direct_images(self, query) -> List[bytes]:
        """提取直接消息中的图片"""
        images = []
        try:
            if (hasattr(query, 'user_message') and 
                query.user_message and 
                hasattr(query.user_message, 'content') and 
                query.user_message.content):
                
                if isinstance(query.user_message.content, list):
                    for content in query.user_message.content:
                        if hasattr(content, 'type') and content.type in ['image', 'image_base64', 'image_url']:
                            image_data = await self._extract_image_from_content(content)
                            if image_data:
                                images.append(image_data)
        except Exception as e:
            self._log(f"提取直接图片失败: {e}", "error")
        
        return images
    
    async def extract_quoted_images(self, query) -> List[bytes]:
        """
        从引用消息中提取图片
        
        Args:
            query: 查询对象
            
        Returns:
            引用图片数据列表
        """
        images = []
        try:
            # 获取消息链
            message_chain = getattr(query, 'message_chain', None)
            if not message_chain:
                self._log("消息链为空，无法提取引用图片")
                return images
            
            # 查找Quote组件
            for component in message_chain:
                if self._is_quote_component(component):
                    quote_images = await self._extract_images_from_quote(component, query)
                    images.extend(quote_images)
            
        except Exception as e:
            self._log(f"提取引用消息图片失败: {e}", "error")
        
        self._log(f"从引用消息提取到 {len(images)} 张图片")
        return images
    
    def _is_quote_component(self, component) -> bool:
        """检查是否是引用组件"""
        return (hasattr(component, 'origin') and 
                hasattr(component, '__class__') and 
                'Quote' in component.__class__.__name__)
    
    async def _extract_images_from_quote(self, quote_component, query=None) -> List[bytes]:
        """从引用组件中提取图片"""
        images = []

        if not hasattr(quote_component, 'origin'):
            return images

        self._log(f"分析引用消息，原始消息链长度: {len(quote_component.origin)}")

        for i, quote_item in enumerate(quote_component.origin):
            self._log(f"引用消息项[{i}]类型: {getattr(quote_item, 'type', 'unknown')}, 类名: {type(quote_item).__name__}")

            # 检查是否是图片类型
            if self._is_image_component(quote_item):
                image_data = await self._extract_image_from_component(quote_item)
                if image_data:
                    images.append(image_data)

            # 处理Unknown组件，可能包含图片XML数据
            elif self._is_unknown_component(quote_item):
                xml_images = await self._extract_images_from_unknown_component(quote_item, query)
                images.extend(xml_images)

        return images
    
    def _is_image_component(self, component) -> bool:
        """检查是否是图片组件"""
        return (hasattr(component, 'get_bytes') and 
                hasattr(component, '__class__') and 
                'Image' in component.__class__.__name__)
    
    def _is_unknown_component(self, component) -> bool:
        """检查是否是Unknown组件"""
        return (hasattr(component, '__class__') and 
                'Unknown' in component.__class__.__name__)
    
    async def _extract_image_from_component(self, component) -> Optional[bytes]:
        """从图片组件中提取图片数据"""
        try:
            self._log("开始提取引用消息中的图片数据...")
            image_bytes, mime_type = await component.get_bytes()
            self._log(f"从引用消息提取到图片，大小: {len(image_bytes)}, 类型: {mime_type}")
            return image_bytes
        except Exception as e:
            self._log(f"提取引用消息图片失败: {e}", "error")
            
            # 尝试其他方式获取图片数据
            try:
                if hasattr(component, 'url') and component.url:
                    self._log(f"尝试从URL获取图片: {component.url}")
                    # 这里可以添加从URL下载图片的逻辑
                    # return await self._download_image_from_url(component.url)
            except Exception as url_e:
                self._log(f"从URL获取图片也失败: {url_e}", "error")
        
        return None
    
    async def _extract_images_from_unknown_component(self, component, query=None) -> List[bytes]:
        """从Unknown组件中提取图片XML数据"""
        images = []

        try:
            unknown_text = str(component)
            if "img" not in unknown_text or "aeskey" not in unknown_text:
                self._log(f"Unknown组件不包含图片数据: {unknown_text[:100]}...")
                return images

            self._log("发现Unknown组件，尝试解析图片数据...")

            # 查找XML数据
            if "<?xml" not in unknown_text:
                return images

            # 提取XML部分
            xml_start = unknown_text.find("<?xml")
            xml_end = unknown_text.find("</msg>") + 6
            xml_data = unknown_text[xml_start:xml_end]

            self._log(f"提取到XML数据: {xml_data[:200]}...")

            # 解析XML
            root = ET.fromstring(xml_data)
            img_tag = root.find('img')

            if img_tag is not None:
                aeskey = img_tag.get('aeskey')
                cdnthumburl = img_tag.get('cdnthumburl')

                if aeskey and cdnthumburl:
                    self._log(f"从XML中提取到图片参数: aeskey={aeskey[:8]}..., cdnthumburl={cdnthumburl[:20]}...")

                    # 下载图片
                    image_data = await self._download_wechat_image(aeskey, cdnthumburl, query)
                    if image_data:
                        images.append(image_data)
                else:
                    self._log(f"XML中缺少必要的图片参数: aeskey={aeskey}, cdnthumburl={cdnthumburl}", "error")

        except Exception as e:
            self._log(f"解析Unknown组件中的XML数据失败: {e}", "error")

        return images
    
    async def _download_wechat_image(self, aeskey: str, cdnthumburl: str, query=None) -> Optional[bytes]:
        """下载微信图片"""
        try:
            # 🔥 修复：直接从平台管理器获取wechatpad的bot实例，而不是重新创建
            wechatpad_bot = await self._get_wechatpad_bot(query)
            if not wechatpad_bot:
                self._log("未找到wechatpad bot实例，无法下载引用图片", "error")
                return None

            self._log("找到wechatpad bot实例，开始下载引用图片...")

            # 直接使用现有的bot实例下载图片
            image_data = wechatpad_bot.cdn_download(
                aeskey=aeskey,
                file_type=1,  # 图片类型
                file_url=cdnthumburl
            )

            if image_data["Data"]['FileData'] == '':
                # 如果缩略图下载失败，尝试原图
                image_data = wechatpad_bot.cdn_download(
                    aeskey=aeskey,
                    file_type=2,  # 原图类型
                    file_url=cdnthumburl
                )

            base64_str = image_data["Data"]['FileData']
            if base64_str:
                # 转换为bytes
                image_bytes = base64.b64decode(base64_str)
                self._log(f"✅ 成功下载引用图片，大小: {len(image_bytes)} bytes")
                return image_bytes
            else:
                self._log("wechatpad下载图片返回空数据", "error")

        except Exception as e:
            self._log(f"下载引用图片失败: {e}", "error")

        return None

    async def _get_wechatpad_bot(self, query=None) -> Optional[Any]:
        """
        获取wechatpad bot实例
        🔥 关键修复：直接使用现有的bot实例，而不是重新创建
        """
        try:
            # 🔥 优先方案：从query对象中直接获取adapter
            if query and hasattr(query, 'adapter') and query.adapter:
                adapter = query.adapter
                self._log(f"🔍 [DEBUG] 从query获取到适配器: {type(adapter).__name__}")
                if hasattr(adapter, 'name'):
                    self._log(f"🔍 [DEBUG] 适配器名称: {adapter.name}")

                # 检查是否是wechatpad适配器
                if (hasattr(adapter, 'name') and
                    adapter.name == 'WeChatPad' and
                    hasattr(adapter, 'bot') and adapter.bot):
                    self._log(f"✅ 从query找到wechatpad适配器，bot实例: {type(adapter.bot).__name__}")
                    return adapter.bot
                else:
                    self._log(f"⚠️ query中的适配器不是WeChatPad: {getattr(adapter, 'name', 'unknown')}")

            # 🔥 备用方案：从应用实例的平台管理器中查找
            app = self.app

            # 如果没有直接传入的应用实例，尝试其他方式获取
            if not app:
                # 尝试从全局应用实例获取配置
                from ...core.app import Application
                import asyncio

                # 获取当前事件循环中的应用实例
                loop = asyncio.get_event_loop()
                if hasattr(loop, '_langbot_app'):
                    app = loop._langbot_app
                else:
                    # 尝试从其他地方获取应用实例
                    app = getattr(Application, '_instance', None)

            if app and hasattr(app, 'platform_mgr') and app.platform_mgr:
                self._log(f"🔍 [DEBUG] 找到应用实例和平台管理器，bot数量: {len(app.platform_mgr.bots)}")

                # 遍历所有机器人实例，查找wechatpad适配器
                for i, bot in enumerate(app.platform_mgr.bots):
                    self._log(f"🔍 [DEBUG] Bot {i}: {type(bot).__name__}")
                    if hasattr(bot, 'adapter_inst') and bot.adapter_inst:
                        adapter = bot.adapter_inst
                        self._log(f"🔍 [DEBUG] Bot {i} 适配器: {type(adapter).__name__}")
                        if hasattr(adapter, 'name'):
                            self._log(f"🔍 [DEBUG] Bot {i} 适配器名称: {adapter.name}")

                        # 检查是否是wechatpad适配器
                        if (hasattr(adapter, 'name') and
                            adapter.name == 'WeChatPad' and
                            hasattr(adapter, 'bot') and adapter.bot):
                            self._log(f"找到wechatpad适配器，bot实例: {type(adapter.bot).__name__}")
                            return adapter.bot
                    else:
                        self._log(f"🔍 [DEBUG] Bot {i} 没有adapter_inst属性")

                self._log("未找到wechatpad适配器或bot实例", "warning")
            else:
                if not app:
                    self._log("无法获取应用实例", "warning")
                elif not hasattr(app, 'platform_mgr'):
                    self._log("应用实例没有platform_mgr属性", "warning")
                elif not app.platform_mgr:
                    self._log("platform_mgr为None", "warning")

        except Exception as e:
            self._log(f"获取wechatpad bot实例失败: {e}", "error")

        return None
    
    async def _extract_image_from_content(self, content) -> Optional[bytes]:
        """从消息内容中提取图片数据"""
        try:
            if hasattr(content, 'image_base64') and content.image_base64:
                return decode_base64_image(content.image_base64)
            elif hasattr(content, 'data') and getattr(content, 'data', None):
                return getattr(content, 'data')
        except Exception as e:
            self._log(f"从内容提取图片失败: {e}", "error")
        return None
    
    def _deduplicate_images(self, images: List[bytes]) -> List[bytes]:
        """去除重复图片"""
        if not images:
            return images
        
        unique_images = []
        seen_hashes = set()
        
        for image_data in images:
            if not image_data:
                continue
            
            image_hash = get_image_hash(image_data)
            if image_hash not in seen_hashes:
                seen_hashes.add(image_hash)
                unique_images.append(image_data)
            else:
                self._log(f"跳过重复图片 (哈希: {image_hash[:8]}...)")
        
        if len(unique_images) != len(images):
            self._log(f"去重后图片数量: {len(unique_images)} (原始: {len(images)})")
        
        return unique_images
    
    def extract_quoted_text(self, query) -> str:
        """
        从引用消息中提取文本
        
        Args:
            query: 查询对象
            
        Returns:
            引用文本内容
        """
        try:
            message_chain = getattr(query, 'message_chain', None)
            if not message_chain:
                return ""
            
            for component in message_chain:
                if self._is_quote_component(component) and hasattr(component, 'origin'):
                    # 提取引用消息中的文本
                    text_parts = []
                    for quote_item in component.origin:
                        if (hasattr(quote_item, '__class__') and 
                            'Plain' in quote_item.__class__.__name__ and
                            hasattr(quote_item, 'text')):
                            text_parts.append(quote_item.text)
                    
                    if text_parts:
                        quoted_text = ''.join(text_parts).strip()
                        self._log(f"提取到引用文本: {quoted_text[:50]}...")
                        return quoted_text
        except Exception as e:
            self._log(f"提取引用文本失败: {e}", "error")
        
        return ""
    
    async def handle_quoted_content_for_kontext(self, query, user_text: str) -> Tuple[str, List[bytes]]:
        """
        智能处理引用内容用于Kontext工作流
        
        Args:
            query: 查询对象
            user_text: 用户输入文本
            
        Returns:
            (最终提示词, 引用图片列表)
        """
        # 提取引用文本
        quoted_text = self.extract_quoted_text(query)
        
        # 提取引用图片
        quoted_images = await self.extract_quoted_images(query)
        
        # 构建最终提示词
        final_prompt = user_text
        if quoted_text:
            # 过滤掉图片占位符文本和错误信息
            quoted_text_clean = quoted_text.replace("[图片]", "").replace("[Image]", "").strip()
            
            # 过滤掉明显的错误信息
            if (quoted_text_clean and 
                not quoted_text_clean.startswith("Unknown Message:") and 
                not quoted_text_clean.startswith("[引用消息解析失败")):
                
                self._log(f"检测到引用文本，将合并到提示词中: {quoted_text_clean}")
                
                # 如果用户输入很短（可能只是简单指令），则使用引用文本作为主要提示词
                if len(user_text.strip()) < 20:
                    final_prompt = f"{quoted_text_clean} {user_text}".strip()
                else:
                    final_prompt = f"{user_text} (参考: {quoted_text_clean})"
        
        return final_prompt, quoted_images
    
    def validate_images(self, images: List[bytes], max_size_mb: int = 10) -> Tuple[List[bytes], List[str]]:
        """
        验证图片列表
        
        Args:
            images: 图片数据列表
            max_size_mb: 最大允许大小（MB）
            
        Returns:
            (有效图片列表, 错误信息列表)
        """
        valid_images = []
        errors = []
        
        for i, image_data in enumerate(images):
            is_valid, error_msg = validate_image(image_data, max_size_mb)
            if is_valid:
                valid_images.append(image_data)
            else:
                errors.append(f"第{i+1}张图片: {error_msg}")
        
        return valid_images, errors
    
    def analyze_images(self, images: List[bytes]) -> List[ImageInfo]:
        """
        分析图片列表
        
        Args:
            images: 图片数据列表
            
        Returns:
            图片信息列表
        """
        return self.analyzer.analyze_images(images)
    
    def check_workflow_compatibility(self, images: List[bytes], workflow_type: str) -> Dict[str, Any]:
        """
        检查图片与工作流的兼容性
        
        Args:
            images: 图片数据列表
            workflow_type: 工作流类型
            
        Returns:
            兼容性检查结果
        """
        image_infos = self.analyze_images(images)
        return self.analyzer.check_images_compatibility(image_infos, workflow_type)
    
    def suggest_parameters(self, images: List[bytes], workflow_type: str) -> Dict[str, Any]:
        """
        根据图片分析结果建议参数
        
        Args:
            images: 图片数据列表
            workflow_type: 工作流类型
            
        Returns:
            建议参数字典
        """
        image_infos = self.analyze_images(images)
        return self.analyzer.suggest_parameters(image_infos, workflow_type)


# 全局图片处理器实例 - 延迟初始化
_global_image_processor = None

def get_image_processor(app=None, logger=None):
    """获取全局图片处理器实例"""
    global _global_image_processor
    if _global_image_processor is None:
        _global_image_processor = ImageProcessor(logger=logger, app=app)
    elif app is not None and _global_image_processor.app is None:
        # 如果之前没有应用实例，现在有了，就更新它
        _global_image_processor.app = app
    elif logger is not None and _global_image_processor.logger is None:
        # 如果之前没有日志器，现在有了，就更新它
        _global_image_processor.logger = logger
    return _global_image_processor

# 全局实例，使用延迟初始化函数
image_processor = get_image_processor()