LICENSE
README.md
pyproject.toml
pkg/adapters/comfyui_adapter.py
pkg/adapters/wechat_adapter.py
pkg/api/__init__.py
pkg/api/http/__init__.py
pkg/api/http/controller/__init__.py
pkg/api/http/controller/group.py
pkg/api/http/controller/main.py
pkg/api/http/controller/groups/__init__.py
pkg/api/http/controller/groups/files.py
pkg/api/http/controller/groups/logs.py
pkg/api/http/controller/groups/plugins.py
pkg/api/http/controller/groups/stats.py
pkg/api/http/controller/groups/system.py
pkg/api/http/controller/groups/user.py
pkg/api/http/controller/groups/pipelines/__init__.py
pkg/api/http/controller/groups/pipelines/pipelines.py
pkg/api/http/controller/groups/pipelines/webchat.py
pkg/api/http/controller/groups/platform/__init__.py
pkg/api/http/controller/groups/platform/adapters.py
pkg/api/http/controller/groups/platform/bots.py
pkg/api/http/controller/groups/provider/__init__.py
pkg/api/http/controller/groups/provider/models.py
pkg/api/http/controller/groups/provider/requesters.py
pkg/api/http/service/__init__.py
pkg/api/http/service/bot.py
pkg/api/http/service/model.py
pkg/api/http/service/pipeline.py
pkg/api/http/service/user.py
pkg/command/__init__.py
pkg/command/cmdmgr.py
pkg/command/entities.py
pkg/command/errors.py
pkg/command/operator.py
pkg/command/operators/__init__.py
pkg/command/operators/cmd.py
pkg/command/operators/delc.py
pkg/command/operators/func.py
pkg/command/operators/help.py
pkg/command/operators/last.py
pkg/command/operators/list.py
pkg/command/operators/next.py
pkg/command/operators/plugin.py
pkg/command/operators/prompt.py
pkg/command/operators/resend.py
pkg/command/operators/reset.py
pkg/command/operators/update.py
pkg/command/operators/version.py
pkg/config/__init__.py
pkg/config/manager.py
pkg/config/model.py
pkg/config/impls/__init__.py
pkg/config/impls/json.py
pkg/config/impls/pymodule.py
pkg/config/impls/yaml.py
pkg/core/__init__.py
pkg/core/app.py
pkg/core/boot.py
pkg/core/entities.py
pkg/core/migration.py
pkg/core/note.py
pkg/core/stage.py
pkg/core/taskmgr.py
pkg/core/bootutils/__init__.py
pkg/core/bootutils/config.py
pkg/core/bootutils/deps.py
pkg/core/bootutils/files.py
pkg/core/bootutils/log.py
pkg/core/migrations/__init__.py
pkg/core/migrations/m001_sensitive_word_migration.py
pkg/core/migrations/m002_openai_config_migration.py
pkg/core/migrations/m003_anthropic_requester_cfg_completion.py
pkg/core/migrations/m004_moonshot_cfg_completion.py
pkg/core/migrations/m005_deepseek_cfg_completion.py
pkg/core/migrations/m006_vision_config.py
pkg/core/migrations/m007_qcg_center_url.py
pkg/core/migrations/m008_ad_fixwin_config_migrate.py
pkg/core/migrations/m009_msg_truncator_cfg.py
pkg/core/migrations/m010_ollama_requester_config.py
pkg/core/migrations/m011_command_prefix_config.py
pkg/core/migrations/m012_runner_config.py
pkg/core/migrations/m013_http_api_config.py
pkg/core/migrations/m014_force_delay_config.py
pkg/core/migrations/m015_gitee_ai_config.py
pkg/core/migrations/m016_dify_service_api.py
pkg/core/migrations/m017_dify_api_timeout_params.py
pkg/core/migrations/m018_xai_config.py
pkg/core/migrations/m019_zhipuai_config.py
pkg/core/migrations/m020_wecom_config.py
pkg/core/migrations/m021_lark_config.py
pkg/core/migrations/m022_lmstudio_config.py
pkg/core/migrations/m023_siliconflow_config.py
pkg/core/migrations/m024_discord_config.py
pkg/core/migrations/m025_gewechat_config.py
pkg/core/migrations/m026_qqofficial_config.py
pkg/core/migrations/m027_wx_official_account_config.py
pkg/core/migrations/m028_aliyun_requester_config.py
pkg/core/migrations/m029_dashscope_app_api_config.py
pkg/core/migrations/m030_lark_config_cmpl.py
pkg/core/migrations/m031_dingtalk_config.py
pkg/core/migrations/m032_volcark_config.py
pkg/core/migrations/m033_dify_thinking_config.py
pkg/core/migrations/m034_gewechat_file_url_config.py
pkg/core/migrations/m035_wxoa_mode.py
pkg/core/migrations/m036_wxoa_loading_message.py
pkg/core/migrations/m037_mcp_config.py
pkg/core/migrations/m038_tg_dingtalk_markdown.py
pkg/core/migrations/m039_modelscope_cfg_completion.py
pkg/core/migrations/m040_ppio_config.py
pkg/core/notes/__init__.py
pkg/core/notes/n001_classic_msgs.py
pkg/core/notes/n002_selection_mode_on_windows.py
pkg/core/notes/n003_print_version.py
pkg/core/stages/__init__.py
pkg/core/stages/build_app.py
pkg/core/stages/genkeys.py
pkg/core/stages/load_config.py
pkg/core/stages/migrate.py
pkg/core/stages/setup_logger.py
pkg/core/stages/show_notes.py
pkg/discover/__init__.py
pkg/discover/engine.py
pkg/entity/__init__.py
pkg/entity/persistence/__init__.py
pkg/entity/persistence/base.py
pkg/entity/persistence/bot.py
pkg/entity/persistence/metadata.py
pkg/entity/persistence/model.py
pkg/entity/persistence/pipeline.py
pkg/entity/persistence/plugin.py
pkg/entity/persistence/user.py
pkg/langbot.egg-info/PKG-INFO
pkg/langbot.egg-info/SOURCES.txt
pkg/langbot.egg-info/dependency_links.txt
pkg/langbot.egg-info/requires.txt
pkg/langbot.egg-info/top_level.txt
pkg/persistence/__init__.py
pkg/persistence/database.py
pkg/persistence/mgr.py
pkg/persistence/migration.py
pkg/persistence/databases/__init__.py
pkg/persistence/databases/sqlite.py
pkg/persistence/migrations/__init__.py
pkg/persistence/migrations/dbm001_migrate_v3_config.py
pkg/persistence/migrations/dbm002_combine_quote_msg_config.py
pkg/persistence/migrations/dbm003_n8n_config.py
pkg/pipeline/__init__.py
pkg/pipeline/controller.py
pkg/pipeline/entities.py
pkg/pipeline/pipelinemgr.py
pkg/pipeline/pool.py
pkg/pipeline/stage.py
pkg/pipeline/bansess/__init__.py
pkg/pipeline/bansess/bansess.py
pkg/pipeline/cntfilter/__init__.py
pkg/pipeline/cntfilter/cntfilter.py
pkg/pipeline/cntfilter/entities.py
pkg/pipeline/cntfilter/filter.py
pkg/pipeline/cntfilter/filters/__init__.py
pkg/pipeline/cntfilter/filters/baiduexamine.py
pkg/pipeline/cntfilter/filters/banwords.py
pkg/pipeline/cntfilter/filters/cntignore.py
pkg/pipeline/longtext/__init__.py
pkg/pipeline/longtext/longtext.py
pkg/pipeline/longtext/strategy.py
pkg/pipeline/longtext/strategies/__init__.py
pkg/pipeline/longtext/strategies/forward.py
pkg/pipeline/longtext/strategies/image.py
pkg/pipeline/msgtrun/__init__.py
pkg/pipeline/msgtrun/msgtrun.py
pkg/pipeline/msgtrun/truncator.py
pkg/pipeline/msgtrun/truncators/__init__.py
pkg/pipeline/msgtrun/truncators/round.py
pkg/pipeline/preproc/__init__.py
pkg/pipeline/preproc/preproc.py
pkg/pipeline/process/__init__.py
pkg/pipeline/process/handler.py
pkg/pipeline/process/process.py
pkg/pipeline/process/handlers/__init__.py
pkg/pipeline/process/handlers/chat.py
pkg/pipeline/process/handlers/command.py
pkg/pipeline/ratelimit/__init__.py
pkg/pipeline/ratelimit/algo.py
pkg/pipeline/ratelimit/ratelimit.py
pkg/pipeline/ratelimit/algos/__init__.py
pkg/pipeline/ratelimit/algos/fixedwin.py
pkg/pipeline/respback/__init__.py
pkg/pipeline/respback/respback.py
pkg/pipeline/resprule/__init__.py
pkg/pipeline/resprule/entities.py
pkg/pipeline/resprule/resprule.py
pkg/pipeline/resprule/rule.py
pkg/pipeline/resprule/rules/__init__.py
pkg/pipeline/resprule/rules/atbot.py
pkg/pipeline/resprule/rules/prefix.py
pkg/pipeline/resprule/rules/random.py
pkg/pipeline/resprule/rules/regexp.py
pkg/pipeline/wrapper/__init__.py
pkg/pipeline/wrapper/wrapper.py
pkg/platform/__init__.py
pkg/platform/adapter.py
pkg/platform/botmgr.py
pkg/platform/logger.py
pkg/platform/sources/__init__.py
pkg/platform/sources/aiocqhttp.py
pkg/platform/sources/dingtalk.py
pkg/platform/sources/discord.py
pkg/platform/sources/gewechat.py
pkg/platform/sources/lark.py
pkg/platform/sources/nakuru.py
pkg/platform/sources/officialaccount.py
pkg/platform/sources/qqbotpy.py
pkg/platform/sources/qqofficial.py
pkg/platform/sources/slack.py
pkg/platform/sources/telegram.py
pkg/platform/sources/webchat.py
pkg/platform/sources/wechatpad.py
pkg/platform/sources/wecom.py
pkg/platform/sources/wecomcs.py
pkg/platform/types/__init__.py
pkg/platform/types/base.py
pkg/platform/types/entities.py
pkg/platform/types/events.py
pkg/platform/types/message.py
pkg/plugin/__init__.py
pkg/plugin/context.py
pkg/plugin/errors.py
pkg/plugin/events.py
pkg/plugin/host.py
pkg/plugin/installer.py
pkg/plugin/loader.py
pkg/plugin/manager.py
pkg/plugin/models.py
pkg/plugin/installers/__init__.py
pkg/plugin/installers/github.py
pkg/plugin/loaders/__init__.py
pkg/plugin/loaders/classic.py
pkg/plugin/loaders/manifest.py
pkg/processors/llm_preprocessor.py
pkg/provider/__init__.py
pkg/provider/entities.py
pkg/provider/runner.py
pkg/provider/modelmgr/__init__.py
pkg/provider/modelmgr/entities.py
pkg/provider/modelmgr/errors.py
pkg/provider/modelmgr/modelmgr.py
pkg/provider/modelmgr/requester.py
pkg/provider/modelmgr/token.py
pkg/provider/modelmgr/requesters/__init__.py
pkg/provider/modelmgr/requesters/anthropicmsgs.py
pkg/provider/modelmgr/requesters/bailianchatcmpl.py
pkg/provider/modelmgr/requesters/chatcmpl.py
pkg/provider/modelmgr/requesters/deepseekchatcmpl.py
pkg/provider/modelmgr/requesters/geminichatcmpl.py
pkg/provider/modelmgr/requesters/giteeaichatcmpl.py
pkg/provider/modelmgr/requesters/lmstudiochatcmpl.py
pkg/provider/modelmgr/requesters/modelscopechatcmpl.py
pkg/provider/modelmgr/requesters/moonshotchatcmpl.py
pkg/provider/modelmgr/requesters/ollamachat.py
pkg/provider/modelmgr/requesters/openrouterchatcmpl.py
pkg/provider/modelmgr/requesters/ppiochatcmpl.py
pkg/provider/modelmgr/requesters/siliconflowchatcmpl.py
pkg/provider/modelmgr/requesters/volcarkchatcmpl.py
pkg/provider/modelmgr/requesters/xaichatcmpl.py
pkg/provider/modelmgr/requesters/zhipuaichatcmpl.py
pkg/provider/runners/__init__.py
pkg/provider/runners/dashscopeapi.py
pkg/provider/runners/difysvapi.py
pkg/provider/runners/localagent.py
pkg/provider/runners/n8nsvapi.py
pkg/provider/session/__init__.py
pkg/provider/session/sessionmgr.py
pkg/provider/tools/__init__.py
pkg/provider/tools/entities.py
pkg/provider/tools/loader.py
pkg/provider/tools/toolmgr.py
pkg/provider/tools/loaders/__init__.py
pkg/provider/tools/loaders/mcp.py
pkg/provider/tools/loaders/plugin.py
pkg/routers/llm_router.py
pkg/services/wechat_comfyui_service.py
pkg/storage/__init__.py
pkg/storage/mgr.py
pkg/storage/provider.py
pkg/storage/providers/__init__.py
pkg/storage/providers/localstorage.py
pkg/utils/__init__.py
pkg/utils/announce.py
pkg/utils/constants.py
pkg/utils/funcschema.py
pkg/utils/image.py
pkg/utils/importutil.py
pkg/utils/logcache.py
pkg/utils/pkgmgr.py
pkg/utils/platform.py
pkg/utils/proxy.py
pkg/utils/version.py
pkg/workers/comfyui_worker.py